---
description: 
globs: 
alwaysApply: true
---
# CLAUDE.md

Always respond in Português Brasil.

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

O **Promokit/MeuCardápio** um sistema de cardápio digital para restaurantes brasileiros com integração de WhatsApp, apps mobile e extensões Chrome. O sistema serve múltiplos restaurantes em uma arquitetura de multi-tenant.

## Development Commands

### Start Development Environment
```bash
# Start all services (admin + store + TypeScript compilation + file watching)
npm run start

# Admin panel only (port 4200)
npm run startAdmin

# Digital menu store only (port 4201) 
npm run startLoja

# TypeScript compilation with watch
npm run startTsc

# File watching/copying (Grunt)
npm run copiar
```

### Production Build
```bash
npm run build              # Angular production build
npm run compilar          # TypeScript compilation
```

### Individual Services
```bash
npm run startAngular      # Admin Angular dev server (port 4200)
npm run startAngularLoja  # Store Angular dev server (port 4201)
```

## Architecture & Technology Stack

### Backend Architecture Pattern
The system follows a consistent 4-layer architecture:
```
Express Routes → Mappers → Domain Objects → MyBatis XML Mappings → Database
```

1. **Routes** (`server/routes/`): Express.js controllers
2. **Mappers** (`server/mapeadores/`): Business logic extending `MapeadorBasico`
3. **Domain Objects** (`server/domain/`): TypeScript entity classes
4. **XML Mappings** (`server/mapeamentos/`): MyBatis SQL mappings

### Frontend Architecture
- **Two Angular 14 projects**: `sorteieme-js` (admin) and `loja` (customer-facing)
- **UI Framework**: Kendo UI Angular + Bootstrap 4
- **Forms**: Template-driven forms (not reactive)
- **Services**: Extend `ServerService` for API communication
- **Styling**: SCSS files (never inline CSS)


### Routes
As respostas das rotas express são sempre Resposta.sucesso(objeto) ou Resposta.erro(mensagem).
Nas rotas express vc pega empresa usando req.empresa

Exemplo de rota express XXXController:

import {Router} from "express";

const router: Router = Router();

//router.post, router.get de acordo com as necessidades
export const XXXController: Router = router;


### Technology Stack
- **Backend**: Node.js, Express.js, TypeScript, MyBatis
- **Frontend**: Angular 14, Kendo UI, Bootstrap 4
- **Database**: MySQL with MyBatis ORM
- **Mobile**: Flutter/Dart with Firebase integration
- **Real-time**: Socket.io
- **Caching**: Redis with custom store

## Key Development Rules

### Always Respond in Portuguese Brazil
All communication should be in Portuguese Brazil.

### Planning First
Always plan tasks before implementation and get confirmation before making changes.

### Mapper Pattern Usage
All mappers inherit from `MapeadorBasico` which provides standard methods:
- `obtenhaIdEmpresaLogada()`, `possuiEmpresaLogada()`
- `existe()`, `existeSync()`, `selecioneSync()`, `listeAsync()`
- `insiraSync()`, `atualizeSync()`, `salveSync()`, `removaAsync()`
- `transacao()`, `contexto()`

Never expose `gerenciadorDeMapeamentos` outside mappers - always use mapper methods.

### Database Query Rules
- Always use `SELECT *` (never aliases): `select * from empresa` not `select * from empresa e`
- Use MyBatis `<if>` syntax for conditional queries
- Foreign key naming: `empresa_{{entidade}}_id`
- The `empresa_id` context is automatically provided (multi-tenant)

### Angular Development Rules
- **Template-driven forms** only (not reactive forms)
- **Kendo UI controls** for all UI components
- **Bootstrap 4** styling with SCSS files
- **Never inline CSS** - always use component SCSS files
- **Services extend ServerService** and return promises
- **Extensive validation** on all form fields

### Express Route Pattern
```typescript
import {Router} from "express";
const router: Router = Router();
// Define routes...
export const XXXController: Router = router;
```

Routes always return `Resposta.sucesso(objeto)` or `Resposta.erro(mensagem)`.
Access company context via `req.empresa`.

### Service Pattern
```typescript
insiraX() {
   return this.facaPost();
}

// Usage:
this.service.insiraX().then((resposta: any) => {
   // Handle success
});
```

## Project Structure

### Main Components
- **`Servidor/`**: Backend and two Angular frontends
  - Admin panel: `sorteieme-js` 
  - Digital menu: `loja`
- **`MeuCardapioScriptsApp/`**: Flutter mobile apps for different restaurant brands
- **`WhatsappChrome2/` & `WhatsappChromeV3/`**: Chrome extensions for WhatsApp Business
- **`certificados/`**: SSL certificates and app signing keys

### File Organization Example
For a new entity like "Cidade":
- Route: `server/routes/cidade.ts`
- Mapper: `server/mapeadores/MapeadorDeCidade.ts`
- Domain: `server/domain/Cidade.ts`
- Mapping: `server/mapeamentos/Cidade.xml`

### Mapeador Pattern
Nos arquivos de rotas o mapeador deve ser declarado dentro de cada método.

Se você for usar um Mapeador ele tem sempre os seguintes métodos:
obtenhaIdEmpresaLogada(): string | null;
possuiEmpresaLogada(): boolean;
desativeMultiCliente(): void;
existe(query: any, cb: (exists: boolean) => void): void;
existeSync(query: any): Promise<boolean>;
contexto(): any;
selecioneTotal(query: any): Promise<any>;
selecioneJaUsou(idCupom: any): Promise<boolean>;
selecioneSync(query: any): Promise<any>;
listeAsync(query: any): Promise<any>;
insiraGraph(obj: any): Promise<any>;
insiraSync(obj: any): Promise<any>;
atualizeSync(obj: any): Promise<any>;
salveSync(obj: any): Promise<any>;
removaAsync(obj: any): Promise<any>;
cancele(obj: any, cb: () => void): void;
metodo(nome: string): string;
transacao(cb: any): Promise<any>;

Para lista objetos vc deve usar sempre o selecione no arquivo de mapeamento xml.
Nos select sempre use * antes do from.
Prefix="true" nos mapeamentos faz o mysql retornar as colunas com o nome {{nometabela}}_{{nome_coluna}}

Ao criar um mapeador novo você não precisa criar esses métodos só herdar de MapeadorBasico.
Em geral, você não precisa sobreescrever os métodos acima, a não ser para adicionar algum comportamento extra.

Ao criar um mapeador novo vc não precisa criar esses métodos só herdar de MapeadorBasico.

## Business Domains

### Core Features
- Digital menu system with categories and products
- Order management with delivery and payment processing
- Loyalty programs with points and rewards
- WhatsApp Business integration for customer service
- Multi-tenant architecture supporting multiple restaurant chains
- Mobile apps with brand-specific customization

### Payment Integration
Support for multiple Brazilian payment gateways:
- PagSeguro, Cielo, MercadoPago, Iugu
- PIX, credit cards, and digital wallets

### Multi-tenant Architecture
- Automatic `empresa_id` context injection
- Brand-specific mobile apps and configurations
- Isolated data per restaurant chain

### Mapping

Nas consultas dos mapeamentos nunca use alias nas consultas
faça
select * from  empresa;
ao invés de select * from empresa e;

### Tables

Todas mudanças de sql devem ser feitas no arquivo sql/createl.sql
Formato das tabelas. O nome das colunas de foreign keys são empresa {{nome_entidade}}_id

Exemplo de tabela:
CREATE TABLE notificacao_mesa (
id bigint NOT NULL AUTO_INCREMENT,
origem varchar(255)  NOT NULL,
comando varchar(255)  NOT NULL,
operacao varchar(255)  NOT NULL,
numero varchar(255)  DEFAULT NULL,
horario datetime NOT NULL,
horario_notificado datetime NOT NULL,
dados longtext ,
executada bit(1) DEFAULT NULL,
erro varchar(255)  DEFAULT NULL,
ignorar bit(1) DEFAULT NULL,
empresa_id  bigint not null,
comanda_id  bigint not null,
PRIMARY KEY (id),
CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
CONSTRAINT   FOREIGN KEY (comanda_id) REFERENCES comanda (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

## UI/UX

Quando te solicitar a criar uma nova tela adicione o máximo de validações possível em cada campo.

Nunca escreva estilo css no html dos componentes angular, sempre coloque os estilos css nos arquivos scss.
Tente fazer telas sempre bonitas e modernas, mas sem gradientes e efeitos hover.

## Important Notes

- **Production System**: Never delete existing files, only modify
- **No Module Creation**: Don't create Angular modules unless explicitly requested
- **Maximum Validations**: Add extensive validation to all form fields when creating new screens
- **Portuguese Locale**: System uses Portuguese Brazil throughout
- **Bootstrap 4**: All styling should be compatible with Bootstrap 4

---
description:
globs:
alwaysApply: true
---


desenvolvido em Nodejs, Angular, Kendo UI Angular e bootstrap 4. Sempre observe os arquivos anexos para ver a forma como as telas são feitas.
A persistencia é feita usando mybatisnodejs através de arquivos de mapeamentos Mybatis.
A arquitetura do sistema é organizanda usando express e em geral temos:
- rota (express)
- mapeador
- objeto domain
- arquivo de mapamento mybatis
- telas em angular sempre usando o modo template-driven forms e sempre usar controles kendo-ui-angular.

No arquivo de Mapeamento todas as listagens e selecao de objetos sao feitos na consulta com id selecione e devem usar <if da sintaxe do mybatis

Nos mapeadores o parametro idEmpresa vem automaticamente do contexto, não é necessário passar nas chamadas de funções nem no construtor.

Exemplo:
- rota: cidade.ts
- mapeador: server/mapeadores/MapeadorDeCidade.ts
- objeto domain: server/domain/Cidade.ts
- mapeamento: server/mapeamentos/Cidade.xml

----

O site inteiro usa boostrap 4, então leve isso em consideração ao fazer o scss e as estilizações.
Sempre use cores sólidas nos botões.
NUnca adicione efeitos nas páginas gerada se eu não pedir explicitamente.

Você nunca deve criar módulos a não ser que eu peça explicitamente.

O services angular sempre herdam de ServerService e retorno dos métodos de busca sempre são os dados, se a resposta falha (sucesso false) ele já dispara um Error.

o post é feito chamando exemplo:

insiraX() {
return this.facaPost();
}

e a chamada retorna uma promise que vc pode tratar com then ou catch

this.service.insiraX().then((resposta: any) => {
});


----


----
Todo mapeador header de MapeadorBasico e o mapemanto é no arquivo xml mybatis. No construtor do Mapeador vc passa o nome do mapeamento.
constructor() {
super('nome_mapeamento');
}
Todo código dos métodos dos mapeadores usam o this.gerenciadorDeMapeamentos para executar comandos no banco de dados.

Você só pode chamar o mapeador via funcoes jamais exponhar o gerenciadorDeMapeamentos em outros arquivos como rotas.
---

------

Nunca execute o projeto, deixa que eu cuido dessa parte.

O sistema já está em produção então não saia deletando arqquivo existens, apenas altere.

Só crie novos módulos angular se eu pedir explicitamente no prompt.



