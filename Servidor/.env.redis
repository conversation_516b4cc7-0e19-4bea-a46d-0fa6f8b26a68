# 🔒 Configurações Redis para Locks Distribuídos

# Host do Redis
REDIS_HOST=localhost

# Porta do Redis
REDIS_PORT=6379

# Senha do Redis (se necessário)
# REDIS_PASSWORD=sua_senha_aqui

# Database do Redis para locks
REDIS_DB=0

# Configurações de TTL para locks (em milissegundos)
REDIS_LOCK_TTL_JOB=300000        # 5 minutos para jobs principais
REDIS_LOCK_TTL_TAREFA=30000      # 30 segundos para tarefas individuais

# Configurações de retry
REDIS_LOCK_RETRY_COUNT=3
REDIS_LOCK_RETRY_DELAY=200
REDIS_LOCK_RETRY_JITTER=200

# Configurações de conexão
REDIS_RETRY_DELAY_ON_FAILOVER=100
REDIS_ENABLE_OFFLINE_QUEUE=false
