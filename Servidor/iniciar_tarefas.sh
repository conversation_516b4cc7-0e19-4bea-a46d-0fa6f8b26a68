. ~/.nvm/nvm.sh
export NODE_ENV=production
nvm use 12

export NODE_OPTIONS=--max-old-space-size=4192

tsc --p server

grunt copy
echo $PWD

# 🔒 DEPLOY SEGURO - Evita duplicação de instâncias

echo "🛑 Parando todas as instâncias de tarefas..."
pm2 stop tarefas 2>/dev/null || true
pm2 delete tarefas 2>/dev/null || true

# Aguardar para garantir que parou
sleep 2

echo "🧹 Removendo locks órfãos..."
rm -f tarefas.lock 2>/dev/null || true

echo "🔍 Verificando processos órfãos..."
ORPHAN_PIDS=$(ps aux | grep "distServer/bin/tarefas.js" | grep -v grep | awk '{print $2}' || true)
if [ ! -z "$ORPHAN_PIDS" ]; then
    echo "⚠️ Terminando processos órfãos: $ORPHAN_PIDS"
    echo $ORPHAN_PIDS | xargs kill -9 2>/dev/null || true
    sleep 1
fi

echo "✅ Iniciando instância única usando ecosystem.config.js..."
pm2 start ecosystem.config.js --only tarefas

echo "📊 Status atual do PM2:"
pm2 list | grep tarefas

echo "Finalizando..."
