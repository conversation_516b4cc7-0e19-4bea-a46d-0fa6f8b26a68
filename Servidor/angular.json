{
  "$schema": "./node_modules/@angular/cli/lib/config/schema.json",
  "version": 1,
  "newProjectRoot": "projects",
  "projects": {
    "sorteieme-js": {
      "root": "",
      "sourceRoot": "src",
      "projectType": "application",
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "aot": false,
            "outputPath": "dist",
            "localize": false,
            "index": "src/index.html",
            "main": "src/main.ts",
            "tsConfig": "src/tsconfig.app.json",
            "polyfills": "src/polyfills.ts",
            "assets": [
              "src/assets",
              "src/favicon.ico",
              "src/indexadmin.html",
              "src/manifest.webmanifest"
            ],
            "styles": [
              "node_modules/@angular/material/prebuilt-themes/deeppurple-amber.css",
              "node_modules/leaflet/dist/leaflet.css",
              "src/styles.css",
              "src/assets/template/css/bootstrap.css",
              "src/assets/template/css/icons.css",
              "src/assets/template/css/app.css",
              "node_modules/@progress/kendo-theme-default/dist/all.css",
              "src/assets/template/css/custom.css"
            ],
            "scripts": [
              "src/assets/js/qz-tray.js",
              "src/assets/template/js/vendor.min.js",
              "src/assets/template/js/app.min.js",
              "node_modules/marked/marked.min.js",
              "node_modules/underscore/underscore-min.js",
              "node_modules/chart.js/dist/Chart.js",
              "node_modules/moment/min/moment.min.js",
              "node_modules/moment/min/locales.min.js",
              "node_modules/leaflet/dist/leaflet.js",
              "src/assets/leaflet/js/L.KML.js",
              "node_modules/pluralize/pluralize.js"
            ],
            "vendorChunk": true,
            "extractLicenses": false,
            "buildOptimizer": false,
            "sourceMap": {
              "styles": false
            },
            "optimization": false,
            "namedChunks": true
          },
          "configurations": {
            "pt": {
              "localize": ["pt"]
            },
            "development": {
              "sourceMap": true
            },
            "production": {
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "2mb",
                  "maximumError": "12.4mb"
                  "maximumError": "12.5mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "6kb"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "namedChunks": false,
              "aot": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "fileReplacements": [
                {
                  "replace": "src/environments/environment.ts",
                  "with": "src/environments/environment.prod.ts"
                }
              ],
              "serviceWorker": true,
              "ngswConfigPath": "ngsw-config.json"
            }
          },
          "defaultConfiguration": ""
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "options": {
            "proxyConfig": "src/proxy.conf.json",
            "disableHostCheck": false
          },
          "configurations": {
            "development": {
              "browserTarget": "sorteieme-js:build:development"
            },
            "pt": {
              "browserTarget": "sorteieme-js:build:pt"
            },
            "production": {
              "browserTarget": "sorteieme-js:build:production"
            }
          },
          "defaultConfiguration": "development"
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n",
          "options": {
            "browserTarget": "sorteieme-js:build"
          }
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "main": "src/test.ts",
            "karmaConfig": "./karma.conf.js",
            "polyfills": "src/polyfills.ts",
            "tsConfig": "src/tsconfig.spec.json",
            "scripts": [
              "node_modules/marked/marked.min.js",
              "node_modules/underscore/underscore-min.js",
              "node_modules/chart.js/dist/Chart.js",
              "node_modules/moment/min/moment.min.js",
              "node_modules/moment/min/locales.min.js"
            ],
            "styles": [
              {
                "input": "node_modules/@progress/kendo-theme-default/dist/all.css",
                "inject": true
              },
              "src/styles.css"
            ],
            "assets": [
            ]
          }
        }
      },
      "i18n": {
        "sourceLocale": "en-US",
        "locales": {
          "pt": "src/locale/messages.pt.xlf"
        }
      }
    },
    "sorteieme-js-e2e": {
      "root": "e2e",
      "sourceRoot": "e2e",
      "projectType": "application",
      "architect": {
        "e2e": {
          "builder": "@angular-devkit/build-angular:protractor",
          "options": {
            "protractorConfig": "./protractor.conf.js",
            "devServerTarget": "sorteieme-js:serve"
          }
        }
      }
    },
    "loja": {
      "projectType": "application",
      "schematics": {
        "@schematics/angular:component": {
          "style": "scss"
        }
      },
      "root": "projects/loja",
      "sourceRoot": "projects/loja/src",
      "prefix": "app",
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/_loja",
            "localize": false,
            "index": "projects/loja/src/index.html",
            "main": "projects/loja/src/main.ts",
            "polyfills": "projects/loja/src/polyfills.ts",
            "tsConfig": "projects/loja/tsconfig.app.json",
            "aot": false,
            "assets": [
              "projects/loja/src/favicon.ico",
              "projects/loja/src/assets",
              {
                "glob": "*",
                "input": "projects/loja/src/assets/fonts",
                "output": "."
              },
              "projects/loja/src/lojamanifest.webmanifest"
            ],
            "styles": [
              "projects/loja/src/styles.scss",
              "node_modules/leaflet/dist/leaflet.css",
              "projects/loja/src/assets/css/bootstrap.css",
              "projects/loja/src/assets/css/icons.css",
              "node_modules/@progress/kendo-theme-default/dist/all.css",
              "projects/loja/src/assets/css/app.css",
              "projects/loja/src/assets/css/custom.css"
            ],
            "scripts": [
              "node_modules/underscore/underscore-min.js",
              "node_modules/leaflet/dist/leaflet.js",
              "src/assets/template/js/vendor.min.js",
              "src/assets/template/js/app.min.js"
            ],
            "vendorChunk": true,
            "extractLicenses": false,
            "buildOptimizer": false,
            "sourceMap": true,
            "optimization": false,
            "namedChunks": true
          },
          "configurations": {
            "pt": {
              "localize": ["pt"]
            },
            "production": {
              "fileReplacements": [
                {
                  "replace": "projects/loja/src/environments/environment.ts",
                  "with": "projects/loja/src/environments/environment.prod.ts"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "2mb",
                  "maximumError": "5.4mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "6kb",
                  "maximumError": "40kb"
                }
              ],
              "serviceWorker": true,
              "ngswConfigPath": "projects/loja/ngsw-config.json"
            }
          },
          "defaultConfiguration": ""
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "options": {
            "browserTarget": "loja:build",
            "proxyConfig": "src/proxy.conf.json",
            "disableHostCheck": false
          },
          "configurations": {
            "pt": {
              "browserTarget": "loja:build:pt"
            },
            "production": {
              "browserTarget": "loja:build:production"
            }
          }
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n",
          "options": {
            "browserTarget": "loja:build",
            "ivy": true
          }
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "main": "projects/loja/src/test.ts",
            "polyfills": "projects/loja/src/polyfills.ts",
            "tsConfig": "projects/loja/tsconfig.spec.json",
            "karmaConfig": "projects/loja/karma.conf.js",
            "assets": [
              "projects/loja/src/favicon.ico",
              "projects/loja/src/assets",
              "projects/loja/src/lojamanifest.webmanifest"
            ],
            "styles": [
              "projects/loja/src/styles.scss"
            ],
            "scripts": [
              "node_modules/underscore/underscore-min.js",
              "node_modules/chart.js/dist/Chart.js",
              "node_modules/moment/min/moment.min.js",
              "node_modules/moment/min/locales.min.js"
            ],
            "scripts": []
          }
        },
        "e2e": {
          "builder": "@angular-devkit/build-angular:protractor",
          "options": {
            "protractorConfig": "projects/loja/e2e/protractor.conf.js",
            "devServerTarget": "loja:serve"
          },
          "configurations": {
            "production": {
              "devServerTarget": "loja:serve:production"
            }
          }
        }
      },
      "i18n": {
        "sourceLocale": "en-US",
        "locales": {
          "pt": "src/locale/messages.pt.xlf"
        }
      }
    }
  },
  "schematics": {
    "@schematics/angular:component": {
      "prefix": "app",
      "style": "scss"
    },
    "@schematics/angular:directive": {
      "prefix": "app"
    }
  },
  "cli": {
    "analytics": false
  }
}
