# 🔧 Comandos PM2 para Gerenciar Tarefas

## 🔍 Verificar Status
```bash
# Ver todas as instâncias
pm2 list

# Ver apenas tarefas
pm2 list | grep tarefas

# Ver detalhes da instância
pm2 show tarefas

# Ver logs em tempo real
pm2 logs tarefas

# Ver logs das últimas 50 linhas
pm2 logs tarefas --lines 50
```

## 🛑 Parar Duplicações
```bash
# Parar todas as instâncias de tarefas
pm2 stop tarefas

# Deletar todas as instâncias de tarefas
pm2 delete tarefas

# Parar e deletar TUDO (cuidado!)
pm2 stop all
pm2 delete all

# Matar processos órfãos
ps aux | grep "distServer/bin/tarefas.js" | grep -v grep | awk '{print $2}' | xargs kill -9
```

## ✅ Iniciar Corretamente
```bash
# Usando ecosystem.config.js (RECOMENDADO)
pm2 start ecosystem.config.js --only tarefas

# Ou usando comando direto
pm2 start distServer/bin/tarefas.js --name tarefas -i 1

# Reiniciar instância existente
pm2 restart tarefas

# Reload (zero downtime)
pm2 reload tarefas
```

## 🔍 Monitoramento
```bash
# Monitor em tempo real
pm2 monit

# Status detalhado
pm2 status

# Informações do sistema
pm2 info tarefas

# Logs de erro
pm2 logs tarefas --err

# Logs de saída
pm2 logs tarefas --out
```

## 🧹 Limpeza
```bash
# Limpar logs
pm2 flush

# Limpar logs específicos
pm2 flush tarefas

# Remover processos parados
pm2 delete all

# Resetar contadores
pm2 reset tarefas
```

## 🚨 Emergência - Parar Tudo
```bash
# Se houver muitas duplicações
pm2 kill
pm2 resurrect

# Ou forçar parada
sudo pkill -f "distServer/bin/tarefas.js"
```
