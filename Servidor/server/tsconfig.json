{"compilerOptions": {"module": "commonjs", "target": "es2017", "noImplicitAny": true, "moduleResolution": "node", "sourceMap": true, "outDir": "../distServer", "baseUrl": ".", "experimentalDecorators": true, "resolveJsonModule": true, "paths": {"*": ["node_modules/*"]}, "typeRoots": ["./typings"], "lib": ["dom", "es2017", "scripthost", "es2015.promise"], "skipLibCheck": true}, "include": ["./routes/*.ts", "./domain/*.ts", "./testes/*.ts", "./mapeadores/*.ts", "./bin/*.ts", "public"], "exclude": ["../**/WhatsappAPI"]}