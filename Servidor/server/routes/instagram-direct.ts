 import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {InstagramDirectService} from "../service/InstagramDirectService";
import {DadosInstagram} from "../domain/instagram/DadosInstagram";
import {MapeadorDeDadosInstagram} from "../mapeadores/MapeadorDeDadosInstagram";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {ConstrutorDeRespostaIG} from "../lib/instagram/ConstrutorDeRespostaIG";
import {TratadorDeMensagemWitai} from "../lib/instagram/TratadorDeMensagemWitai";
import * as crypto from 'crypto';
const axios = require('axios');

const router: Router = Router();

/**
 * Obtém URL de autorização para Instagram Basic Display API
 */
router.get('/urlAutorizacao', async (req: any, res: any) => {
  try {
    const instagramService = InstagramDirectService.Instance;

    // Extrair URL base do request
    const protocol = 'https';
    const host = req.get('host') || req.get('Host');
    const baseUrl = `${protocol}://${host}`;

    const urlAutorizacao = instagramService.obtenhaUrlAutorizacao(baseUrl);

    res.json(Resposta.sucesso({ url: urlAutorizacao }));
  } catch (error: any) {
    console.error('Erro ao gerar URL de autorização:', error);
    res.json(Resposta.erro('Erro ao gerar URL de autorização'));
  }
});

/**
 * Conecta conta Instagram usando código de autorização
 */
router.post('/conectar', async (req: any, res: any) => {
  try {
    const { codigo } = req.body;
    const empresa = req.empresa;

    if (!codigo) {
      return res.json(Resposta.erro('Código de autorização não informado'));
    }

    const instagramService = InstagramDirectService.Instance;
    const mapeador = new MapeadorDeDadosInstagram();

    // 1. Trocar código por token
    console.log('Trocando código por token...');
    const resultadoToken = await instagramService.troqueCodigoPorToken(codigo);

    if (!resultadoToken.sucesso) {
      return res.json(resultadoToken);
    }

    const accessToken = resultadoToken.data.access_token;
    const userId = resultadoToken.data.user_id;

    // 2.5. Converter short-lived token em long-lived token (60 dias)
    console.log('Convertendo short-lived token para long-lived token...');
    const resultadoExtensao = await instagramService.estenderToken(accessToken);

    if (!resultadoExtensao.sucesso) {
      console.error('[Instagram Direct] Erro ao converter para long-lived token:', resultadoExtensao.erro);
      return res.json(Resposta.erro('Erro ao converter token para longa duração: ' + resultadoExtensao.erro));
    }

    // Usar o long-lived token daqui em diante
    const longLivedToken = resultadoExtensao.data.access_token;
    const expiresIn = resultadoExtensao.data.expires_in;

    console.log('[Instagram Direct] Token convertido com sucesso!');
    console.log('[Instagram Direct] Expira em:', expiresIn, 'segundos (', Math.floor(expiresIn / 86400), 'dias)');

    // 2. Obter dados do perfil
    console.log('Obtendo dados do perfil...');
    const resultadoPerfil = await instagramService.obtenhaPerfilUsuario(longLivedToken, userId);

    let perfilData;
    let userIdCorreto;
    if (!resultadoPerfil.sucesso) {
      // Fallback: usar dados básicos se não conseguir obter o perfil
      console.warn('[Instagram Direct] Não foi possível obter o perfil, usando dados básicos');
      perfilData = {
        id: userId,
        username: 'Instagram_User_' + userId.substring(0, 8),
        account_type: 'PERSONAL'
      };
      userIdCorreto = userId;
    } else {
      perfilData = resultadoPerfil.data;
      userIdCorreto = perfilData.id; // Usar o ID que vem do perfil, não do request inicial
      console.log('[Instagram Direct] User ID corrigido do perfil:', userIdCorreto);
    }

    // 4. Verificar se já existe integração Instagram Direct para esta empresa
    let dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    // 5. Criar ou atualizar dados
    if (!dadosInstagram) {
      dadosInstagram = new DadosInstagram();
      dadosInstagram.empresa = empresa;
      dadosInstagram.tipoIntegracao = 'DIRECT';
    }

    dadosInstagram.nomeInstagram = perfilData.username;
    dadosInstagram.userIdInsta = userIdCorreto; // Usar o ID correto do perfil
    dadosInstagram.accessToken = longLivedToken;
    dadosInstagram.dataExpiracao = instagramService.calculeDataExpiracao();
    dadosInstagram.ativo = true;
    dadosInstagram.dataCriacao = dadosInstagram.dataCriacao || new Date();

    console.log('[Instagram Direct] Data de expiração configurada para:', dadosInstagram.dataExpiracao);

    // Limpar campos específicos do Facebook
    dadosInstagram.idPaginaFace = null;
    dadosInstagram.accessTokenPagina = null;

    // 6. Salvar no banco
    if (!dadosInstagram.id) {
      await mapeador.insiraGraph(dadosInstagram);
    } else {
      await mapeador.atualizeSync(dadosInstagram);
    }

    // 7. Habilitar webhook subscriptions (Step 3 da documentação Instagram)
    console.log('Habilitando webhook subscriptions...');
    const resultadoSubscriptions = await instagramService.habilitarSubscriptions(userIdCorreto, longLivedToken);

    if (!resultadoSubscriptions.sucesso) {
      console.warn('[Instagram Direct] Falha ao habilitar subscriptions:', resultadoSubscriptions.erro);
      // Não vamos falhar a integração por isso, apenas avisar
    } else {
      console.log('[Instagram Direct] Webhook subscriptions habilitadas com sucesso');
    }

    // 8. Configurar ice-breakers
    console.log('Configurando ice-breakers...');
    const resultadoIceBreakers = await instagramService.definaIceBreakers(longLivedToken, userIdCorreto);

    if (!resultadoIceBreakers.sucesso) {
      console.warn('[Instagram Direct] Falha ao configurar ice-breakers:', resultadoIceBreakers.erro);
      // Não vamos falhar a integração por isso, apenas avisar
    } else {
      console.log('[Instagram Direct] Ice-breakers configurados com sucesso');
    }

    console.log('Instagram Direct conectado com sucesso para empresa:', empresa.id);

    // Remover token da resposta por segurança
    const dadosResposta = { ...dadosInstagram };
    delete dadosResposta.accessToken;
    delete dadosResposta.refreshToken;

    res.json(Resposta.sucesso(dadosResposta));

  } catch (error: any) {
    console.error('Erro ao conectar Instagram Direct:', error);
    res.json(Resposta.erro('Erro interno ao conectar Instagram'));
  }
});

/**
 * Verifica se há conta Instagram Direct conectada
 */
router.get('/contaConectada', async (req: any, res: any) => {
  try {
    const mapeador = new MapeadorDeDadosInstagram();

    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (dadosInstagram) {
      // Remover tokens da resposta por segurança
      delete dadosInstagram.accessToken;
      delete dadosInstagram.refreshToken;
    }

    res.json(Resposta.sucesso(dadosInstagram));

  } catch (error: any) {
    console.error('Erro ao verificar conta conectada:', error);
    res.json(Resposta.erro('Erro ao verificar conta conectada'));
  }
});

/**
 * Desconecta conta Instagram Direct
 */
router.post('/desconectar', async (req: any, res: any) => {
  try {
    const mapeador = new MapeadorDeDadosInstagram();

    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (dadosInstagram) {
      dadosInstagram.ativo = false;
      await mapeador.atualizeSync(dadosInstagram);
    }

    res.json(Resposta.sucesso(true));

  } catch (error: any) {
    console.error('Erro ao desconectar Instagram Direct:', error);
    res.json(Resposta.erro('Erro ao desconectar conta'));
  }
});

/**
 * Renova token de acesso
 */
router.post('/renovarToken', async (req: any, res: any) => {
  try {
    const mapeador = new MapeadorDeDadosInstagram();
    const instagramService = InstagramDirectService.Instance;

    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (!dadosInstagram) {
      return res.json(Resposta.erro('Nenhuma conta Instagram Direct conectada'));
    }

    const resultadoRenovacao = await instagramService.renovarToken(dadosInstagram.accessToken);

    if (!resultadoRenovacao.sucesso) {
      return res.json(resultadoRenovacao);
    }

    // Atualizar token e data de expiração
    dadosInstagram.accessToken = resultadoRenovacao.data.access_token;
    dadosInstagram.dataExpiracao = instagramService.calculeDataExpiracao();

    await mapeador.atualizeSync(dadosInstagram);

    res.json(Resposta.sucesso({
      renovado: true,
      dataExpiracao: dadosInstagram.dataExpiracao
    }));

  } catch (error: any) {
    console.error('Erro ao renovar token:', error);
    res.json(Resposta.erro('Erro ao renovar token'));
  }
});

/**
 * Obtém dados do perfil conectado
 */
router.get('/perfil', async (req: any, res: any) => {
  try {
    const mapeador = new MapeadorDeDadosInstagram();
    const instagramService = InstagramDirectService.Instance;

    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (!dadosInstagram) {
      return res.json(Resposta.erro('Nenhuma conta Instagram Direct conectada'));
    }

    const resultadoPerfil = await instagramService.obtenhaPerfilUsuario(dadosInstagram.accessToken, dadosInstagram.userIdInsta);

    if (!resultadoPerfil.sucesso) {
      return res.json(resultadoPerfil);
    }

    res.json(Resposta.sucesso(resultadoPerfil.data));

  } catch (error: any) {
    console.error('Erro ao obter perfil:', error);
    res.json(Resposta.erro('Erro ao obter dados do perfil'));
  }
});

/**
 * Obtém mídia do usuário
 */
router.get('/midia', async (req: any, res: any) => {
  try {
    const { limit = 25 } = req.query;
    const mapeador = new MapeadorDeDadosInstagram();
    const instagramService = InstagramDirectService.Instance;

    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (!dadosInstagram) {
      return res.json(Resposta.erro('Nenhuma conta Instagram Direct conectada'));
    }

    const resultadoMidia = await instagramService.obtenhaMidiaUsuario(dadosInstagram.accessToken,
      parseInt(limit as string));

    if (!resultadoMidia.sucesso) {
      return res.json(resultadoMidia);
    }

    res.json(Resposta.sucesso(resultadoMidia.data));

  } catch (error: any) {
    console.error('Erro ao obter mídia:', error);
    res.json(Resposta.erro('Erro ao obter mídia do usuário'));
  }
});

/**
 * Testa conexão com Instagram
 */
router.get('/testarConexao', async (req: any, res: any) => {
  try {
    const mapeador = new MapeadorDeDadosInstagram();
    const instagramService = InstagramDirectService.Instance;

    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (!dadosInstagram) {
      return res.json(Resposta.erro('Nenhuma conta Instagram Direct conectada'));
    }

    const resultadoTeste = await instagramService.testeToken(dadosInstagram.accessToken);

    res.json(resultadoTeste);

  } catch (error: any) {
    console.error('Erro ao testar conexão:', error);
    res.json(Resposta.erro('Erro ao testar conexão'));
  }
});

/**
 * Verifica status das webhook subscriptions
 */
router.get('/statusSubscriptions', async (req: any, res: any) => {
  try {
    const mapeador = new MapeadorDeDadosInstagram();
    const instagramService = InstagramDirectService.Instance;

    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (!dadosInstagram) {
      return res.json(Resposta.erro('Nenhuma conta Instagram Direct conectada'));
    }

    const resultadoSubscriptions = await instagramService.verificarSubscriptions(
      dadosInstagram.userIdInsta,
      dadosInstagram.accessToken
    );

    res.json(resultadoSubscriptions);

  } catch (error: any) {
    console.error('Erro ao verificar subscriptions:', error);
    res.json(Resposta.erro('Erro ao verificar subscriptions'));
  }
});

/**
 * Habilita webhook subscriptions manualmente
 */
router.post('/habilitarSubscriptions', async (req: any, res: any) => {
  try {
    const mapeador = new MapeadorDeDadosInstagram();
    const instagramService = InstagramDirectService.Instance;

    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (!dadosInstagram) {
      return res.json(Resposta.erro('Nenhuma conta Instagram Direct conectada'));
    }

    const resultadoSubscriptions = await instagramService.habilitarSubscriptions(
      dadosInstagram.userIdInsta,
      dadosInstagram.accessToken
    );

    res.json(resultadoSubscriptions);

  } catch (error: any) {
    console.error('Erro ao habilitar subscriptions:', error);
    res.json(Resposta.erro('Erro ao habilitar subscriptions'));
  }
});

/**
 * Configura ice-breakers manualmente
 */
router.post('/configurarIceBreakers', async (req: any, res: any) => {
  try {
    const mapeador = new MapeadorDeDadosInstagram();
    const instagramService = InstagramDirectService.Instance;

    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (!dadosInstagram) {
      return res.json(Resposta.erro('Nenhuma conta Instagram Direct conectada'));
    }

    const resultadoIceBreakers = await instagramService.definaIceBreakers(
      dadosInstagram.accessToken,
      dadosInstagram.userIdInsta
    );

    res.json(resultadoIceBreakers);

  } catch (error: any) {
    console.error('Erro ao configurar ice-breakers:', error);
    res.json(Resposta.erro('Erro ao configurar ice-breakers'));
  }
});

/**
 * Webhook verification endpoint (GET)
 */
router.get('/webhook', (req: any, res: any) => {
  console.log("[Instagram Direct] Got /webhook verification");

  const mode = req.query["hub.mode"];
  const token = req.query["hub.verify_token"];
  const challenge = req.query["hub.challenge"];

  // TODO: Adicionar INSTAGRAM_WEBHOOK_VERIFY_TOKEN no config.json
  const verifyToken = '234'; // Mesmo token usado no instagram.ts por enquanto

  if (mode && token) {
    if (mode === "subscribe" && token === verifyToken) {
      console.log("[Instagram Direct] WEBHOOK_VERIFIED");
      res.status(200).send(challenge);
    } else {
      res.sendStatus(403);
    }
  } else {
    console.warn("[Instagram Direct] Got /webhook but without needed parameters.");
    res.sendStatus(400);
  }
});

/**
 * Webhook para receber mensagens (POST)
 */
router.post('/webhook', async (req: any, res: any) => {
  let body = req.body;

  console.log('[Instagram Direct] Webhook received:');
  console.dir(body, {depth: null});
  const mapeadorDeEmpresa = new MapeadorDeEmpresa();

  if (body.object === "instagram") {
    // Iterate over each entry - there may be multiple if batched
    for( let entry of body.entry ) {
      if (!("messaging" in entry)) {
        console.warn("[Instagram Direct] No messaging field in entry. Possibly a webhook test.");
        return null;
      }

      const idInstagram = entry.id;
      for( let webhookEvent of entry.messaging ) {
        let reciepientIgsid = webhookEvent.recipient.id;

        if ("message" in webhookEvent && webhookEvent.message.is_echo === true) {
          console.log("[Instagram Direct] Got an echo: " + reciepientIgsid);
          // Por enquanto, ignorar mensagens echo na API Direct
          continue;
        }

        let senderIgsid = webhookEvent.sender.id;

        const mapeador = new MapeadorDeDadosInstagram();
        mapeador.desativeMultiCliente();
        console.log('[Instagram Direct] id Instagram: ' + idInstagram);

        // Buscar pelos campos corretos dependendo do tipo de integração
        let dadosInstagram = await mapeador.selecioneSync({
          idInstagram: idInstagram,
          tipoIntegracao: 'DIRECT',
          ativo: true
        });

        // Se não encontrar, tentar buscar por userIdInsta
        if (!dadosInstagram) {
          dadosInstagram = await mapeador.selecioneSync({
            userIdInsta: idInstagram,
            tipoIntegracao: 'DIRECT',
            ativo: true
          });
        }

        if(!dadosInstagram) {
          console.error('[Instagram Direct] Nenhuma conta encontrada para id: ' + idInstagram);
          return res.status(400).json({ erro: 'Nenhuma conta Instagram Direct conectada para id: ' + idInstagram});
        }

        const queryEmpresa = {id: dadosInstagram.empresa.id};
        const empresa = await mapeadorDeEmpresa.selecioneSync(queryEmpresa);

        const respostas = await processeMensagemWebhookDirect(empresa, senderIgsid, webhookEvent);

        if (Array.isArray(respostas)) {
          let delay = 0;
          for (let resposta of respostas) {
            await enviarMensagemInstagramDirect(dadosInstagram, senderIgsid, resposta, delay * 1000);
            delay++;
          }
        } else if (respostas) {
          await enviarMensagemInstagramDirect(dadosInstagram, senderIgsid, respostas, 0);
        }
      }
    }

    res.json(Resposta.sucesso(true));
  } else {
    res.sendStatus(404);
  }
});

/**
 * Verifica assinatura SHA256 do webhook
 */
function verificarAssinaturaWebhook(req: any): boolean {
  const signature = req.headers['x-hub-signature-256'];
  if (!signature) {
    console.warn('[Instagram Direct] Webhook sem assinatura');
    return true; // Por enquanto, aceitar sem assinatura em desenvolvimento
  }

  try {
    // TODO: Usar app secret do config quando disponível
    // const appSecret = Ambiente.Instance.config.instagramDirect.appSecret;
    // const expectedSignature = crypto
    //   .createHmac('sha256', appSecret)
    //   .update(JSON.stringify(req.body))
    //   .digest('hex');

    // const receivedSignature = signature.split('sha256=')[1];

    // return crypto.timingSafeEqual(
    //   Buffer.from(expectedSignature),
    //   Buffer.from(receivedSignature)
    // );

    return true; // Temporariamente aceitar todos
  } catch (error) {
    console.error('[Instagram Direct] Erro ao verificar assinatura:', error);
    return false;
  }
}

/**
 * Processar mensagem webhook - similar ao instagram.ts mas para Direct API
 */
async function processeMensagemWebhookDirect(empresa: any, igId: string, webhookEvent: any): Promise<any> {
  const construtor = new ConstrutorDeRespostaIG(empresa);

  // Verifica se está no modo atendente
  const client = require('redis').createClient();

  return new Promise((resolve, reject) => {
    client.get('atendente:' + igId, async (erro: Error, valor: string) => {
      try {
        if (valor && false) {
          // Se está no modo atendente, não responde
          console.log('[Instagram Direct] Modo atendente ativo');
          resolve(null);
          return;
        }

        const event = webhookEvent;
        let responses: any = [];

        if (event.message) {
          let message = event.message;

          if (message.is_echo) {
            // Mensagens echo já são tratadas no webhook principal
            resolve(null);
            return;
          } else if (message.quick_reply) {
            // Processa quick reply
            let payload = message.quick_reply.payload;
            responses = await processarPayload(construtor, payload, igId);
          } else if (message.attachments) {
            // Por enquanto não processa anexos
            responses = await construtor.construaResposta('nao_entendi', { igId });
          } else if (message.text) {
            // Processa mensagem de texto
            responses = await processarMensagemTexto(construtor, message.text, igId);
          }
        } else if (event.postback) {
          // Processa postback
          let payload = event.postback.payload;
          responses = await processarPayload(construtor, payload.toUpperCase(), igId);
        } else if (event.referral) {
          // Por enquanto não processa referral
          responses = await construtor.construaResposta('cumprimento', { igId });
        }

        resolve(responses);
      } catch (error) {
        console.error('[Instagram Direct] Erro ao processar mensagem:', error);
        const respostasErro = await construtor.construaResposta('nao_entendi', { igId });
        resolve(respostasErro);
      }
    });
  });
}


/**
 * Processar texto da mensagem com detecção de intenção
 */
async function processarMensagemTexto(construtor: ConstrutorDeRespostaIG, texto: string, igId: string): Promise<any[]> {
  console.log('[Instagram Direct] Processando texto:', texto);

  const intent = await new TratadorDeMensagemWitai().processeTexto(texto);

  const contextoMensagem = {
    igId: igId,
    message: texto,
    intent: intent
  };

  console.log('[Instagram Direct] Intent detectado:', intent);

  switch (intent) {
    case 'horario_atendimento':
      return await construtor.construaResposta('horario_atendimento', contextoMensagem);
    case 'fazerpedido':
      return await construtor.construaResposta('cardapio', contextoMensagem);
    case 'enviar_cardapio':
      return await construtor.construaResposta('cardapio', contextoMensagem);
    case 'fome':
      return await construtor.construaResposta('fome', contextoMensagem);
    case 'cumprimento':
      return await construtor.construaResposta('get_started', contextoMensagem);
    case 'agradecimento':
      return await construtor.construaResposta('agradecimento', contextoMensagem);
    case 'whatsapp':
      return await construtor.construaResposta('whatsapp', contextoMensagem);
    case 'taxasdeentrega':
      return await construtor.construaResposta('taxas_entrega', contextoMensagem);
    case 'endereco':
      return await construtor.construaResposta('localizacao', contextoMensagem);
    case 'atendente':
      ativarAtendente(igId);
      return await construtor.construaResposta('atendente', contextoMensagem);
    default:
      ativarAtendente(igId);
      return await construtor.construaResposta('fallback', contextoMensagem);
  }
}

/**
 * Processar payload de postback ou quick reply
 */
async function processarPayload(construtor: ConstrutorDeRespostaIG, payload: string, igId: string): Promise<any[]> {
  const contextoPayload = {
    igId: igId,
    payload: payload
  };

  payload = payload.toUpperCase();

  if (payload.includes("GET_STARTED")) {
    return await construtor.construaResposta('get_started', contextoPayload);
  } else if (payload.includes("FAZER_PEDIDO")) {
    return await construtor.construaResposta('fazer_pedido', contextoPayload);
  } else if (payload.includes("HORARIO_ATENDIMENTO")) {
    return await construtor.construaResposta('horario_atendimento', contextoPayload);
  } else if (payload.includes("CARDAPIO")) {
    return await construtor.construaResposta('cardapio', contextoPayload);
  } else if (payload.includes("ATENDENTE")) {
    ativarAtendente(igId);
    return await construtor.construaResposta('atendente', contextoPayload);
  } else {
    return await construtor.construaResposta('fallback', contextoPayload);
  }
}


/**
 * Ativar modo atendente
 */
function ativarAtendente(igId: string) {
  const client = require('redis').createClient();
  client.setex('atendente:' + igId, 60 * 60 * 4, 'true', (erro: any) => {
    if (erro) console.error('[Instagram Direct] Erro ao ativar atendente:', erro);
  });
}

/**
 * Enviar mensagem via Instagram Direct API
 */
async function enviarMensagemInstagramDirect(
  dadosInstagram: DadosInstagram,
  recipientId: string,
  resposta: any,
  delay: number = 0
) {
  return new Promise((resolve, reject) => {
    setTimeout(async () => {
      const startTime = new Date().getTime();

      try {
        console.log('[Instagram Direct] =====================================');
        console.log('[Instagram Direct] INICIANDO ENVIO DE MENSAGEM');
        console.log('[Instagram Direct] Timestamp:', new Date().toISOString());
        console.log('[Instagram Direct] Recipient ID:', recipientId);
        console.log('[Instagram Direct] User ID Instagram:', dadosInstagram.userIdInsta);
        console.log('[Instagram Direct] Access Token (primeiros 20 chars):', dadosInstagram.accessToken.substring(0, 20) + '...');
        console.log('[Instagram Direct] Delay configurado:', delay + 'ms');

        const url = `https://graph.instagram.com/v21.0/me/messages`;

        const requestBody = {
          recipient: {
            id: recipientId
          },
          message: resposta
        };

        console.log('[Instagram Direct] STEP 1: Configuração da requisição');
        console.log('[Instagram Direct] URL:', url);
        console.log('[Instagram Direct] Método: POST');
        console.log('[Instagram Direct] Request Body completo:', JSON.stringify(requestBody, null, 2));

        // Verificar se a resposta tem quick_replies
        if (resposta.quick_replies && resposta.quick_replies.length > 0) {
          console.log('[Instagram Direct] ATENÇÃO: Mensagem contém quick_replies');
          console.log('[Instagram Direct] Quick replies:', JSON.stringify(resposta.quick_replies, null, 2));
          console.log('[Instagram Direct] Instagram Direct pode não suportar quick_replies como o Messenger');
        }

        console.log('[Instagram Direct] STEP 2: Enviando requisição...');
        const requestStartTime = new Date().getTime();

        let response;
        try {
          response = await axios.post(url, requestBody, {
            params: {
              access_token: dadosInstagram.accessToken
            },
            headers: {
              'Content-Type': 'application/json'
            }
          });
        } catch (firstError: any) {
          // Se falhou e a mensagem tem quick_replies, tentar novamente sem eles
          if (resposta.quick_replies && resposta.quick_replies.length > 0) {
            console.warn('[Instagram Direct] FALLBACK: Primeira tentativa falhou, tentando sem quick_replies...');
            console.warn('[Instagram Direct] Erro original:', firstError.response?.data || firstError.message);

            const requestBodySemQuickReplies = {
              recipient: {
                id: recipientId
              },
              message: {
                text: resposta.text
              }
            };

            console.log('[Instagram Direct] FALLBACK: Request body sem quick_replies:', JSON.stringify(requestBodySemQuickReplies, null, 2));

            // Tentar novamente sem quick_replies
            response = await axios.post(url, requestBodySemQuickReplies, {
              params: {
                access_token: dadosInstagram.accessToken
              },
              headers: {
                'Content-Type': 'application/json'
              }
            });

            console.log('[Instagram Direct] FALLBACK: Sucesso! Mensagem enviada sem quick_replies');
          } else {
            // Se não tem quick_replies, re-throw o erro original
            throw firstError;
          }
        }

        const requestEndTime = new Date().getTime();
        const requestDuration = requestEndTime - requestStartTime;
        const totalTime = requestEndTime - startTime;

        console.log('[Instagram Direct] STEP 3: Resposta recebida!');
        console.log('[Instagram Direct] Status:', response.status);
        console.log('[Instagram Direct] Status Text:', response.statusText);
        console.log('[Instagram Direct] Headers:', response.headers);
        console.log('[Instagram Direct] Response Data:', JSON.stringify(response.data, null, 2));
        console.log('[Instagram Direct] Tempo da requisição:', requestDuration + 'ms');
        console.log('[Instagram Direct] Tempo total (com delay):', totalTime + 'ms');
        console.log('[Instagram Direct] SUCESSO: Mensagem enviada com sucesso!');
        console.log('[Instagram Direct] =====================================');

        resolve(response.data);

      } catch (error: any) {
        const totalTime = new Date().getTime() - startTime;

        console.error('[Instagram Direct] =====================================');
        console.error('[Instagram Direct] ERRO AO ENVIAR MENSAGEM');
        console.error('[Instagram Direct] Tempo até erro:', totalTime + 'ms');
        console.error('[Instagram Direct] Timestamp do erro:', new Date().toISOString());

        // Log completo do erro
        console.error('[Instagram Direct] Erro completo:', error);
        console.error('[Instagram Direct] Error name:', error.name);
        console.error('[Instagram Direct] Error message:', error.message);

        if (error.response) {
          console.error('[Instagram Direct] Resposta de erro HTTP:');
          console.error('[Instagram Direct] Status:', error.response.status);
          console.error('[Instagram Direct] Status text:', error.response.statusText);
          console.error('[Instagram Direct] Headers:', error.response.headers);
          console.error('[Instagram Direct] Data completa:', JSON.stringify(error.response.data, null, 2));

          // Log do request que falhou
          if (error.config) {
            console.error('[Instagram Direct] Request que falhou:');
            console.error('[Instagram Direct] URL:', error.config.url);
            console.error('[Instagram Direct] Method:', error.config.method?.toUpperCase());
            console.error('[Instagram Direct] Headers enviados:', error.config.headers);
            console.error('[Instagram Direct] Params:', error.config.params);
            console.error('[Instagram Direct] Data enviada:', error.config.data);
          }

          // Log de trace IDs se disponível
          if (error.response.data?.error?.fbtrace_id) {
            console.error('[Instagram Direct] Facebook Trace ID:', error.response.data.error.fbtrace_id);
          }

          // Detectar erros específicos
          if (error.response.status === 500) {
            console.error('[Instagram Direct] ERRO 500: Erro interno do servidor Instagram');
            console.error('[Instagram Direct] Possíveis causas:');
            console.error('[Instagram Direct] - Token de acesso inválido ou expirado');
            console.error('[Instagram Direct] - Formato de mensagem incompatível');
            console.error('[Instagram Direct] - Quick replies não suportadas no Instagram Direct');
            console.error('[Instagram Direct] - Rate limiting');
          }

        } else if (error.request) {
          console.error('[Instagram Direct] Erro de rede/timeout:');
          console.error('[Instagram Direct] Request:', error.request);
        } else {
          console.error('[Instagram Direct] Erro de configuração:', error.message);
        }

        console.error('[Instagram Direct] =====================================');
        reject(error);
      }
    }, delay);
  });
}

/**
 * Endpoint para testar envio de mensagem simples (apenas texto)
 */
router.post('/testarEnvio', async (req: any, res: any) => {
  try {
    const { recipientId, mensagem } = req.body;

    if (!recipientId || !mensagem) {
      return res.json(Resposta.erro('Parâmetros recipientId e mensagem são obrigatórios'));
    }

    const mapeador = new MapeadorDeDadosInstagram();
    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (!dadosInstagram) {
      return res.json(Resposta.erro('Nenhuma conta Instagram Direct conectada'));
    }

    console.log('[Instagram Direct] Testando envio de mensagem simples...');
    console.log('[Instagram Direct] Recipient ID:', recipientId);
    console.log('[Instagram Direct] Mensagem:', mensagem);

    const resultado = await enviarMensagemInstagramDirect(
      dadosInstagram,
      recipientId,
      { text: mensagem },
      0
    );

    res.json(Resposta.sucesso(resultado));

  } catch (error: any) {
    console.error('[Instagram Direct] Erro ao testar envio:', error);
    res.json(Resposta.erro(error.message));
  }
});

/**
 * Endpoint para testar envio de mensagem com quick replies
 */
router.post('/testarEnvioComQuickReplies', async (req: any, res: any) => {
  try {
    const { recipientId, mensagem, quickReplies } = req.body;

    if (!recipientId || !mensagem) {
      return res.json(Resposta.erro('Parâmetros recipientId e mensagem são obrigatórios'));
    }

    const mapeador = new MapeadorDeDadosInstagram();
    const dadosInstagram = await mapeador.selecioneSync({
      tipoIntegracao: 'DIRECT',
      ativo: true
    });

    if (!dadosInstagram) {
      return res.json(Resposta.erro('Nenhuma conta Instagram Direct conectada'));
    }

    console.log('[Instagram Direct] Testando envio de mensagem com quick replies...');
    console.log('[Instagram Direct] Recipient ID:', recipientId);
    console.log('[Instagram Direct] Mensagem:', mensagem);
    console.log('[Instagram Direct] Quick Replies:', quickReplies);

    const mensagemCompleta = {
      text: mensagem,
      quick_replies: quickReplies || [
        { content_type: 'text', title: 'Sim', payload: 'SIM' },
        { content_type: 'text', title: 'Não', payload: 'NAO' }
      ]
    };

    const resultado = await enviarMensagemInstagramDirect(
      dadosInstagram,
      recipientId,
      mensagemCompleta,
      0
    );

    res.json(Resposta.sucesso(resultado));

  } catch (error: any) {
    console.error('[Instagram Direct] Erro ao testar envio com quick replies:', error);
    res.json(Resposta.erro(error.message));
  }
});

export const InstagramDirectController: Router = router;
