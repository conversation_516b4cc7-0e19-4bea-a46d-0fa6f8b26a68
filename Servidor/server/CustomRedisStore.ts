import * as expressSession from 'express-session';
const RedisStore = require('connect-redis')(expressSession);
import * as UAParser from 'ua-parser-js';
import DeviceDetector = require("device-detector-js");
import {MapeadorDeRegistroDeLogin} from "./mapeadores/MapeadorDeRegistroDeLogin";
import {ExecutorAsync} from "./utils/ExecutorAsync";
import { promisify } from 'util';
import {RegistroDeLogin} from "./domain/RegistroDeLogin";

export class CustomRedisStore extends RedisStore {
  constructor(options: any) {
    super(options);
  }

  obtenhaDadosRequisicao(req: any) {
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    const diaAcesso = new Date();
    const horarioAcesso = new Date();
    const sid = req.sessionID;
    const userAgentHeader = req.headers['user-agent'];

    const agent = new UAParser().setUA(userAgentHeader).getResult();
    const detector = new DeviceDetector();

    const result = detector.parse(userAgentHeader);

    const detalhes_dispositivo = `${result.os?.name} ${result.os?.version} - ${result.device?.brand} ${result.device?.model}`;
    const nome_navegador = agent.browser.name;
    const versao_navegador = agent.browser.version;
    const tipoDispositivo = result.device?.type;
    const urlSolicitado = req.originalUrl || req.url;
    const origem = req.headers['referer'] || req.headers['referrer'];
    const usuario = req.user;

    return new RegistroDeLogin(
        usuario,
        ip,
        sid,
        diaAcesso,
        horarioAcesso,
        tipoDispositivo,
        detalhes_dispositivo,
        nome_navegador,
        versao_navegador,
        urlSolicitado,
        origem
    );
  }
  // Sobrescrever o método de salvar sessão
  async set(sid: any, sess: any, callback: Function) {
    const dados_requisicao: RegistroDeLogin = this.obtenhaDadosRequisicao(sess.req);

    dados_requisicao.empresaId = sess.req.empresa.id;
    const mapeador = new MapeadorDeRegistroDeLogin();

    if( dados_requisicao.usuario ) {
      const registro = await mapeador.insiraGraph(dados_requisicao);
    }

    console.log(`Salvando sessão: ${sid}`, sess.req.url);
    // Chamar método original com lógica adicional
    super.set(sid, sess, function(err: Error) {
      // Lógica adicional após salvar a sessão, se necessário
      if( sess.req.user ) {
        console.log('sessao');
      }
      if (callback) callback(err);
    });
  }

  // Sobrescrever o método destroy
  async destroy(sid: any, callback: Function) {
    console.log(`Destruindo sessão: ${sid}`);
    // Executar lógica adicional antes de chamar o método original, se necessário

    ExecutorAsync.execute(async (cbAsync: any) => {
      let contexto: any = require('domain').active.contexto;
      //atualiza o registro de login via id de sessao
      const mapeador = new MapeadorDeRegistroDeLogin();
      const registro: RegistroDeLogin = await mapeador.selecioneSync({idSessao: sid});

      if( !registro ) {
        return;
      }

      registro.sessaoAtiva = false;
      registro.diaLogout = new Date();
      registro.horarioLogout = new Date();

      if( registro ) {
        await mapeador.atualizeSync(registro);
      }
      cbAsync();
    }, (erroAsync: any) => { console.log(erroAsync)}, 100);

    // Chamar método original para destruir a sessão
    super.destroy(sid, function(err: Error) {
      // Lógica adicional após destruir a sessão, se necessário
      if (callback) callback(err);
    });
  }
}
