import * as crypto from 'crypto';

// Base64 URL-safe encode
export function b64url(input: Buffer | string): string {
  const b = Buffer.isBuffer(input) ? input : Buffer.from(input, 'utf8');
  return b.toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/g, '');
}

// Base64 URL-safe decode to string
export function b64urlToStr(b64: string): string {
  const padLen = (4 - (b64.length % 4)) % 4;
  const pad = padLen === 0 ? '' : padLen === 2 ? '==' : padLen === 3 ? '=' : '';
  const s = b64.replace(/-/g, '+').replace(/_/g, '/') + pad;
  return Buffer.from(s, 'base64').toString('utf8');
}

export type StateSubPayload = { sd: string; n: string; exp: number };

// Generate state from subdomain with HMAC-SHA256 signature
export function makeStateFromSub(sd: string, secret: string, ttlSeconds = 600): string {
  if (!/^[a-z0-9-]{1,63}$/i.test(sd)) throw new Error('subdomínio inválido');

  const payload: StateSubPayload = {
    sd,
    n: crypto.randomBytes(9).toString('hex'),
    exp: Math.floor(Date.now() / 1000) + ttlSeconds
  };
  const payloadStr = JSON.stringify(payload);
  const sig = crypto.createHmac('sha256', secret).update(payloadStr).digest();
  return `${b64url(payloadStr)}.${b64url(sig)}`;
}

// Verify state and return payload
export function verifyStateSub(state: string, secret: string): StateSubPayload {
  const parts = (state || '').split('.');
  if (parts.length !== 2) throw new Error('state inválido');
  const [payloadB64, sigB64] = parts;

  const payloadStr = b64urlToStr(payloadB64);
  const expectedSig = crypto.createHmac('sha256', secret).update(payloadStr).digest();
  const gotSigRaw = sigB64.replace(/-/g, '+').replace(/_/g, '/');
  const padLen = (4 - (gotSigRaw.length % 4)) % 4;
  const gotSig = Buffer.from(gotSigRaw + (padLen === 0 ? '' : padLen === 2 ? '==' : padLen === 3 ? '=' : ''), 'base64');

  if (expectedSig.length !== gotSig.length || !crypto.timingSafeEqual(expectedSig, gotSig)) {
    throw new Error('assinatura inválida');
  }

  const payload = JSON.parse(payloadStr) as StateSubPayload;
  if (!payload.sd || !payload.n || !payload.exp) throw new Error('payload inválido');
  if (!/^[a-z0-9-]{1,63}$/i.test(payload.sd)) throw new Error('subdomínio inválido');
  if (payload.exp < Math.floor(Date.now() / 1000)) throw new Error('state expirado');

  return payload;
}
