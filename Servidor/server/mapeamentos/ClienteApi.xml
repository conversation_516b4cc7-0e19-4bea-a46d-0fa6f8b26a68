<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="clienteApi">
  <resultMap id="clienteApiResultMap" type="ClienteApi">
    <id property="id" column="cliente_api_id"/>

    <result property="segredo" column="cliente_api_segredo"/>
    <result property="nome" column="cliente_api_nome"/>
    <result property="identificador" column="cliente_api_identificador"/>
    <result property="tipo" column="cliente_api_tipo"/>
    <result property="ip" column="cliente_api_ip"/>
    <result property="dataCriacao" column="cliente_api_data_criacao"/>
    <result property="ativo" column="cliente_api_ativo"/>
    <result property="acessoDireto" column="cliente_api_acesso_direto"/>

    <association property="sistemaIntegrado" resultMap="sistemaIntegrado.sistemaIntegradoRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="clienteApiResultMap" prefix="true">
    select cliente_api.*, sistema_integrado.*
      from  cliente_api left join sistema_integrado on cliente_api.sistema_integrado_id = sistema_integrado.id
        where cliente_api.empresa_id = #{idEmpresa}
          <if test="id">     and cliente_api.id = #{id} </if>
          <if test="parceiros">    and (identificador is null or identificador not in ('gcomapi', 'opendeliveryapi')) </if>
 </select>


  <update id="definaSistema" parameterType="map">
    update cliente_api
    set sistema_integrado_id = #{sistemaIntegrado.id}
    where id = #{id}
    and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeAtivo" parameterType="map">
    update cliente_api
    set ativo = #{ativo},
    acesso_direto = #{acessoDireto}
    where empresa_id = #{empresa.id}
      and id = #{id}
      and segredo = #{segredo}
  </update>

  <update id="atualizeAcessoDireto" parameterType="map">
    update cliente_api
    set acesso_direto = #{acessoDireto}
    where empresa_id = #{empresa.id}
    and id = #{id}
    and segredo = #{segredo}
  </update>
</mapper>
