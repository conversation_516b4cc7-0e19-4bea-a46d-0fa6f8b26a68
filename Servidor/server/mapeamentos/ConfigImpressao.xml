<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="configImpressao">
  <resultMap id="configImpressaoRM" type="ConfigImpressao">
    <id property="id" column="config_impressao_id"/>

    <result property="imprimirTXT" column="config_impressao_imprimir_txt"/>
    <result property="imprimirAutomatico" column="config_impressao_imprimir_automatico" />
    <result property="imprimirOnlineNaoPago" column="config_impressao_imprimir_online_nao_pago" />
    <result property="layoutPedido" column="config_impressao_layout_pedido" />
    <result property="multiplasImpressoras" column="config_impressao_multiplas_impressoras" />
    <result property="modoHTML" column="config_impressao_modo_html" />
    <result property="momentoImprimirAuto" column="config_impressao_momento_imprimir_auto" />
    <result property="cortarAutomatico" column="config_impressao_cortar_automatico"/>
    <result property="emitirBeep" column="config_impressao_emitir_beep" />
    <result property="duracaoBeep" column="config_impressao_duracao_beep" />
    <result property="quantidadeBeeps" column="config_impressao_quantidade_beeps" />
    <result property="ocultarNumeroCliente" column="config_impressao_ocultar_numero_cliente"/>
    <result property="ocultarCobranca" column="config_impressao_ocultar_cobranca"/>

    <collection  property="impressoras"   resultMap="impressora.impressoraRM"/>

  </resultMap>


  <insert id="insira" parameterType="ConfigImpressao" keyProperty="id">
    insert into config_impressao (imprimir_txt, imprimir_automatico, layout_pedido, multiplas_impressoras, impressora_id, impressora_resumido_id, modo_html,
    momento_imprimir_auto, cortar_automatico, emitir_beep, duracao_beep, quantidade_beeps, imprimir_online_nao_pago, ocultar_numero_cliente, ocultar_cobranca)
    values(#{imprimirTXT}, #{imprimirAutomatico}, #{layoutPedido}, #{multiplasImpressoras},  #{impressora.id}, #{impressoraResumido.id}, #{modoHTML},
    #{momentoImprimirAuto}, #{cortarAutomatico}, #{emitirBeep}, #{duracaoBeep}, #{quantidadeBeeps}, #{imprimirOnlineNaoPago}, #{ocultarNumeroCliente},
    #{ocultarCobranca});
  </insert>

  <update id="atualize">
    update config_impressao
    set imprimir_txt = #{imprimirTXT},
         imprimir_automatico =  #{imprimirAutomatico},
         imprimir_online_nao_pago = #{imprimirOnlineNaoPago},
         layout_pedido = #{layoutPedido},
         multiplas_impressoras = #{multiplasImpressoras},
         modo_html = #{modoHTML},
         momento_imprimir_auto = #{momentoImprimirAuto},
         cortar_automatico = #{cortarAutomatico},
         emitir_beep = #{emitirBeep},
         duracao_beep = #{duracaoBeep},
         quantidade_beeps = #{quantidadeBeeps},
         ocultar_numero_cliente = #{ocultarNumeroCliente},
         ocultar_cobranca = #{ocultarCobranca}
    where id = #{id};
  </update>

</mapper>
