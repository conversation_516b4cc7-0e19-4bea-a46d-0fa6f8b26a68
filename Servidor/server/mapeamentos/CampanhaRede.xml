<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="campanha_rede">
  <resultMap id="campanhaRedeResultMap" type="CampanhaRede">
    <id property="id" column="campanha_rede_id"/>

    <result property="nome" column="campanha_rede_nome"/>

    <result property="mensagem" column="campanha_rede_mensagem"/>

    <result property="qtdeEnviadas" column="campanha_rede_qtde_enviadas"/>
    <result property="qtdeLidas" column="campanha_rede_qtde_lidas"/>

    <result property="origemContatos" column="campanha_rede_origem_contatos"/>
    <result property="status" column="campanha_rede_status"/>

    <result property="tipoDeEnvio" column="campanha_rede_tipo_de_envio"/>
    <result property="horarioEnvio" column="campanha_rede_horario_envio"/>
    <result property="qtdeDeDiasNovaNotificacao" column="campanha_rede_qtde_de_dias_nova_notificacao"/>
    <result property="linkImagem" column="campanha_rede_link_imagem"/>

    <result property="foiTestada" column="campanha_rede_foi_testada"/>
    <result property="ativa" column="campanha_rede_ativa"/>

    <result property="foiReplicada" column="campanha_rede_foi_replicada"/>

    <result property="statusAprovacao" column="campanha_rede_status_aprovacao"/>

    <result property="qtdeMensagens" column="campanha_rede_qtde_mensagens"/>

    <association property="empresa" column="empresa_id" resultMap="empresa.empresaRM"/>

    <collection property="redes" resultMap="rede.redeRM"></collection>
  </resultMap>

  <resultMap id="dtoCampanhaResultMap" type="CampanhaRede">
    <id property="id" column="campanha_rede_id"/>

    <result property="nome" column="campanha_rede_nome"/>

    <result property="mensagem" column="campanha_rede_mensagem"/>

    <result property="qtdeEnviadas" column="campanha_rede_qtde_enviadas"/>
    <result property="qtdeLidas" column="campanha_rede_qtde_lidas"/>

    <result property="origemContatos" column="campanha_rede_origem_contatos"/>
    <result property="status" column="campanha_rede_status"/>

    <result property="tipoDeEnvio" column="campanha_rede_tipo_de_envio"/>
    <result property="horarioEnvio" column="campanha_rede_horario_envio"/>
    <result property="qtdeDeDiasNovaNotificacao" column="campanha_rede_qtde_de_dias_nova_notificacao"/>
    <result property="linkImagem" column="campanha_rede_link_imagem"/>

    <result property="foiTestada" column="campanha_rede_foi_testada"/>

    <result property="statusAprovacao" column="campanha_rede_status_aprovacao"/>

    <result property="qtdeMensagens" column="campanha_rede_qtde_mensagens"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="campanhaRedeResultMap" prefix="true">
    select *
      from campanha_rede
        join empresa on (campanha_rede.empresa_id = empresa.id)
        left join campanharede_rede on(campanharede_rede.campanha_rede_id = campanha_rede.id)
        left join rede on(campanharede_rede.rede_id = rede.id)
        left join numero_whatsapp on numero_whatsapp.empresa_id = empresa.id and numero_whatsapp.principal is true
        left join empresa_modulo em on em.empresa_id = empresa.id
        left join modulo on modulo.id = em.modulo_id
        where
        <if test="idEmpresa != null">
          campanha_rede.empresa_id = #{idEmpresa}
        </if>
        <if test="idEmpresa == null">
          1 = 1
        </if>
        <choose>
          <when test="status != null">
            and campanha_rede.status in
            <foreach item="s" collection="status" open="(" separator="," close=")">
              #{s}
            </foreach>
          </when>
          <when test="ativas != null">
            and campanha_rede.ativa = 1
          </when>
        </choose>
        <if test="foiTestada != null">
          and campanha_rede.foi_testada = #{foiTestada}
        </if>
        <if test="statusAprovacao != null">
          and campanha_rede.status_aprovacao = #{statusAprovacao}
        </if>
        <if test="agendadasAEnviar != null">
          and horario_envio &lt;= now()
        </if>
        <if test="pendentes != null">
          and campanha_rede.foi_testada = true
          and campanha_rede.status_aprovacao = 'Pendente'
        </if>
        <choose>
          <when test="tipoDeEnvio != null">
            and campanha_rede.tipo_de_envio = #{tipoDeEnvio}
          </when>
          <when test="id != null">
            and campanha_rede.id = #{id}
          </when>
          <when test="desativadas != null">
            and campanha_rede.ativa = 0
          </when>
        </choose>
          <if test="orderBy">
             order by campanha_rede.id desc
          </if>
          <if test="inicio != null">
            limit #{inicio}, #{quantidade}
          </if>
  </select>

  <select id="selecioneEmpresasAceitaram" parameterType="map" resultMap="empresa.dtoEmpresaRM" prefix="true">
    select *
    from campanha_rede
    join campanha on(campanha_rede.id = campanha.campanha_rede_id)
    join empresa on (campanha.empresa_id = empresa.id)
    where
    campanha_rede.id = #{id}
    <if test="pendentes != null">
    and campanha.status = 'Nova';
    </if>
  </select>

  <select id="selecioneTodas" parameterType="map" resultMap="campanhaRedeResultMap" prefix="true">
    select *
    from campanha_rede
    join empresa on (campanha_rede.empresa_id = empresa.id)
    where
    campanha_rede.empresa_id = #{idEmpresa}
    <choose>
      <when test="desativadas != null">
        and campanha_rede.ativa = 0
      </when>
      <otherwise>
        and campanha_rede.ativa = 1
      </otherwise>
    </choose>
    <choose>
      <when test="tipoDeEnvio != null">
        and campanha_rede.tipo_de_envio = #{tipoDeEnvio}
      </when>
    </choose>
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int" prefix="true">
    select count(*)
    from campanha_rede
    where
    <if test="idEmpresa != null">
      campanha_rede.empresa_id = #{idEmpresa}
    </if>
    <if test="idEmpresa == null">
      1 = 1
    </if>
    <choose>
      <when test="status != null">
        and campanha_rede.status in
        <foreach item="s" collection="status" open="(" separator="," close=")">
          #{s}
        </foreach>
      </when>
      <when test="pendentes != null">
        and campanha_rede.foi_testada = true
        and campanha_rede.status_aprovacao = 'Pendente'
      </when>
      <!--
      <when test="desativadas != null">
        and campanha_rede.ativa = 0
      </when>
      <otherwise>
        and campanha_rede.ativa = 1
      </otherwise>
      -->
    </choose>
    <choose>
      <when test="tipoDeEnvio != null">
        and campanha_rede.tipo_de_envio = #{tipoDeEnvio}
      </when>
      <when test="id != null">
        and atividade.id = #{id}
      </when>
    </choose>
    <if test="inicio != null">
      limit #{inicio}, #{quantidade}
    </if>
  </select>

  <select id="existe" parameterType="map" resultType="long">
    select count(1) from atividade a  where a.empresa_id = #{idEmpresa} and  a.nome = #{nome}
  </select>

  <select id="selecioneQtdeLidas" parameterType="map" resultType="long">
    select count(*) qtde
      from campanha_rede join mensagem_enviada me on campanha_rede.id = me.campanha_id  join links_mensagem_enviada lme on(lme.mensagem_enviada_id = me.id)
                               join link_encurtado le on (lme.link_encurtado_id = le.id)
        where  campanha_rede.empresa_id = #{idEmpresa} and  campanha_rede.id = #{id} and visitas > 0
            and lme.empresa_id = #{idEmpresa};
  </select>

  <update id="removaNaListaContatos">
    DELETE FROM campanharede_rede where campanha_rede_id = #{id};
  </update>

  <update id="atualizeStatus">
    update campanha_rede
    set qtde_mensagens = #{qtdeMensagens}, qtde_enviadas = #{qtdeEnviadas},
    qtde_lidas = #{qtdeLidas}, status = #{status}
    where
    campanha_rede.empresa_id = #{empresa.id}
    and id = #{id};
  </update>

  <insert id="insiraRedesCampanha">
    DELETE FROM campanharede_rede where campanha_rede_id = #{id};

    INSERT INTO campanharede_rede
    (campanha_rede_id, rede_id)
    VALUES
    <foreach item="rede" collection="dados" open="" separator="," close="">
      ( #{rede.idCampanha} ,   #{rede.idRede}  )
    </foreach>
  </insert>

  <update id="atualize">
    update campanha_rede
        set nome = #{nome}, mensagem = #{mensagem}, origem_contatos = #{origemContatos},
          status = #{status}, horario_envio = #{horarioEnvio}, tipo_de_envio = #{tipoDeEnvio},
          foi_replicada = #{foiReplicada}, ativa = #{ativa}
          where
            campanha_rede.empresa_id = #{empresa.id}
            and id = #{id};
  </update>
</mapper>
