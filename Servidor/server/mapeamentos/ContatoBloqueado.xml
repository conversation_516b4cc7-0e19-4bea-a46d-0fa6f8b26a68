<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="contato_bloqueado">
  <resultMap id="contatoBloqueadoRM" type="ContatoBloqueado">
    <id property="id" column="contato_bloqueado_id"/>

    <id property="telefone" column="contato_bloqueado_telefone"/>

  </resultMap>

  <delete id="remova">
    delete from contato_bloqueado where
    <choose>
      <when test="id != null">
        contato_bloqueado.id = #{id}
      </when>
      <when test="telefone != null">
        contato_bloqueado.telefone = #{telefone}
      </when>
    </choose>
  </delete>
</mapper>
