<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="relatorioWhatsapp">
  <resultMap id="relatorioWhatsappRM" type="ObjetoRelatorio">
    <id property="id" column="resumo_id"/>

    <result property="nome" column="resumo_nome"/>

    <result property="qtdeTotalLinksUnicos" column="resumo_qtde_links"/>
    <result property="qtdeMensagensCampanha" column="resumo_qtde_msgs_campanha"/>
    <result property="qtdeConversas" column="resumo_qtde_conversas"/>
    <result property="qtdeDePedidos" column="resumo_qtde_pedidos"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="relatorioWhatsappRM" prefix="true">
    select * from (
    select id, nome, qtde_links, qtde_msgs_campanha, (qtde_links - qtde_msgs_campanha) as qtde_conversas, qtde_pedidos
    from
    (select empresa.id, empresa.nome, count(*) qtde_links, (select count(*) from mensagem_enviada where mensagem_enviada.empresa_id = empresa.id and tipo_de_notificacao = 'Marketing'
    and horario >= #{dataInicio} and horario &lt; #{dataFim}) qtde_msgs_campanha,
    (select count(*) from pedido where pedido.empresa_id = empresa.id
    and pedido.horario >= #{dataInicio} and pedido.horario &lt; #{dataFim}) qtde_pedidos
    from
    (select sessao_link_saudacao.*, min(sessao_link_saudacao.id) id_min from sessao_link_saudacao join empresa on(empresa.id = sessao_link_saudacao.empresa_id)
    where empresa.rede = #{rede}
    and
    dia >= #{dataInicio} and dia &lt; #{dataFim}
    group by empresa_id, contato_id, dia) sessao_link_saudacao join
    empresa on(empresa.id = sessao_link_saudacao.empresa_id)
    group by empresa_id order by count(*) desc) resumo_interno) resumo;
  </select>

  <update id="atualize" parameterType="map">
    update sessao_link_saudacao
    set qtde_acessos = #{qtdeAcessos}
    where id = #{id}
  </update>
</mapper>
