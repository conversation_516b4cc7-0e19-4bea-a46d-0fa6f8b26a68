<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="formaDePagamentoBandeira">
  <resultMap id="formaDePagamentoRM" type="FormaDePagamentoBandeira">
    <id property="id" column="forma_de_pagamento_bandeira_id"/>

    <result property="nome" column="forma_de_pagamento_bandeira_nome"/>
    <result property="tipo" column="forma_de_pagamento_bandeira_tipo"/>
    <result property="metodo" column="forma_de_pagamento_bandeira_metodo"/>
    <result property="ativo" column="forma_de_pagamento_bandeira_ativo"/>


  </resultMap>

  <update id="atualize">
    update bandeira
     set nome = #{nome}, tipo = #{tipo}, metodo = #{metodo} , ativo = #{ativo}   where id = #{id}
  </update>

</mapper>
