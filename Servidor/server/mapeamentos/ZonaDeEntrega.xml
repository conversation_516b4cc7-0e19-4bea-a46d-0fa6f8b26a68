
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zonaDeEntrega">
  <resultMap id="zonaDeEntregaRM" type="ZonaDeEntrega">
    <id property="id" column="zona_de_entrega_id"/>

    <result property="nome" column="zona_de_entrega_nome"/>
    <result property="valor" column="zona_de_entrega_valor"/>
    <result property="permiteFreteGratis" column="zona_de_entrega_permite_frete_gratis"/>
    <result property="desativada" column="zona_de_entrega_desativada"/>
  </resultMap>


  <insert id="insira" parameterType="RaioDeCobranca" keyProperty="id" useGeneratedKeys="true">
    insert into zona_de_entrega (nome, valor, permite_frete_gratis, empresa_forma_de_entrega_id)
    values(#{nome}, #{valor}, #{permiteFreteGratis}, #{formaDeEntregaEmpresa.id});
  </insert>

  <update id="atualize">
    update zona_de_entrega
      set nome = #{nome}, valor =  #{valor},
        permite_frete_gratis = #{permiteFreteGratis}
         where id = #{id};
  </update>
  <update id="atualizeDesativada">
    update zona_de_entrega
      set desativada = #{desativada}
         where id = #{id};
  </update>


  <update id="remova">
     update endereco set zona_de_entrega_id = null where zona_de_entrega_id = #{id};
     delete from zona_de_entrega where id = #{id};
  </update>


</mapper>
