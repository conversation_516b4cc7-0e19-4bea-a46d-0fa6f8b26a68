<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="opendelivery">
  <resultMap id="opendeliveryRM" type="IntegracaoOpendelivery">
    <id property="id" column="opend_id"/>

    <result property="merchantApiKey" column="opend_merchant_api_key"/>
    <result property="merchantBaseUrl" column="opend_merchant_base_url"/>
    <result property="merchantWebhook" column="opend_merchant_webhook"/>
    <result property="merchantId" column="opend_merchant_id"/>
    <result property="merchantName" column="opend_merchant_name"/>
    <result property="ativa" column="opend_ativa"/>

    <association property="cliente"   resultMap="clienteApi.clienteApiResultMap"/>

    <collection  property="formasDePagamento"   resultMap="opendelivery.opendeliveryMethodRM"/>


  </resultMap>


  <resultMap id="opendeliveryMethodRM" type="FormaDePagamentoIntegrada" >
    <id property="id" column="opendelivery_method_id"/>

    <result property="codigo" column="opendelivery_method_codigo" />
    <result property="descricao" column="opendelivery_method_descricao" />
    <result property="tipoBandeira" column="opendelivery_method_tipo_bandeira" />

  </resultMap>

  <select id="selecioneMetodosPagamento" parameterType="map" resultMap="opendeliveryMethodRM"  prefix="true">
      select * from opendelivery_method;
  </select>

  <update id="atualize">
    update integracao_opendelivery
      set cliente_id = #{cliente.id}
            where id = #{id}   and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeMerchant">
     update integracao_opendelivery
          set
              merchant_api_key = #{merchantApiKey},
              merchant_base_url = #{merchantBaseUrl},
              merchant_webhook = #{merchantWebhook},
              merchant_id = #{merchantId},
              merchant_name = #{merchantName}
          where id = #{id}   and empresa_id = #{empresa.id};
  </update>
  <update id="atualizeAtiva">
  update integracao_opendelivery
    set ativa = #{ativa}
    where id = #{id}   and empresa_id = #{empresa.id};
  </update>

</mapper>
