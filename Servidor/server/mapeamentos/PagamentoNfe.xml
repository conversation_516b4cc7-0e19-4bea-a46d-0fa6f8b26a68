<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="pagamentoNfe">
  <resultMap id="pagamentoNfeRM" type="PagamentoNfe">
    <id property="id" column="pagamento_nfe_id"/>
    <result property="indicadorFormaPagamento" column="pagamento_nfe_indicador_forma_pagamento"/>
    <result property="meioDePagamento" column="pagamento_nfe_meio_de_pagamento"/>
    <result property="valor" column="pagamento_nfe_valor"/>
    <result property="tipoIntegracao" column="pagamento_nfe_tipo_integracao"/>
    <result property="cnpjCredenciadoraCartao" column="pagamento_nfe_cnpj_credenciadora_cartao"/>
    <result property="bandeiraCartao" column="pagamento_nfe_bandeira_cartao"/>
    <result property="numeroAutorizacaoCartao" column="pagamento_nfe_numero_autorizacao_cartao"/>
    <result property="descricao" column="pagamento_nfe_descricao"/>

    <association property="notaFiscalEletronica" column="nota_fiscal_eletronica_id" resultMap="notaFiscalEletronica.notaFiscalEletronicaRM"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists pagamento_nfe (
      id bigint(20) primary key,
      indicador_forma_pagamento int,
      meio_de_pagamento varchar(100),
      valor double,
      tipo_integracao int,
      cnpj_credenciadora_cartao varchar(14),
      bandeira_cartao varchar(100),
      numero_autorizacao_cartao varchar(100),
      descricao text,
      nota_fiscal_eletronica_id bigint(20),
      foreign key (nota_fiscal_eletronica_id) references nota_fiscal_eletronica (id)
    );
  </create>
</mapper>
