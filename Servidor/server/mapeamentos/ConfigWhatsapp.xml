<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="configWhatsapp">
  <resultMap id="configWhatsappRM" type="ConfigWhatsapp">
    <id property="id" column="config_whatsapp_id"/>

    <result property="tempoMsgSaudacao" column="config_whatsapp_tempo_msg_saudacao"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="configWhatsappRM" prefix="true">
    select *
      from config_whatsapp
    where
      empresa_id = #{idEmpresa}
      order by config_whatsapp.id desc
      <if test="inicio != null">
        limit #{inicio}, #{total}
      </if>
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int" prefix="true">
    select count(*)
    from config_whatsapp
    where
    empresa_id = #{idEmpresa}
    order by config_whatsapp.id desc;
  </select>

  <update id="atualize">
    update config_whatsapp
    set tempo_msg_saudacao = #{tempoMsgSaudacao}
    where id = #{id}
    and empresa_id = #{empresa.id};
  </update>
</mapper>
