<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="regraDaPromocao">
  <resultMap id="regraDaPromocaoRM" type="RegraDaPromocao">
    <id property="id" column="regra_da_promocao_id"/>

    <result property="percentual" column="regra_da_promocao_percentual" />
    <result property="maximoAplicacoes" column="regra_da_promocao_maximo_aplicacoes"/>
    <result property="ativa" column="regra_da_promocao_ativa"/>

    <result property="valorMinimoPedido" column="regra_da_promocao_valor_minimo_pedido" />
    <result property="produtoCompoeMinimo" column="regra_da_promocao_produto_compoe_minimo"/>

    <association property="produto"   resultMap="produto.produtoResultMap"/>
    <association property="adicional"  columnPrefix="regra_"  resultMap="adicionalDeProduto.adicionalDeProdutoRM"/>
    <association property="promocao" resultMap="promocao.promocaoRM" />
    <result property="quantidade" column="regra_da_promocao_quantidade" />
    <association property="tamanhoPizzaComprar"   resultMap="produtoTemplate.produtoTemplateTamanhoComprarRM"/>
    <association property="tamanhoPizzaGanhar"
                 resultMap="produtoTemplate.produtoTemplateTamanhoGanharRM"/>
    <discriminator javaType="String" column="regra_da_promocao_tipo" >

      <case value="percentual" resultType="RegraDaPromocaoPercentual"></case>
      <case value="percentual-no-segundo" resultType="RegraDaPromocaoPercentualNoSegundo"></case>
      <case value="percentual-todos" resultType="RegraDaPromocaoPercentualEmTodos"></case>
      <case value="adicionar-produto" resultType="RegraDaPromocaoAdicionarProduto"></case>
      <case value="compre-pizza-ganhe-outra" resultType="RegraDaPromocaoComprePizzaXGanhePizzaY"></case>
      <case value="promocao-china-in-box-rolinhos" resultType="RegraDaPromocaoChinaInBoxRolinhos"></case>
      <case value="chinainbox-bowl" resultType="RegraDaPromocaoChinaInBoxBowl"></case>

    </discriminator>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="brindeResultMap" prefix="true">
    select * from regra_da_promocao
    where empresa_id = #{idEmpresa}
    and excluido is not true;
  </select>

  <insert id="insira" parameterType="map">
    insert into regra_da_promocao(percentual, maximo_aplicacoes, ativa, produto_id, adicional_id, promocao_id, tipo, empresa_id
    , valor_minimo_pedido, produto_compoe_minimo, tamanho_pizza_comprar_id, tamanho_pizza_ganhar_id, quantidade)
    values (#{percentual}, #{maximoAplicacoes}, #{ativa}, #{produto.id}, #{adicional.id}, #{promocao.id}, #{tipo}, #{empresa.id}
    , #{valorMinimoPedido}, #{produtoCompoeMinimo}, #{tamanhoPizzaComprar.id}, #{tamanhoPizzaGanhar.id}, #{quantidade})
  </insert>


  <update id="atualizeStatusAtiva">
    update regra_da_promocao
    set ativa = #{ativa}
    where id = #{id}
  </update>

  <update id="removaTodas" parameterType="map">
    update regra_da_promocao
    set excluido = true
    where   promocao_id = #{idPromocao}
    and empresa_id  = #{empresa.id}
  </update>


</mapper>
