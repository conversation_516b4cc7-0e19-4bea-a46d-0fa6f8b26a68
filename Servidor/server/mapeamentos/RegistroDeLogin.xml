<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="registroLogin">

  <resultMap id="registroLoginRM" type="RegistroDeLogin">
    <id property="id" column="registro_de_login_id"/>

    <result property="ip" column="registro_de_login_ip"/>
    <result property="diaAcesso" column="registro_de_login_dia_acesso"/>
    <result property="horarioAcesso" column="registro_de_login_horario_acesso"/>
    <result property="tipoDispositivo" column="registro_de_login_tipo_dispositivo"/>
    <result property="detalhesDispositivo" column="registro_de_login_detalhes_dispositivo"/>
    <result property="nomeNavegador" column="registro_de_login_nome_navegador"/>

    <result property="idSessao" column="registro_de_login_id_sessao"/>

    <result property="versaoNavegador" column="registro_de_login_versao_navegador"/>
    <result property="url" column="registro_de_login_url"/>
    <result property="origem" column="registro_de_login_origem"/>
    <result property="diaLogout" column="registro_de_login_dia_logout"/>
    <result property="horarioLogout" column="registro_de_login_horario_logout"/>
    <result property="sessaoAtiva" column="registro_de_login_sessao_ativa"/>

    <result property="empresaId" column="registro_de_login_empresa_id"/>

    <result property="hostname" column="registro_de_login_hostname"/>
    <result property="city" column="registro_de_login_city"/>
    <result property="region" column="registro_de_login_region"/>
    <result property="country" column="registro_de_login_country"/>
    <result property="loc" column="registro_de_login_loc"/>
    <result property="org" column="registro_de_login_org"/>
    <result property="postal" column="registro_de_login_postal"/>
    <result property="timezone" column="registro_de_login_timezone"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="registroLoginRM" prefix="true">
    select *
    from registro_de_login
    where
    registro_de_login.empresa_id = #{idEmpresa}
    <if test="idSessao != null">
      and id_sessao = #{idSessao}
    </if>
    <if test="idUsuario != null">
      and usuario_id = #{idUsuario}
    </if>

    <if test="sessaoAtiva != null">
      and sessao_ativa is true
    </if>
  </select>

  <update id="atualize" parameterType="map">
      update registro_de_login
      set ip = #{ip},
          dia_acesso = #{diaAcesso},
          horario_acesso = #{horarioAcesso},
          tipo_dispositivo = #{tipoDispositivo},
          detalhes_dispositivo = #{detalhesDispositivo},
          nome_navegador = #{nomeNavegador},
          versao_navegador = #{versaoNavegador},
          url = #{url},
          origem = #{origem},
          dia_logout = #{diaLogout},
          horario_logout = #{horarioLogout},
          sessao_ativa = #{sessaoAtiva},
          hostname = #{hostname},
          city = #{city},
          region = #{region},
          country = #{country},
          loc = #{loc},
          org = #{org},
          postal = #{postal},
          timezone = #{timezone}
      where id = #{id};
  </update>
</mapper>
