<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="notificacaoifood">
  <resultMap id="notificacaoifoodRM" type="NotificacaoIfood">
    <id property="id" column="notificacao_ifood_id"/>

    <result property="code" column="notificacao_ifood_code" />
    <result property="fullCode" column="notificacao_ifood_full_code" />
    <result property="orderId" column="notificacao_ifood_order_id" />
    <result property="merchantId" column="notificacao_merchant_id" />

    <result property="executada" column="notificacao_ifood_executada" />
    <result property="horario" column="notificacao_ifood_horario" />
    <result property="dados" column="notificacao_ifood_dados" />
    <result property="erro" column="notificacao_ifood_erro" />
    <result property="ignorar" column="notificacao_ifood_ignorar" />

  </resultMap>

  <insert id="insira" parameterType="map">
    insert IGNORE into notificacao_ifood (id,merchant_id,code,full_code, order_id, horario, horario_criacao, dados, executada)
          values (#{id},#{merchantId},#{code}, #{fullCode}, #{orderId},#{horario},#{horarioCriacao},#{dados},false)
  </insert>

  <select id="selecione" parameterType="map"  resultMap="notificacaoifoodRM" prefix="true">
    select * from notificacao_ifood
      where
          <if test="id">
            id  = #{id}
          </if>

          <if test="orderId">
            order_id  = #{orderId}
          </if>

          <if test="merchantId">
            merchant_id  = #{merchantId}
          </if>

          <if test="codes">
            and code in (
            <foreach item="item" collection="codes" open="" separator="," close="">
              #{code}
            </foreach> )


            order by horario desc
          </if>

          <if test="naoExecutadas">
            executada is not true and ignorar is not true and datediff( horario, now())  = 0
          </if>

  </select>

  <update id="atualize">
    update notificacao_ifood
    set executada = #{executada}, erro = #{erro}, ignorar = #{ignorar}
    where id = #{id}

  </update>

</mapper>

