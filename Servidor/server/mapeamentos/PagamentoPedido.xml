<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pagamentoPedido">

  <resultMap id="pagamentoPedidoRM" type="PagamentoPedido">
    <id property="id" column="pagamento_id"/>

    <result property="status" column="pagamento_status"/>
    <result property="valor" column="pagamento_valor"/>
    <result property="taxa" column="pagamento_taxa"/>
    <result property="valorTaxaSplit" column="pagamento_valor_taxa_split"/>
    <result property="codigo" column="pagamento_codigo"/>
    <result property="codigoQrCode" column="pagamento_codigo_qr_code"/>
    <result property="idLink" column="pagamento_id_link"/>
    <result property="link" column="pagamento_link"/>
    <result property="codigoTransacao" column="pagamento_codigo_transacao"/>
    <result property="nsu" column="pagamento_nsu"/>
    <result property="codigoAutorizacao" column="pagamento_codigo_autorizacao"/>
    <result property="codigoAdquirente" column="pagamento_codigo_adquirente"/>
    <result property="codigoAutenticacao" column="pagamento_codigo_autenticacao"/>

    <result property="motivoReprovacao" column="pagamento_motivo_reprovacao"/>
    <result property="bandeira" column="pagamento_bandeira"/>
    <result property="codigoTipoPagamento" column="pagamento_codigo_tipo_pagamento"/>
    <result property="metodoPagamento" column="pagamento_metodo_pagamento"/>

    <result property="trocoPara" column="pagamento_troco_para"/>
    <result property="email" column="pagamento_email"/>
    <result property="cpf" column="pagamento_cpf"/>
    <result property="finalCartao" column="pagamento_final_cartao"/>
    <result property="parcela" column="pagamento_parcela"/>
    <result property="estornoId" column="pagamento_estorno_id"/>
    <result property="urlAutenticar" column="pagamento_url_autenticar"/>
    <result property="tokenizacaoId" column="pagamento_tokenizacao_id"/>
    <result property="provedorExterno" column="pagamento_provedor_externo"/>
    <result property="dataExpiracao" column="pagamento_data_expiracao"/>

    <association property="formaDePagamento" resultMap="formaDePagamento.formaDePagamentoRM"/>
    <association property="cashbackReserva" resultMap="cashbackReserva.cashbackReservaRM"/>

  </resultMap>


  <resultMap id="pagamentoPedidoCompletoRM" type="PagamentoPedido">
    <id property="id" column="pagamento_id"/>

    <result property="status" column="pagamento_status"/>
    <result property="valor" column="pagamento_valor"/>
    <result property="valorTaxaSplit" column="pagamento_valor_taxa_split"/>
    <result property="taxa" column="pagamento_taxa"/>
    <result property="codigo" column="pagamento_codigo"/>
    <result property="codigoQrCode" column="pagamento_codigo_qr_code"/>
    <result property="idLink" column="pagamento_id_link"/>
    <result property="link" column="pagamento_link"/>
    <result property="bandeira" column="pagamento_bandeira"/>
    <result property="metodoPagamento" column="pagamento_metodo_pagamento"/>
    <result property="trocoPara" column="pagamento_troco_para"/>
    <result property="email" column="pagamento_email"/>
    <result property="cpf" column="pagamento_cpf"/>
    <result property="parcela" column="pagamento_parcela"/>

    <result property="codigoTransacao" column="pagamento_codigo_transacao"/>
    <result property="nsu" column="pagamento_nsu"/>
    <result property="codigoAutorizacao" column="pagamento_codigo_autorizacao"/>
    <result property="codigoAdquirente" column="pagamento_codigo_adquirente"/>
    <result property="codigoAutenticacao" column="pagamento_codigo_autenticacao"/>
    <result property="estornoId" column="pagamento_estorno_id"/>
    <result property="urlAutenticar" column="pagamento_url_autenticar"/>
    <result property="tokenizacaoId" column="pagamento_tokenizacao_id"/>
    <result property="provedorExterno" column="pagamento_provedor_externo"/>
    <result property="dataExpiracao" column="pagamento_data_expiracao"/>


    <association property="pedido" resultMap="pedido.pedidoPagamentoRM"/>
    <association property="formaDePagamento" resultMap="formaDePagamento.formaDePagamentoRM"/>
  </resultMap>


  <select id="selecione" resultMap="pagamentoPedidoCompletoRM" prefix="true">
     select *
      from  pagamento_pedido pagamento join pedido  on pedido.id = pagamento.pedido_id
                             join forma_de_pagamento on forma_de_pagamento_id = forma_de_pagamento.id
                             join contato on contato.id = pedido.contato_id
                             join empresa on empresa.id = pedido.empresa_id
                             left join forma_de_pagamento_integrada_nova on forma_de_pagamento_integrada_id = forma_de_pagamento_integrada_nova.id
                              left join integracao_pedido_fidelidade on integracao_pedido_fidelidade.empresa_id = empresa.id
                              left join plano on plano.id = integracao_pedido_fidelidade.plano_id
                              left join tipo_de_pontuacao  on tipo_de_pontuacao.id = plano.id_tipo_de_pontuacao
                              left join atividade on atividade.id = integracao_pedido_fidelidade.atividade_id
                              left join forma_de_entrega on forma_de_entrega.id = forma_de_entrega_id
                where
                  <choose>
                    <when test="idLink">
                      pagamento.id_link = #{idLink}
                    </when>

                    <when test="codigo">
                      pagamento.codigo  = #{codigo}
                    </when>

                    <when test="codigoTransacao">
                      pagamento.codigo_transacao = #{codigoTransacao}
                    </when>

                    <when test="tokenizacaoId">
                      pagamento.tokenizacao_id = #{tokenizacaoId}
                    </when>

                  </choose>

  </select>


  <update id="removaTodosDoPedido">
    delete from pagamento_pedido where pedido_id = #{id};
  </update>

  <update id="remova">
    delete from pagamento_pedido where  id = #{id};
  </update>


  <update id="atualize">
    update  pagamento_pedido
      set valor = #{valor}, forma_de_pagamento_id = #{formaDePagamento.id},
          troco_para = #{trocoPara} , taxa = #{taxa}, metodo_pagamento =#{metodoPagamento}, codigo_transacao = #{codigoTransacao}
    where  id = #{id};
  </update>

  <update id="atualizeDados">
    update  pagamento_pedido
        set codigo = #{codigo}, link = #{link}, id_link = #{idLink}, status = #{status},
            codigo_transacao = #{codigoTransacao}, motivo_reprovacao = #{motivoReprovacao},
            codigo_qr_code = #{codigoQrCode}, bandeira = #{bandeira} ,
            codigo_tipo_pagamento = #{codigoTipoPagamento},
            metodo_pagamento = #{metodoPagamento}, data_expiracao = #{dataExpiracao},
            final_cartao = #{finalCartao}, email = #{email}, cpf = #{cpf}, parcela = #{parcela},
            url_autenticar = #{urlAutenticar}, provedor_externo = #{provedorExterno},
            nsu = #{nsu}, codigo_autorizacao = #{codigoAutorizacao}, codigo_adquirente = #{codigoAdquirente},
            codigo_autenticacao = #{codigoAutenticacao} , tokenizacao_id = #{tokenizacaoId},
            valor_taxa_split = #{valorTaxaSplit}
          where  id = #{id};
  </update>

  <update id="atualizeStatus">
     update pagamento_pedido
        set status = #{status} where  id = #{id};
  </update>

  <update id="atualizeFormaPagamento">
     update pagamento_pedido
        set forma_de_pagamento_id = #{formaDePagamento.id}  where  id = #{id};
  </update>
  <update id="atualizeIdEstorno">
     update pagamento_pedido
        set estorno_id = #{idEstorno} , status = #{status}
      where  id = #{id};
  </update>

  <update id="atualizeCashbackReserva">
     update pagamento_pedido
          set cashback_reserva_id = #{cashbackReserva.id}  where  id = #{id};
  </update>

  <select id="relataorioPorFormasGlobal">

    <!-- média por mês de valor -->
    SELECT   if(online is true, 'ONLINE', 'NA ENTREGA') forma,
    COUNT(*) AS qtde,  SUM(pedido.valor + pedido.taxa_entrega) AS valor,
         YEAR(pedido.horario) AS ano,     MONTH(pedido.horario) AS mes
    FROM pagamento_pedido  JOIN pedido ON pedido_id = pedido.id  AND 3 >= pagamento_pedido.status
    JOIN forma_de_pagamento fpg ON fpg.id = forma_de_pagamento_id     LEFT JOIN config_meio_pagamento cf ON cf.id = config_meio_de_pagamento_id
    WHERE  pedido.status AND pedido.horario > '2024-07-01'  and fpg.nome != 'cashback' and origem != 'ifood'
    GROUP BY ano, mes, online is true ORDER BY ano, mes

    <!-- média por mês de valor pagamentos offline -->
    SELECT IF(descricao = 'dinheiro', 'Dinheiro',
                 IF(descricao LIKE '%pix%', 'Pix',
                       IF(descricao like '%credito%', 'Credito',
                             IF(descricao like '%debito%', "Debito", 'Cartoes Outros'
            )) )) AS forma,
            COUNT(*) AS qtde,
            SUM(pedido.valor + pedido.taxa_entrega) AS valor,
            YEAR(pedido.horario) AS ano,     MONTH(pedido.horario) AS mes
    FROM pagamento_pedido  JOIN pedido ON pedido_id = pedido.id  AND 3 >= pagamento_pedido.status
           JOIN forma_de_pagamento fpg ON fpg.id = forma_de_pagamento_id     LEFT JOIN config_meio_pagamento cf ON cf.id = config_meio_de_pagamento_id
           WHERE  pedido.status AND pedido.horario > '2024-07-01' and online is not true and fpg.nome != 'cashback' and origem != 'ifood'
             GROUP BY ano, mes, descricao = 'dinheiro',  descricao LIKE '%pix%',  descricao like '%credito%', ( descricao like '%debito%' and descricao not like '%credito%' )
                  ORDER BY ano, mes,forma;




    <!-- média por mês de valor pagamentos online -->
    SELECT   if(pix is true, 'PIX', 'ONLINE') forma,
    COUNT(*) AS qtde,  SUM(pedido.valor + pedido.taxa_entrega) AS valor,
      YEAR(pedido.horario) AS ano,     MONTH(pedido.horario) AS mes
    FROM pagamento_pedido  JOIN pedido ON pedido_id = pedido.id  AND 3 >= pagamento_pedido.status
              JOIN forma_de_pagamento fpg ON fpg.id = forma_de_pagamento_id
                  LEFT JOIN config_meio_pagamento cf ON cf.id = config_meio_de_pagamento_id
    WHERE  pedido.status AND pedido.horario > '2024-07-01'  and fpg.nome != 'cashback' and online is true
    GROUP BY ano, mes, pix is true ORDER BY ano, mes


    <!-- média por mês de valor pagamentos cartao bandeiras -->
    SELECT IF(descricao = 'dinheiro', 'Dinheiro',
              IF(descricao LIKE '%master%', 'Mastercard',
               IF(descricao like '%visa%', 'Visa',
                IF(descricao like '%elo%', "Elo",
                 if(descricao like '%hiper%', "HIPERCARD",
                   IF(descricao like '%amex%', "AMEX",  'Cartoes Outros'
    ))) ))) AS forma, descricao,
    COUNT(*) AS qtde,
    SUM(pedido.valor + pedido.taxa_entrega) AS valor,
    YEAR(pedido.horario) AS ano,     MONTH(pedido.horario) AS mes
    FROM pagamento_pedido  JOIN pedido ON pedido_id = pedido.id  AND 3 >= pagamento_pedido.status
    JOIN forma_de_pagamento fpg ON fpg.id = forma_de_pagamento_id     LEFT JOIN config_meio_pagamento cf ON cf.id = config_meio_de_pagamento_id
    WHERE  pedido.status AND pedido.horario > '2024-07-01' and online is not true and fpg.nome not in ('cashback','dinheiro')  and origem != 'ifood'
             and descricao not LIKE '%pix%'
    GROUP BY ano, mes, descricao like '%master%',  descricao like '%visa%',descricao like '%elo%',  descricao like '%hiper%', descricao like '%amex%'
    ORDER BY ano, mes,forma DESC;

  </select>

</mapper>
