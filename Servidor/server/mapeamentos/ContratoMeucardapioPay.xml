<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="contratoMeucardapioPay">

  <resultMap id="contratoMeucardapioPayRM" type="ContratoMeucardapioPay">
    <id property="id" column="contrato_meucardapio_pay_id"/>
    <result property="descricao" column="contrato_meucardapio_pay_descricao"/>
    <result property="taxaPix" column="contrato_meucardapio_pay_taxa_pix"/>
    <result property="taxaCartao" column="contrato_meucardapio_pay_taxa_cartao"/>
    <result property="formaDePagamento" column="contrato_meucardapio_pay_forma_de_pagamento"/>
    <result property="idExterno" column="contrato_meucardapio_pay_id_externo"/>
    <result property="padrao" column="contrato_meucardapio_pay_padrao"/>
    <result property="producao" column="contrato_meucardapio_pay_producao"/>
  </resultMap>


  <select id="selecione" parameterType="map" resultMap="contratoMeucardapioPayRM" prefix="true">
      select * from contrato_meucardapio_pay where
        <choose>
          <when test="producao">
            producao is true
          </when>
          <otherwise>
            producao is not true
          </otherwise>
        </choose>

          <if test="idExterno">
              and id_externo = #{idExterno}
          </if>

         <if test="padrao">
            and padrao is true
         </if>

          order by descricao
  </select>

</mapper>
