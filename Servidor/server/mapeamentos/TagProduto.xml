<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tagproduto">
  <resultMap id="tagprodutoRM" type="TagProduto">
    <id property="id" column="tag_produto_id"/>

    <result property="nome" column="tag_produto_nome"/>
    <result property="descricao" column="tag_produto_descricao"/>
    <result property="grupo" column="tag_produto_grupo"/>

  </resultMap>


  <resultMap id="tagDoprodutoRM" type="TagProduto">
    <id property="id" column="produto_tags_tag_produto_id"/>

  </resultMap>



  <select id="selecione" parameterType="map" resultMap="tagprodutoRM" prefix="true">
    select * from tag_produto
        where   grupo = #{grupo}
  </select>

  <update id="removaTagsProduto">
    delete from produto_tags where  produto_id = #{idProduto}
  </update>

  <insert id="insiraTagsProduto">
    INSERT INTO produto_tags
    (produto_id, tag_produto_id)
    VALUES
    <foreach item="item" collection="dados" open="" separator="," close="">
      ( #{item.idProduto} ,   #{item.idTag}  )
    </foreach>
  </insert>


</mapper>

