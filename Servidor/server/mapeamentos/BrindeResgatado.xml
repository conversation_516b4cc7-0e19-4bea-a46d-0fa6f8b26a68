<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="brindeResgatado">
  <resultMap id="brindeResgatadoRM" type="BrindeResgatado">
    <id property="id" column="brinde_resgatado_id"/>

    <result property="codigo" column="brinde_resgatado_codigo"/>
    <result property="valor" column="brinde_resgatado_mensagem"/>
    <result property="horario" column="brinde_resgatado_horario"/>
    <result property="mensagem" column="brinde_resgatado_mensagem"/>
    <result property="valorEmPontos" column="brinde_resgatado_valor_em_pontos"/>
    <result property="saldo" column="brinde_resgatado_saldo"/>
    <result property="removido" column="brinde_resgatado_removido"/>

    <association property="cartao"   resultMap="cartao.cartaoResultMap"/>
    <association property="brinde"   resultMap="brinde.brindeResultMap"/>
    <association property="produto"   resultMap="produto.produtoResumidoRM"/>
    <association property="empresa"  resultMap="empresa.empresaRM"/>
    <association property="operador"  resultMap="usuario.operadorResultMap"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="brindeResgatadoRM" prefix="true">
    select *
        from brinde_resgatado join cartao on cartao.id = brinde_resgatado.cartao_id
                              join contato on contato.id = cartao.contato_id
                              join plano on plano.id = cartao.plano_id
                              join empresa on empresa.id =  brinde_resgatado.empresa_id
                              left join brinde on brinde.id = id_brinde
                              left join produto on produto.id = produto_id
                              left join usuario operador on operador.id = brinde_resgatado.operador_id


    where
    <choose>
       <when test="migrar">
         not exists (select 1 from pontuacao_registrada pr where pr.cartao_id = brinde_resgatado.cartao_id and pontos_usados > 0)

         <if test="idCartao">
           and  cartao.id =  #{idCartao}
         </if>
       </when>
      <otherwise>
        empresa.id = #{idEmpresa} and brinde_resgatado.removido is not true
      </otherwise>
    </choose>
    <if test="id">
      and brinde_resgatado.id = #{id}
    </if>

    <if test="idContato">
      and  cartao.contato_id =  #{idContato}
    </if>


    <if test="codigo">
      and brinde_resgatado.codigo = #{codigo}
    </if>

    <if test="idPedido">
      and brinde_resgatado.pedido_id = #{idPedido}
    </if>

    <if test="idPlano">
      and plano.id = #{idPlano}
    </if>

    <if test="idBrinde">
      and brinde.id = #{idBrinde}
    </if>

    <if test="dataInicio">
      and  brinde_resgatado.horario  >= #{dataInicio}
    </if>

    <if test="dataFim">
      and  brinde_resgatado.horario    &lt;=  #{dataFim}
    </if>

    <if test="total">
      order by brinde_resgatado.id desc limit #{inicio},#{total}
    </if>

  </select>

  <update id="remova">
     update brinde_resgatado set removido = true
        where id = #{id} and empresa_id = #{empresa.id}
  </update>

  <insert id="insira" parameterType="map">
      insert into brinde_resgatado ( operador_id, cartao_id, id_brinde, produto_id, mensagem, valor_em_pontos, saldo, codigo,  empresa_id, horario, removido , pedido_id)
          values  ( #{operador.id}, #{cartao.id}, #{brinde.id}, #{produto.id}, #{mensagem}, #{valorEmPontos}, #{saldo}, #{codigo},  #{empresa.id} , now(), false, #{pedido.id});
  </insert>
</mapper>
