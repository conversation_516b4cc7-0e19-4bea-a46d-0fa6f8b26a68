<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="adicionalDeProduto">
  <resultMap id="adicionalDeProdutoRM" type="AdicionalDeProduto">
    <id property="id" column="adicional_produto_id"/>

    <result property="nome" column="adicional_produto_nome"/>
    <result property="obrigatorio" column="adicional_produto_obrigatorio"/>
    <result property="qtdMinima" column="adicional_produto_qtd_minima"/>
    <result property="qtdMaxima" column="adicional_produto_qtd_maxima"/>
    <result property="ordem" column="produto_adicional_produto_ordem"/>

    <result property="codigoIfood" column="adicional_produto_codigo_ifood"/>
    <result property="campoOrdenar" column="adicional_produto_campo_ordenar"/>
    <result property="podeRepetirItem" column="adicional_produto_pode_repetir_item" />

    <collection  property="opcoesDisponiveis" resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoRM"/>

    <result property="entidade" column="adicional_produto_entidade"/>

    <result property="tipo" column="adicional_produto_tipo"/>
    <result property="classe" column="adicional_produto_classe"/>
    <result property="tipoDeCobranca" column="adicional_produto_tipo_de_cobranca"/>

    <result property="compartilhado" column="adicional_produto_compartilhado"/>
    <result property="combo" column="adicional_produto_combo"/>

    <association property="template"   resultMap="produtoTemplate.produtoTemplateAdicionalRM"/>

    <discriminator javaType="String" column="adicional_produto_classe" >
      <case value="escolha-simples-produto" resultType="AdicionalDeProdutoEscolhaSimples"></case>
      <case value="multipla-escolha-produto" resultType="AdicionalDeProdutoMultiplaEscolha"></case>

      <case value="escolha-simples-pedido" resultType="AdicionalDePedidoEscolhaSimples"></case>
      <case value="multipla-escolha-pedido" resultType="AdicionalDePedidoMultiplaEscolha"></case>
    </discriminator>
  </resultMap>

  <resultMap id="adicionalDePedidoRM" type="AdicionalDeProduto">
    <id property="id" column="adicional_pedido_id"/>

    <result property="nome" column="adicional_pedido_nome"/>
    <result property="obrigatorio" column="adicional_pedido_obrigatorio"/>
    <result property="qtdMinima" column="adicional_pedido_qtd_minima"/>
    <result property="qtdMaxima" column="adicional_pedido_qtd_maxima"/>
    <result property="ordem" column="adicional_pedido_ordem"/>
    <result property="codigoIfood" column="adicional_pedido_codigo_ifood"/>
    <result property="campoOrdenar" column="adicional_pedido_campo_ordenar"/>
    <result property="podeRepetirItem" column="adicional_pedido_pode_repetir_item" />

    <collection  property="opcoesDisponiveis" resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoRM"/>

    <result property="entidade" column="adicional_pedido_entidade"/>

    <result property="tipo" column="adicional_pedido_tipo"/>
    <result property="classe" column="adicional_pedido_classe"/>
    <result property="tipoDeCobranca" column="adicional_pedido_tipo_de_cobranca"/>

    <result property="compartilhado" column="adicional_pedido_compartilhado"/>

    <association property="template"   resultMap="produtoTemplate.produtoTemplateAdicionalRM"/>

    <discriminator javaType="String" column="adicional_pedido_classe" >
      <case value="escolha-simples-pedido" resultType="AdicionalDePedidoEscolhaSimples"></case>
      <case value="multipla-escolha-pedido" resultType="AdicionalDePedidoMultiplaEscolha"></case>
    </discriminator>
  </resultMap>

  <resultMap id="adicionalCampoRM" type="AdicionalDeProduto">
    <id property="id" column="adicional_id"/>

    <result property="nome" column="adicional_nome"/>
    <result property="ordem" column="adicional_ordem"/>
    <result property="campoOrdenar" column="adicional_campo_ordenar"/>

    <association property="adicionalNoProduto"   resultMap="adicionalDeProduto.adicionalNoProdutoRM"/>

  </resultMap>

  <resultMap id="adicionalNoProdutoRM" type="AdicionalNoProduto">
    <id property="id" column="noproduto_adicional_produto_id"/>
    <result property="ordem" column="noproduto_ordem"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="adicionalDeProdutoRM" prefix="true">
    select * from
    (
    select * from adicional_produto
    <if test="limite">
      where dados_json is null
      and adicional_produto.excluido is not true
      and adicional_produto.catalogo_id in(604, 663, 1)
    limit #{limite}
    </if>
    ) adicional_produto
    left join produto_adicional_produto on(produto_adicional_produto.adicional_produto_id = adicional_produto.id)
    left join opcao_adicional_produto on adicional_produto.id = opcao_adicional_produto.adicional_produto_id
      and opcao_adicional_produto.excluido is not true and opcao_adicional_produto.oculta is not true
    left join configuracao_fiscal_opcao on configuracao_fiscal_opcao.opcao_adicional_produto_id = opcao_adicional_produto.id
    left join opcao_na_empresa on opcao_na_empresa.opcao_id = opcao_adicional_produto.id
    left join dependencia_opcao_adicional on dependencia_opcao_adicional.opcao_id = opcao_adicional_produto.id
    left join produto_template_adicional on(produto_template_adicional.id
      = adicional_produto.produto_template_adicional_id)
    left join produto_template_opcao on (produto_template_opcao.produto_template_adicional_id = produto_template_adicional.id
    and opcao_adicional_produto.produto_template_opcao_id = produto_template_opcao.id)
    left join insumo_opcao on opcao_adicional_produto.id = insumo_opcao.opcao_id
    left join insumo opcao_insumo on opcao_insumo.id = insumo_opcao.insumo_id
    where
      adicional_produto.excluido is not true and adicional_produto.oculto is not true
      <if test="objeto != null">
        and produto_adicional_produto.catalogo_id = #{catalogo.id}
        and produto_adicional_produto.objeto_id = #{objeto.id}
      </if>
      <if test="entidade != null">
        and adicional_produto.entidade = #{entidade}
      </if>
      <if test="id != null">
      and adicional_produto.id = #{id}
      </if>
    <if test="idProduto != null">
      and adicional_produto.produto_id = #{idProduto}
    </if>
    <if test="template != null">
      and adicional_produto.produto_template_adicional_id = #{template.id}
    </if>
    order by produto_adicional_produto.ordem
  </select>

  <select id="selecioneAdicionaisProdutos"   resultMap="adicionalDeProdutoRM" prefix="true"  >
    select * from  produto_adicional_produto join produto  on (produto_adicional_produto.objeto_id = produto.id)
                    join adicional_produto on (produto_adicional_produto.adicional_produto_id = adicional_produto.id
                        and adicional_produto.excluido is not true and adicional_produto.oculto is not true )
                    left join opcao_adicional_produto on (adicional_produto.id = opcao_adicional_produto.adicional_produto_id and
                              opcao_adicional_produto.excluido is not true and opcao_adicional_produto.oculta is not true)
                    left join configuracao_fiscal_opcao on configuracao_fiscal_opcao.opcao_adicional_produto_id = opcao_adicional_produto.id
                    <if test="idEmpresa != null">
                      left join opcao_na_empresa on (opcao_na_empresa.opcao_id = opcao_adicional_produto.id and opcao_na_empresa.empresa_id = #{idEmpresa})
                    </if>
                    left join dependencia_opcao_adicional on dependencia_opcao_adicional.opcao_id = opcao_adicional_produto.id
                    left join produto_template_adicional on produto_template_adicional.id = adicional_produto.produto_template_adicional_id
                    left join produto_template_opcao on produto_template_opcao.id = opcao_adicional_produto.produto_template_opcao_id
                    left join produto_template_tamanho produto_template_tamanho_opcao on produto_template_tamanho_opcao.id = produto_template_opcao.tamanho_id
    where produto.id in

    <foreach item="id" collection="ids" open="(" separator="," close=")">
      #{id}
    </foreach>
      order by produto.id, produto_adicional_produto.ordem, opcao_adicional_produto.disponivel desc;
  </select>



  <insert id="insira" parameterType="map" useGeneratedKeys="true">
    insert into adicional_produto (nome, obrigatorio, qtd_minima, qtd_maxima, pode_repetir_item, tipo, produto_id,
                                   tipo_de_cobranca, produto_template_adicional_id, entidade, catalogo_id, classe, ordem,
    campo_ordenar, codigo_ifood, combo, oculto) values
    (#{nome}, #{obrigatorio}, #{qtdMinima}, #{qtdMaxima}, #{podeRepetirItem}, #{tipo}, #{produto.id}, #{tipoDeCobranca}, #{template.id},
    #{entidade}, #{catalogo.id}, #{classe}, #{ordem}, #{campoOrdenar}, #{codigoIfood}, #{combo}, #{oculto})
  </insert>

  <insert id="insiraAdicionalAoProduto" parameterType="map" useGeneratedKeys="true">

    INSERT INTO produto_adicional_produto (objeto_id, adicional_produto_id, catalogo_id, ordem)
    SELECT #{produto.id},#{adicionalProduto.id}, #{catalogo.id}, COALESCE(MAX(ordem) + 1, 0)
         FROM produto_adicional_produto
             WHERE objeto_id = #{produto.id}   AND catalogo_id =  #{catalogo.id}


  </insert>

  <select id="existeAdicionalNoProduto" parameterType="map" resultType="int">
    select count(*) total
     from produto_adicional_produto
        where objeto_id = #{produto.id}  and adicional_produto_id = #{adicionalProduto.id}  AND catalogo_id =  #{catalogo.id}
  </select>

  <update id="atualize" parameterType="map">
    update  adicional_produto set nome = #{nome},
    obrigatorio = #{obrigatorio},
    qtd_minima = #{qtdMinima},
    qtd_maxima = #{qtdMaxima},
    pode_repetir_item = #{podeRepetirItem},
    tipo_de_cobranca = #{tipoDeCobranca},
    tipo = #{tipo},
    classe = #{classe},
    ordem = #{ordem},
    campo_ordenar = #{campoOrdenar},
    compartilhado = #{compartilhado}
    where id = #{id}
  </update>

  <update id="reordene">
    <foreach item="adicional" collection="dados" open="" separator=";" close="">
      UPDATE produto_adicional_produto SET ordem = #{adicional.ordem} WHERE
      produto_adicional_produto.adicional_produto_id = #{adicional.id} and
      produto_adicional_produto.objeto_id = #{adicional.objeto.id} and
      produto_adicional_produto.catalogo_id = #{catalogo.id}
    </foreach>
  </update>

  <update id="desassocieAdicional">
    DELETE FROM produto_adicional_produto WHERE
    produto_adicional_produto.adicional_produto_id = #{id} and
    produto_adicional_produto.objeto_id = #{produto.id} and
    produto_adicional_produto.catalogo_id = #{catalogo.id};
  </update>

  <update id="remova" parameterType="map">
    DELETE FROM produto_adicional_produto WHERE
      produto_adicional_produto.adicional_produto_id = #{id} and
      produto_adicional_produto.objeto_id = #{idProduto} and
    produto_adicional_produto.catalogo_id = #{idCatalogo};

    UPDATE produto_adicional_produto pap JOIN
      (SELECT objeto_id, adicional_produto_id, catalogo_id, ordem, ROW_NUMBER() OVER w - 1 as num_ordem
       FROM produto_adicional_produto
       where objeto_id = #{idProduto}
        and catalogo_id = #{idCatalogo}
         WINDOW w AS (ORDER BY ordem)) nova_ordem
      ON (pap.objeto_id = nova_ordem.objeto_id and pap.adicional_produto_id = nova_ordem.adicional_produto_id
        and pap.catalogo_id = nova_ordem.catalogo_id)
    SET pap.ordem= nova_ordem.num_ordem;

    update opcao_adicional_produto, adicional_produto
        set opcao_adicional_produto.excluido = true, adicional_produto.excluido = true
      where   opcao_adicional_produto.adicional_produto_id = adicional_produto.id and
              adicional_produto_id   = #{id};
  </update>


</mapper>
