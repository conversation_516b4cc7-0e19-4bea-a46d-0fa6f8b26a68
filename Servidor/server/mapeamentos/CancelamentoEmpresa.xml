<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cancelamentoEmpresa">

  <resultMap id="cancelamentoEmpresaRM" type="CancelamentoEmpresa">
    <id property="id" column="cancelamento_empresa_id"/>

    <result property="motivo" column="cancelamento_empresa_motivo"/>
    <result property="horario" column="cancelamento_empresa_horario"/>

    <association property="operador"   resultMap="usuario.operadorResultMap"/>
    <association property="empresa"   resultMap="empresa.empresaRM"/>

    <collection property="motivosCancelamento" resultMap="motivocancelamento.motivocancelamentoRM"></collection>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="cancelamentoEmpresaRM" prefix="true">
    select *
      from cancelamento_empresa join empresa on empresa.id = empresa_id
                        left join usuario operador on operador.id = operador_id
                        left join endereco on endereco.id = empresa.endereco_id
                        left join cidade on cidade.id = endereco.cidade_id
                        left join estado on estado.id = cidade.estado_id
                        left join cancelamento_empresa_motivo on cancelamento_empresa_id = cancelamento_empresa.id
                        left join motivo_cancelamento_empresa on motivo_cancelamento_empresa.id  = motivo_id

      where cancelamento_empresa.ativo = true
            <if test="ativadas">
               and exists (select 1 from contrato  where empresa.id = empresa_id and data_ativacao is not null)
            </if>
            <if test="oportunidades">
              and exists (select 1 from contrato  where empresa.id = empresa_id and data_ativacao is null)
            </if>

    <if test="dataCancelamento">
         and  DATEDIFF(horario,#{dataCancelamento}) >= 0
    </if>

            order by horario desc
  </select>

  <update id="remova">
     update cancelamento_empresa set  ativo = false, horario_desativacao  = now(),
    operador_desativou_id = #{operador.id}
          where id = #{id}
  </update>
</mapper>
