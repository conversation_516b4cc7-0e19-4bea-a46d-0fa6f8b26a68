<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="transporte">
  <resultMap id="transporteRM" type="Transporte">
      <!-- propriedades vindas de Transporte.ts-->
    <id property="id" column="transporte_id"/>

    <result property="cnpj" column="transporte_cnpj"/>
    <result property="cpf" column="transporte_cpf"/>
    <result property="nome" column="transporte_nome"/>
    <result property="inscricaoEstadual" column="transporte_inscricao_estadual"/>
    <result property="enderecoCompleto" column="transporte_endereco_completo"/>
    <result property="nomeMunicipio" column="transporte_nome_municipio"/>
    <result property="siglaUF" column="transporte_sigla_uf"/>
    <result property="valorDoServico" column="transporte_valor_do_servico"/>

    <result property="baseCalcICMS" column="transporte_base_calc_icms"/>
    <result property="aliquotaICMS" column="transporte_aliquota_icms"/>
    <result property="valorICMS" column="transporte_valor_icms"/>
    <result property="cfop" column="transporte_cfop"/>
    <result property="municipioFatorGerador" column="transporte_municipio_fator_gerador"/>
    <result property="qtdeDeVolumes" column="transporte_qtde_de_volumes"/>
    <result property="especieVolumes" column="transporte_especie_volumes"/>
    <result property="marcaVolumes" column="transporte_marca_volumes"/>
    <result property="numeracaoVolumes" column="transporte_numeracao_volumes"/>
    <result property="pesoLiquido" column="transporte_peso_liquido"/>
    <result property="pesoBruto" column="transporte_peso_bruto"/>

    <association property="veiculoPrincipal" resultMap="veiculo.veiculoRM"/>

  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists transporte (
      id bigint(20) primary key,
      cnpj varchar(20),
      cpf varchar(20),
      nome varchar(100),
      inscricao_estadual varchar(20),
      endereco_completo varchar(100),
      nome_municipio varchar(100),
      sigla_uf varchar(2),
      valor_do_servico decimal(10,2),
      base_calc_icms decimal(10,2),
      aliquota_icms decimal(10,2),
      valor_icms decimal(10,2),
      cfop varchar(10),
      municipio_fator_gerador varchar(100),
      qtde_de_volumes int,
      especie_volumes varchar(100),
      marca_volumes varchar(100),
      numeracao_volumes varchar(100),
      peso_liquido decimal(10,2),
      peso_bruto decimal(10,2),
      veiculo_principal_id bigint(20),
      foreign key (veiculo_principal_id) references veiculo(id)
    );
  </create>
</mapper>
