<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="notificacaopedido">
  <resultMap id="notificacaompResultMap" type="NotificacaoPedido">
    <id property="id" column="notificacao_pedido_id"/>


    <result property="codigo" column="notificacao_pedido_codigo" />
    <result property="status" column="notificacao_pedido_status" />
    <result property="horario" column="notificacao_pedido_horario" />
    <result property="horarioNotificado" column="notificacao_pedido_horario_notificado" />
    <result property="origem" column="notificacao_pedido_origem" />
    <result property="dados" column="notificacao_pedido_dados" />
    <result property="executada" column="notificacao_pedido_executada" />
    <result property="erro" column="notificacao_pedido_erro" />
    <result property="ignorar" column="notificacao_pedido_ignorar" />
    <result property="pedidoId" column="notificacao_pedido_pedido_id" />
    <result property="empresaId" column="notificacao_pedido_empresa_id" />

    <discriminator javaType="String" column="notificacao_pedido_origem" >

      <case value="foodydelivery" resultType="NotificacaoPedidoFoodyDelivery"></case>
      <case value="ecletica" resultType="NotificacaoPedidoEcletica"></case>
      <case value="gcom" resultType="NotificacaoPedidoGcom"></case>
      <case value="bluesoft" resultType="NotificacaoPedidoBluesoft"></case>
      <case value="saipos" resultType="NotificacaoPedidoSaipos"></case>

    </discriminator>

  </resultMap>


  <select id="selecione" parameterType="map" resultMap="notificacaompResultMap" prefix="true">
    select *
        from notificacao_pedido
          where
          <choose>
            <when test="id">
              id = #{id}
            </when>
            <when test="naoExecutadas">
              executada is not true and ignorar is not true and TIMESTAMPDIFF(MINUTE,horario,now()) > 10

              <if test="status">
                and status = #{status}
              </if>
              order by horario
            </when>
          </choose>

  </select>

  <insert id="insira">
    insert into notificacao_pedido (id, pedido_id, empresa_id, origem, status, codigo, horario, horario_notificado, dados, ignorar)
    values (#{id}, #{pedidoId}, #{empresaId}, #{origem},  #{status}, #{codigo}, #{horario}, #{horarioNotificado}, #{dados}, #{ignorar})
    ON DUPLICATE KEY UPDATE dados = #{dados};
  </insert>


  <update id="atualize">
    update notificacao_pedido
    set executada = #{executada}, erro = #{erro}, ignorar = #{ignorar}
    where id = #{id}

  </update>

</mapper>
