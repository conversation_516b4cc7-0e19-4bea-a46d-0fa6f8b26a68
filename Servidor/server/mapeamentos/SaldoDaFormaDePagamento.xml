<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="saldoDaFormaDePagamento">
  <resultMap id="saldoDaFormaDePagamentoRM" type="SaldoDaFormaDePagamento">
    <id property="id" column="saldo_da_forma_de_pagamento_id"/>

    <result property="saldoEmCentavos" column="saldo_da_forma_de_pagamento_saldo_em_centavos"/>
    <result property="ultimaAtualizacao" column="saldo_da_forma_de_pagamento_ultima_atualizacao"/>

    <association property="formaDePagamento" columnPrefix="saldo_" column="forma_de_pagamento_id" resultMap="formaDePagamento.formaDePagamentoRM"/>
    <association property="caixa" column="caixa_id" resultMap="caixa.caixaRM"/>
  </resultMap>

  <insert id="insira" parameterType="SaldoDaFormaDePagamento">
    insert into saldo_da_forma_de_pagamento
    (forma_de_pagamento_id, caixa_id, saldo_em_centavos, ultima_atualizacao)
    values
    (#{formaDePagamento.id}, #{caixa.id}, #{saldoEmCentavos}, #{ultimaAtualizacao})
  </insert>

  <insert id="insiraLista" parameterType="list">
    insert into saldo_da_forma_de_pagamento
    (forma_de_pagamento_id, caixa_id, saldo_em_centavos, ultima_atualizacao)
    values
    <foreach collection="list" item="saldoDaFormaDePagamento" separator=",">
      (#{saldoDaFormaDePagamento.formaDePagamento.id}, #{saldoDaFormaDePagamento.caixa.id},
      #{saldoDaFormaDePagamento.saldoEmCentavos}, #{saldoDaFormaDePagamento.ultimaAtualizacao})
    </foreach>
  </insert>

  <update id="atualize" parameterType="SaldoDaFormaDePagamento">
    update saldo_da_forma_de_pagamento
    set saldo_em_centavos = #{saldoEmCentavos},
      ultima_atualizacao = #{ultimaAtualizacao}

    where id = #{id}
  </update>

</mapper>


