<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="telefoneCloudWhatsapp">
  <resultMap id="telefoneCloudWhatsappRM" type="TelefoneCloudWhatsapp">
    <id property="id" column="telefone_cloud_whatsapp_id"/>

    <result property="nome" column="telefone_cloud_whatsapp_nome"/>
    <result property="accessTokenUsuarioDeSistema" column="telefone_cloud_whatsapp_access_token_usuario_de_sistema"/>

    <result property="telefone" column="telefone_cloud_whatsapp_telefone"/>
    <result property="rating" column="telefone_cloud_whatsapp_rating"/>
    <result property="whatsapp_id" column="telefone_cloud_whatsapp_whatsapp_id"/>
    <result property="waba_id" column="telefone_cloud_whatsapp_waba_id"/>
    <result property="data_criacao" column="telefone_cloud_whatsapp_data_criacao"/>
    <result property="status" column="telefone_cloud_whatsapp_status"/>

    <association property="empresa" resultMap="empresa.empresaRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="telefoneCloudWhatsappRM" prefix="true">
    select *
    from telefone_cloud_whatsapp join empresa on(telefone_cloud_whatsapp.empresa_id = empresa.id)
    where
    <if test="idEmpresa != null">
      telefone_cloud_whatsapp.empresa_id = #{idEmpresa}
    </if>
    <if test="idEmpresa == null">
      1 = 1
    </if>
    <if test="id != null">
      and telefone_cloud_whatsapp.id = #{id}
    </if>
    <if test="whatsapp_id != null">
      and telefone_cloud_whatsapp.whatsapp_id = #{whatsapp_id}
    </if>
    order by telefone_cloud_whatsapp.id desc limit 1
  </select>

  <update id="atualize" parameterType="TelefoneCloudWhatsapp">
    update telefone_cloud_whatsapp set
    nome = #{nome},
    access_token_user = #{accessTokenUsuarioDeSistema},
    rating = #{rating},
    waba_id = #{waba_id},
    data_criacao = #{dataCriacao},
    status = #{status}
    where
    id = #{id}
    and telefone_cloud_whatsapp.empresa_id = #{empresa.id}
  </update>

  <update id="remova">
    delete from telefone_cloud_whatsapp where
    id = #{id}
    and empresa_id = #{empresa.id};
  </update>
</mapper>
