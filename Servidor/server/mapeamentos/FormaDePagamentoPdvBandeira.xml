<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="formaDePagamentoPdvBandeira">
  <resultMap id="formaDePagamentoPdvBandeiraRM" type="FormaDePagamentoPdvBandeira">
    <id property="id" column="forma_de_pagamento_pdv_bandeira_id"/>

    <result property="ativo" column="forma_de_pagamento_pdv_bandeira_ativo"/>

    <association  property="bandeira"   resultMap="bandeira.bandeiraRM"/>
  </resultMap>

  <update id="atualize">
    update forma_de_pagamento_pdv_bandeira
    set  ativo = #{ativo}   where id = #{id}
  </update>

</mapper>
