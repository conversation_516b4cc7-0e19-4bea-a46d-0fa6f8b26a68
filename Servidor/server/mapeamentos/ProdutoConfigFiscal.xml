<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="produtoConfigFiscal">
  <resultMap id="produtoConfigFiscalRM" type="ProdutoConfigFiscal">
    <id property="id" column="produto_config_fiscal_id" />
    <result property="ncm" column="produto_config_fiscal_ncm" />
    <result property="gtin" column="produto_config_fiscal_gtin" />
    <result property="cest" column="produto_config_fiscal_cest" />
    <result property="unidade" column="produto_config_fiscal_unidade" />
    <result property="producaoPropria" column="produto_config_fiscal_producao_propria" />
    <result property="cnpjProdutor" column="produto_config_fiscal_cnpj_produtor" />
    <result property="tipoCalculoIPI" column="produto_config_fiscal_tipo_calculo_ipi" />
    <result property="aliquotaIPI" column="produto_config_fiscal_aliquota_ipi" />
    <result property="valorPorUnidadeIPI" column="produto_config_fiscal_valor_por_unidade_ipi" />
    <result property="quantidadeSeloDeControleIPI" column="produto_config_fiscal_quantidade_selo_de_controle_ipi" />
    <result property="ipiCompoeBaseCalculoICMS" column="produto_config_fiscal_ipi_compoe_base_calculo_icms" />
    <result property="aliquotaAtributosAproximados" column="produto_config_fiscal_aliquota_atributos_aproximados" />
    <result property="informacoesAdicionais" column="produto_config_fiscal_informacoes_adicionais" />

    <association property="origem" resultMap="origemProduto.origemProdutoRM" />
    <!--<association property="produto" resultMap="produto.produtoResultMap" />-->
    <association property="tipoDeTributacaoIPI" resultMap="tipoDeTributacaoIPI.tipoDeTributacaoIPIRM" />
    <association property="seloDeControleIPI" resultMap="seloDeControleIPI.seloDeControleIPIRM" />


  </resultMap>

  <select id="selecione" parameterType="map" resultMap="produtoConfigFiscalRM" prefix="true">
    select * from produto_config_fiscal
    left join origem_produto on origem_produto.id = produto_config_fiscal.origem_produto_id
    left join tipo_de_tributacao_ipi on tipo_de_tributacao_ipi.id = produto_config_fiscal.tipo_de_tributacao_ipi_id
    left join selo_de_controle_ipi on selo_de_controle_ipi.id = produto_config_fiscal.selo_de_controle_ipi_id

    where removido is not true
    <if test="id">
      and id = #{id}
    </if>
    <if test="produtoId">
      and produto_id = #{produtoId}
    </if>
  </select>

  <insert id="insira" parameterType="map">
    insert into produto_config_fiscal( produto_id, origem_produto_id, ncm, gtin, cest, unidade, producao_propria, cnpj_produtor, tipo_calculo_ipi, aliquota_ipi,
    valor_por_unidade_ipi, quantidade_selo_de_controle_ipi, ipi_compoe_base_calculo_icms, aliquota_atributos_aproximados, informacoes_adicionais, tipo_de_tributacao_ipi_id, selo_de_controle_ipi_id)
    values(#{produto.id}, #{origem.id}, #{ncm}, #{gtin}, #{cest}, #{unidade}, #{producaoPropria}, #{cnpjProdutor}, #{tipoCalculoIPI}, #{aliquotaIPI},
    #{valorPorUnidadeIPI}, #{quantidadeSeloDeControleIPI}, #{ipiCompoeBaseCalculoICMS}, #{aliquotaAtributosAproximados}, #{informacoesAdicionais}, #{tipoDeTributacaoIPI.id}, #{seloDeControleIPI.id})
  </insert>

  <update id="atualize" parameterType="map">
    update produto_config_fiscal
    set produto_id = #{produto.id},
    origem_produto_id = #{origem.id},
    ncm = #{ncm},
    gtin = #{gtin},
    cest = #{cest},
    unidade = #{unidade},
    producao_propria = #{producaoPropria},
    cnpj_produtor = #{cnpjProdutor},
    tipo_calculo_ipi = #{tipoCalculoIPI},
    aliquota_ipi = #{aliquotaIPI},
    valor_por_unidade_ipi = #{valorPorUnidadeIPI},
    quantidade_selo_de_controle_ipi = #{quantidadeSeloDeControleIPI},
    ipi_compoe_base_calculo_icms = #{ipiCompoeBaseCalculoICMS},
    aliquota_atributos_aproximados = #{aliquotaAtributosAproximados},
    informacoes_adicionais = #{informacoesAdicionais},
    tipo_de_tributacao_ipi_id = #{tipoDeTributacaoIPI.id},
    selo_de_controle_ipi_id = #{seloDeControleIPI.id}
    where id = #{id}
  </update>

  <update id="remova" parameterType="map">
    update produto_config_fiscal set removido = true where id = #{id}
  </update>

</mapper>
