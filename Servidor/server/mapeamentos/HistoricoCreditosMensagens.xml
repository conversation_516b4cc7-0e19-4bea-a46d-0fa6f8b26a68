<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="historicoCreditosMensagens">
  <resultMap id="historicoCreditosMensagensRM" type="mensagens.HistoricoCreditosMensagens">
    <id property="id" column="campanha_id"/>

    <result property="data" column="historico_creditos_mensagens_data"/>
    <result property="referencia" column="historico_creditos_mensagens_referencia"/>
    <result property="qtde" column="historico_creditos_mensagens_qtde"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="historicoCreditosMensagensRM" prefix="true">
    select *
      from historico_creditos_mensagens
        join empresa on (historico_creditos_mensagens.empresa_id = empresa.id)
        where historico_creditos_mensagens.empresa_id = #{idEmpresa}
        <choose>
          <when test="referencia != null">
            and historico_creditos_mensagens.referencia = #{referencia}
          </when>
        </choose>
  </select>
</mapper>
