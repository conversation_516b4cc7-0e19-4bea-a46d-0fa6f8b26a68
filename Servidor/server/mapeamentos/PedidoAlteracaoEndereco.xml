<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pedidoAlteracaoEndereco">

  <resultMap id="pedidoAlteracaoEnderecoRM" type="PedidoAlteracaoEndereco">
    <id property="id" column="pedido_alteracao_endereco_id"/>

    <result property="horario" column="pedido_alteracao_endereco_horario" />
    <result property="horario" column="pedido_alteracao_endereco_horario" />
    <result property="orderId" column="pedido_alteracao_endereco_order_id" />
    <result property="metadata" column="pedido_alteracao_endereco_metadata" />
    <result property="dataExpiracao" column="pedido_alteracao_endereco_data_expiracao" />
    <result property="longtext" column="pedido_alteracao_endereco_longtext" />
    <result property="aceito" column="pedido_alteracao_endereco_aceito" />
    <result property="acao" column="pedido_alteracao_endereco_acao" />

    <association property="pedido" resultMap="pedido.pedidoRM"/>
  </resultMap>


  <resultMap id="alteracaoEnderecoDoPedidoRM" type="PedidoAlteracaoEndereco">
    <id property="id" column="pedido_alteracao_endereco_id"/>

    <result property="horario" column="pedido_alteracao_endereco_horario" />
    <result property="orderId" column="pedido_alteracao_endereco_order_id" />
    <result property="metadata" column="pedido_alteracao_endereco_metadata" />
    <result property="dataExpiracao" column="pedido_alteracao_endereco_data_expiracao" />
    <result property="longtext" column="pedido_alteracao_endereco_longtext" />
    <result property="aceito" column="pedido_alteracao_endereco_aceito" />
    <result property="acao" column="pedido_alteracao_endereco_acao" />

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="pedidoAlteracaoEnderecoRM" prefix="true">
    select * from  pedido_alteracao_endereco join pedido on pedido.id  = pedido_id
    where   pedido_alteracao_endereco.empresa_id= #{idEmpresa} and pedido_alteracao_endereco.aceito is null and data_expiracao > now()

  </select>


  <update id="atualize">
    update pedido_alteracao_endereco
      set aceito = #{aceito},operador_id = #{operador.id} , horario_resposta = #{horarioResposta}
      where id  = #{id} and empresa_id = #{empresa.id}
  </update>


</mapper>
