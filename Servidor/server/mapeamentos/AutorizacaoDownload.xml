<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="autorizacaoDownload">
  <resultMap id="autorizacaoDownloadRM" type="AutorizacaoDownload">
    <id property="id" column="autorizacao_download_id"/>
    <result property="cnpj" column="autorizacao_download_cnpj"/>
    <result property="cpf" column="autorizacao_download_cpf"/>

    <association property="notaFiscalEletronica" column="nota_fiscal_eletronica_id" resultMap="notaFiscalEletronica.notaFiscalEletronicaRM"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists autorizacao_download (
      id bigint(20) primary key,
      cnpj varchar(14),
      cpf varchar(11),
      nota_fiscal_eletronica_id bigint(20),
      foreign key (nota_fiscal_eletronica_id) references nota_fiscal_eletronica (id)
    );
  </create>
</mapper>
