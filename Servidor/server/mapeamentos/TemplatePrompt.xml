<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="templatePrompt">
  <resultMap id="templatePromptRM" type="TemplatePrompt">
    <id property="id" column="template_prompt_id"/>

    <result property="nome" column="template_prompt_nome"/>
    <result property="descricao" column="template_prompt_descricao"/>
    <result property="template" column="template_prompt_template"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="templatePromptRM" prefix="true">
    select *
    from template_prompt
    <if test="id != null">
      where template_prompt.id = #{id};
    </if>
    <if test="nome != null">
      where template_prompt.nome = #{nome};
    </if>
  </select>


  <update id="atualize" parameterType="map">
    update template_prompt
      set nome  = #{nome},
      descricao = #{descricao},
      template = #{template}
      where
        id = #{id}
  </update>
</mapper>
