<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="respostaChatbotInstagram">
  <resultMap id="respostaChatbotInstagramRM" type="RespostaChatbotInstagram">
    <id property="id" column="resposta_chatbot_instagram_id"/>
    
    <result property="empresa_id" column="resposta_chatbot_instagram_empresa_id"/>
    <result property="chaveResposta" column="resposta_chatbot_instagram_chave_resposta"/>
    <result property="titulo" column="resposta_chatbot_instagram_titulo"/>
    <result property="mensagem" column="resposta_chatbot_instagram_mensagem"/>
    <result property="icone" column="resposta_chatbot_instagram_icone"/>
    <result property="ativa" column="resposta_chatbot_instagram_ativa"/>
    <result property="ordem" column="resposta_chatbot_instagram_ordem"/>
    
    <association property="dadosInstagram" resultMap="dadosInstagram.dadosInstagramRM"/>
    <collection property="opcoes" resultMap="opcaoRespostaChatbotInstagram.opcaoRespostaChatbotInstagramRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="respostaChatbotInstagramRM" prefix="true">
    select * from resposta_chatbot_instagram 
    left join dados_instagram on dados_instagram.id = resposta_chatbot_instagram.dados_instagram_id
    left join opcao_resposta_chatbot_instagram on opcao_resposta_chatbot_instagram.resposta_chatbot_instagram_id = resposta_chatbot_instagram.id
    where resposta_chatbot_instagram.empresa_id = #{idEmpresa}
    
    <if test="id">
      and resposta_chatbot_instagram.id = #{id}
    </if>
    
    <if test="chaveResposta">
      and resposta_chatbot_instagram.chave_resposta = #{chaveResposta}
    </if>
    
    <if test="ativa != null">
      and resposta_chatbot_instagram.ativa = #{ativa}
    </if>
    
    order by resposta_chatbot_instagram.ordem, resposta_chatbot_instagram.titulo
  </select>

  <insert id="insira" parameterType="map" useGeneratedKeys="true" keyProperty="id">
    insert into resposta_chatbot_instagram (
      empresa_id,
      chave_resposta, 
      titulo, 
      mensagem, 
      icone, 
      ativa, 
      ordem,
      dados_instagram_id
    ) values (
      #{empresa.id},
      #{chaveResposta}, 
      #{titulo}, 
      #{mensagem}, 
      #{icone}, 
      #{ativa}, 
      #{ordem},
      #{dadosInstagram.id}
    )
  </insert>

  <update id="atualize" parameterType="map">
    update resposta_chatbot_instagram set
      chave_resposta = #{chaveResposta},
      titulo = #{titulo},
      mensagem = #{mensagem},
      icone = #{icone},
      ativa = #{ativa},
      ordem = #{ordem},
      dados_instagram_id = #{dadosInstagram.id}
    where id = #{id} 
    and empresa_id = #{empresa.id}
  </update>

  <delete id="remova" parameterType="map">
    delete from resposta_chatbot_instagram 
    where id = #{id} 
    and empresa_id = #{empresa.id}
  </delete>

  <select id="existe" parameterType="map" resultType="int">
    select count(*) total
    from resposta_chatbot_instagram 
    where empresa_id = #{empresa.id} 
    and chave_resposta = #{chaveResposta}
    <if test="id">
      and id != #{id}
    </if>
  </select>
</mapper> 