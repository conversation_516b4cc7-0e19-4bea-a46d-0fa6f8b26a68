<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="entregador">
  <resultMap id="entregadorResultMap" type="Entregador">
    <id property="id" column="entregador_id"/>
    <result property="nome" column="entregador_nome"/>
    <result property="email" column="entregador_email"/>
    <result property="telefone" column="entregador_telefone" />
    <result property="codigo" column="entregador_codigo" />
    <result property="ativo" column="entregador_ativo" />
  </resultMap>



  <select id="selecione" parameterType="map" resultMap="entregadorResultMap" prefix="true">
    select * from entregador
    where  entregador.empresa_id = #{idEmpresa}
    <if test="id != null">
      and   entregador.id = #{id}
    </if>
    <if test="nome">
      and entregador.nome like #{nome}
    </if>
    <if test="apenasAtivos != null">
      and entregador.ativo is true
    </if>
    and entregador.excluido is not true
    order by entregador.nome
  </select>

  <resultMap id="relatorioEntregasRM" type="RelatorioEntregas">
    <id property="id" column="relatorio_entregas_id"/>
    <result property="idEntregador" column="relatorio_entregas_id_entregador" />
    <result property="nomeEntregador" column="relatorio_entregas_nome"/>
    <result property="inicio" column="relatorio_entregas_inicio" />
    <result property="fim" column="relatorio_entregas_fim" />
    <result property="qtdCorridas" column="relatorio_entregas_qtd_corridas" />
    <result property="totalTaxas" column="relatorio_entregas_total_taxas" />
    <result property="qtdEntregasGratis" column="relatorio_entregas_entregas_gratis"/>
    <result property="totalDescontoTaxa" column="relatorio_entregas_desconto_taxa" />
  </resultMap>

  <resultMap id="relatorioPedidosEntregadorRM" type="RelatorioPedidoEntregador">
    <id property="id" column="relatorio_pedidos_entregador_id"/>
    <result property="guidPedido" column="relatorio_pedidos_entregador_guid" />
    <result property="codigoPedido" column="relatorio_pedidos_entregador_codigo" />
    <result property="valorPedido" column="relatorio_pedidos_entregador_valor" />
    <result property="taxaEntrega" column="relatorio_pedidos_entregador_taxa_entrega" />
    <result property="descontoTaxaEntrega" column="relatorio_pedidos_entregador_desconto_taxa_entrega" />
    <result property="ultimaAtualizacao" column="relatorio_pedidos_entregador_ultima_atualizacao" />
    <result property="nomeCliente" column="relatorio_pedidos_entregador_nome_cliente" />
    <result property="telefoneCliente" column="relatorio_pedidos_entregador_telefone_cliente" />

    <association property="enderecoEntrega"   resultMap="endereco.enderecoRM"/>
    <association property="entregador"   resultMap="entregador.entregadorResultMap"/>
  </resultMap>

  <select id="selecioneRelatorioPedidosEntregador" parameterType="map" resultMap="relatorioPedidosEntregadorRM">
    select p.id relatorio_pedidos_entregador_id,
           p.guid relatorio_pedidos_entregador_guid,
           p.codigo relatorio_pedidos_entregador_codigo,
           p.valor relatorio_pedidos_entregador_valor,
           p.taxa_entrega relatorio_pedidos_entregador_taxa_entrega,
           p.horario_atualizacao  relatorio_pedidos_entregador_ultima_atualizacao,
           p.desconto_taxa_entrega relatorio_pedidos_entregador_desconto_taxa_entrega,
           e.id endereco_id,
           e.bairro endereco_bairro,
           e.cep endereco_cep,
           e.localidade endereco_localidade,
           e.localizacao endereco_localizacao,
           e.numero endereco_numero,
           e.logradouro endereco_logradouro,
           cid.id cidade_id,
           cid.nome cidade_nome,
           est.id estado_id,
           est.nome estado_nome,
           c.nome relatorio_pedidos_entregador_nome_cliente,
           c.telefone relatorio_pedidos_entregador_telefone_cliente,
           ent.id entregador_id
    from pedido p
         join endereco e on e.id = p.endereco_id
         join cidade cid on e.cidade_id = cid.id
         join estado est on cid.estado_id = est.id
         join contato c on c.id = p.contato_id
         join entregador ent on ent.id = p.entregador_id
    where p.empresa_id = #{idEmpresa}
      and entregador_id = #{entregador.id}
      <if test="horario">
        and horario &gt;= #{inicio}
        and horario &lt;= #{fim}
      </if>
  </select>


  <select id="selecioneRelatorioEntregas" parameterType="map" resultMap="relatorioEntregasRM" >
    select (@cnt := @cnt + 1) relatorio_entregas_id,
    entregador_id relatorio_entregas_id_entregador, entregador.nome relatorio_entregas_nome, min(horario_atualizacao)
    relatorio_entregas_inicio, max(horario_atualizacao) relatorio_entregas_fim,
    count(*) relatorio_entregas_qtd_corridas, sum(taxa_entrega) relatorio_entregas_total_taxas,
    sum(if(taxa_entrega = 0,true,false)) relatorio_entregas_entregas_gratis,
    sum(desconto_taxa_entrega) relatorio_entregas_desconto_taxa
    from pedido join entregador on entregador.id = pedido.entregador_id
    CROSS JOIN (SELECT @cnt := 0) AS dummy
    where
    entregador.empresa_id = #{idEmpresa}
     and horario &gt;= #{inicio}
     and horario &lt;= #{fim}
    <if test="entregador">
      and entregador.id = #{entregador.id}
    </if>
    group by entregador_Id
   </select>

  <update id="atualize">
    update entregador
    set  nome = #{nome},
       codigo = #{codigo},
        email = #{email},
     telefone = #{telefone},
        ativo = #{ativo}
    where  id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="remova">
    update entregador set excluido =  true where id = #{id} and empresa_id = #{empresa.id}
  </update>
</mapper>
