<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ifoodUsercode">
  <resultMap id="ifoodUsercodeRM" type="IfoodUserCode">
    <id property="id" column="ifood_user_code_id"/>

    <result property="userCode" column="ifood_user_code_user_code"/>
    <result property="verificationUrl" column="ifood_user_code_verification_url"/>
    <result property="authorizationCodeVerifier" column="ifood_user_code_authorization_code_verifier"/>
    <result property="expiresIn" column="ifood_user_code_expires_in"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="ifoodUsercodeRM" prefix="true">
    select * from ifood_user_code where empresa_id  = #{idEmpresa}
      <if test="pendente">
        and not exists (select 1 from integracao_ifood where ifood_user_code.user_code = integracao_ifood.codigo_autorizacao )
      </if>
  </select>


</mapper>
