<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="instituicaoDePagamento">

  <resultMap id="instituicaoDePagamentoRM" type="InstituicaoDePagamento">
    <id property="id" column="instituicao_de_pagamento_id"/>
    <result property="nome" column="instituicao_de_pagamento_nome"/>
    <result property="cnpj" column="instituicao_de_pagamento_cnpj"/>
    <result property="meioDePagamento" column="instituicao_de_pagamento_meio_de_pagamento"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="instituicaoDePagamentoRM" prefix="true">
    select * from instituicao_de_pagamento
    where 1 = 1
    <if test="id != null">
      and instituicao_de_pagamento_id = #{id}
    </if>
    <if test="meioDePagamento != null">
      and instituicao_de_pagamento_meio_de_pagamento = #{meioDePagamento}
    </if>
  </select>

</mapper>
