<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="requestParceiro">
  <resultMap id="requestParceiroRM" type="RequestParceiro">
    <id property="id" column="request_parceiro_id"/>

    <result property="sistema" column="request_parceiro_sistema"/>
    <result property="header" column="request_parceiro_header"/>
    <result property="payload" column="request_parceiro_payload"/>
    <result property="retorno" column="request_parceiro_retorno"/>
    <result property="horario" column="request_parceiro_horario"/>
    <result property="httpstatus" column="request_parceiro_httpstatus"/>
    <result property="erro" column="request_parceiro_erro"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="requestParceiroRM" prefix="true">
    select  *  from request_parceiro where empresa_id = #{idEmpresa} and pedido_id = #{idPedido} order by id desc

  </select>
</mapper>
