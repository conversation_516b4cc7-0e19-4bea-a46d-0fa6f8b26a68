<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="planoVantagem">
  <resultMap id="planoVantagemRM" type="PlanoVantagem">
    <id property="id" column="plano_vantagem_id"/>

    <result property="ordem" column="plano_vantagem_ordem"/>
    <result property="disponivel" column="plano_vantagem_disponivel"/>

    <association property="vantagem"   resultMap="vantagem.vantagemRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="planoVantagemRM" prefix="true">
    select plano_vantagem.*
         from  plano_vantagem where id = #{id}
  </select>



  <update id="atualize"  parameterType="map">
    update plano_vantagem
      set ordem = #{ordem}, disponivel = #{disponivel}
          where id = #{id}
  </update>

  <update id="removaPlanoVantagem"  parameterType="map">
    delete from  plano_vantagem   where id = #{id}
  </update>

  <update id="atualizeOrdemAposRemocao"  parameterType="map">
    update    plano_vantagem set ordem = ordem - 1
        where plano_empresarial_id = #{idPlano} and ordem > #{ordem}
  </update>
</mapper>
