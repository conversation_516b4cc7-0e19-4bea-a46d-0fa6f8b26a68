<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

  <mapper namespace="origemProduto">
    <resultMap id="origemProdutoRM" type="OrigemProduto">
      <id property="id" column="origem_produto_id"/>
      <result property="codigo" column="origem_produto_codigo"/>
      <result property="descricao" column="origem_produto_descricao"/>
    </resultMap>

    <select id="selecione" parameterType="map" resultMap="origemProdutoRM" prefix="true">
      select * from origem_produto
      <if test="id">
        where origem_produto_id = #{id}
      </if>
      order by codigo;
    </select>

    <create id="crieTabela" parameterType="map">
      CREATE TABLE IF NOT EXISTS origem_produto (
      id BIGINT AUTO_INCREMENT PRIMARY KEY,
      codigo INT,
      descricao VARCHAR(255)
    )
    </create>
  </mapper>

