<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="uberdirect">
  <resultMap id="uberdirectRM" type="IntegracaoUberdirect">
    <id property="id" column="uberdirect_id"/>

    <result property="costumerId" column="uberdirect_costumer_id"/>
    <result property="clientId" column="uberdirect_client_id"/>
    <result property="clientSecret" column="uberdirect_client_secret"/>
    <result property="signingKey" column="uberdirect_signing_key"/>
    <result property="token" column="uberdirect_token"/>
    <result property="tokenDataExpiracao" column="uberdirect_token_data_expiracao"/>
    <result property="ativa" column="uberdirect_ativa"/>
    <result property="restaurante" column="uberdirect_restaurante"/>
    <result property="tempoPreparo" column="uberdirect_tempo_preparo"/>
    <result property="instrucoesRetirada" column="uberdirect_instrucoes_retirada"/>
    <result property="acaoretorno" column="uberdirect_acaoretorno"/>

  </resultMap>

  <update id="atualize">
    update integracao_uberdirect

     set client_id = #{clientId}, client_secret = #{clientSecret},costumer_id = #{costumerId},signing_key = #{signingKey},
        token = #{token}, restaurante = #{restaurante}, tempo_preparo = #{tempoPreparo},
    instrucoes_retirada = #{instrucoesRetirada}, token_data_expiracao = #{tokenDataExpiracao}, acaoretorno = #{acaoretorno}

    where id = #{id}   and empresa_id = #{empresa.id};

  </update>


  <update id="atualizeAtiva">
    update integracao_uberdirect
    set  ativa = #{ativa}
    where id = #{id}   and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeToken">
    update  integracao_uberdirect
    set token = #{token}, token_data_expiracao = #{tokenDataExpiracao}
    where empresa_id = #{empresa.id} and id = #{id}
  </update>



</mapper>

