<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="deliverypedido">
  <resultMap id="deliverypedidoRM" type="DeliveryPedido">
    <id property="id" column="delivery_pedido_id"/>

    <result property="origem" column="delivery_pedido_origem" />
    <result property="deliveryId" column="delivery_pedido_delivery_id" />
    <result property="status" column="delivery_pedido_status" />

    <result property="dados" column="delivery_pedido_dados" />
    <result property="foiAceita" column="delivery_pedido_foi_aceita" />
    <result property="erro" column="delivery_pedido_erro" />
    <result property="horario" column="delivery_pedido_horario" />
    <result property="enderecoConfirmado" column="delivery_endereco_confirmado" />


    <discriminator javaType="String" column="delivery_pedido_origem" >
      <case value="opendelivery" resultType="DeliveryPedidoOpendelivery"></case>
      <case value="uber" resultType="DeliveryPedidoUber"></case>
      <case value="foodydelivery" resultType="DeliveryPedidoFoodyDelivery"></case>
      <case value="ifood" resultType="DeliveryPedidoIfood"></case>
    </discriminator>

  </resultMap>


  <select id="selecione" parameterType="map" resultMap="deliverypedidoRM" prefix="true">
    select *  from  delivery_pedido
      where empresa_id = #{idEmpresa} and
      <choose>
        <when test="id">
          id = #{id}
        </when>

        <when test="deliveryId">
          delivery_id = #{deliveryId}
        </when>
        <when test="idPedido">
            pedido_id = #{idPedido}
        </when>
      </choose>

  </select>

  <insert id="insira" parameterType="map" useGeneratedKeys="true" keyProperty="id">
    insert into delivery_pedido(delivery_id,status,dados,foi_aceita,horario,origem,pedido_id,empresa_id)
        values (#{deliveryId},#{status},#{dados},#{foiAceita},#{horario},#{origem},#{pedido.id},#{empresa.id});
  </insert>

  <update id="atualizeRetorno">
    update delivery_pedido
      set status = #{status}, erro = #{erro}, foi_aceita = #{foiAceita}, dados = #{dados}
        where id = #{id} and empresa_id = #{empresa.id}

  </update>

  <update id="atualizeEnderecoConfirmado">
    update delivery_pedido
      set endereco_confirmado  = true
        where id = #{id} and empresa_id = #{empresa.id}

  </update>


  <update id="remova">
    update pedido set delivery_pedido_id = null
    where id = #{idPedido} and empresa_id = #{empresa.id}
  </update>

</mapper>
