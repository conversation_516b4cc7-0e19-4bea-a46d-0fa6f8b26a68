<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cartaoCliente">
    <resultMap id="cartaoClienteRM" type="CartaoCliente">
        <id property="id" column="cartao_cliente_id"/>

        <result property="codigo" column="cartao_cliente_codigo"/>
        <result property="ativo" column="cartao_cliente_ativo"/>
        <result property="dataCriacao" column="cartao_cliente_data_criacao"/>

      <association property="empresa" column="empresa_id" resultMap="empresa.empresaRM"/>
    </resultMap>

    <select id="selecionePorCodigo" resultMap="cartaoClienteRM">
        SELECT
            cc.id as cartao_cliente_id,
            cc.codigo as cartao_cliente_codigo,
            cc.ativo as cartao_cliente_ativo,
            cc.data_criacao as cartao_cliente_data_criacao,
            cc.empresa_id
        FROM cartao_cliente cc
        WHERE cc.codigo = #{codigo}
        AND cc.empresa_id = #{empresa.id}
        LIMIT 1
    </select>

    <select id="selecione" resultMap="cartaoClienteRM" prefix="true">
        SELECT
            *
        FROM cartao_cliente
        WHERE 1=1
        <if test="id != null">
            AND cartao_cliente.id = #{id}
        </if>
        <if test="idEmpresa != null">
            AND cartao_cliente.empresa_id = #{idEmpresa}
        </if>
        <if test="codigo != null">
            AND cartao_cliente.codigo = #{codigo}
        </if>
        ORDER BY cartao_cliente.codigo;
    </select>

    <insert id="insira" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO cartao_cliente (codigo, empresa_id, ativo, data_criacao)
        VALUES (
            #{codigo},
            #{empresa.id},
            #{ativo},
            NOW()
        )
    </insert>

    <update id="atualizeCartaoCliente">
        UPDATE cartao_cliente
        SET codigo = #{codigo},
            ativo = #{ativo}
        WHERE id = #{id}
        AND empresa_id = #{empresa.id}
    </update>

    <update id="removeCartaoCliente">
        UPDATE cartao_cliente
        SET ativo = false
        WHERE id = #{id}
        AND empresa_id = #{empresa.id}
    </update>
</mapper>
