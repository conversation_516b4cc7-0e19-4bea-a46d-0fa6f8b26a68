<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="historicoPedido">

  <resultMap id="historicoPedidoRM" type="HistoricoPedido">
    <id property="id" column="historico_pedido_id"/>

    <result property="horario" column="historico_pedido_horario"/>
    <result property="descricao" column="historico_pedido_descricao"/>

    <association property="clienteApi" resultMap="clienteApi.clienteApiResultMap"/>
    <association property="operador" columnPrefix="historico_" resultMap="usuario.operadorResultMap"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="historicoPedidoRM" prefix="true">
    select historico_pedido.* , historico_operador.*, cliente_api.*
        from historico_pedido join pedido on pedido.id = pedido_id
                      left join usuario historico_operador on historico_operador.id = historico_pedido.operador_id
          left join cliente_api on cliente_api.id = cliente_api_id
        where pedido.guid = #{guid}

  </select>
</mapper>
