<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="integracaoGatewayPagamento">
  <resultMap id="integracaoGatewayPagamentoRM" type="integracao.IntegracaoGatewayPagamento">
    <id property="id" column="integracao_gateway_pagamento_id"/>
    <result property="gateway" column="integracao_gateway_pagamento_gateway"/>
    <result property="publicKey" column="integracao_gateway_pagamento_public_key"/>
    <result property="privateKey" column="integracao_gateway_pagamento_private_key"/>
    <result property="refreshToken" column="integracao_gateway_pagamento_refresh_token"/>
    <result property="dataExpiracao" column="integracao_gateway_pagamento_data_expiracao"/>
    <result property="instalacaoId" column="integracao_gateway_pagamento_instalacao_id"/>
    <result property="lojaId" column="integracao_gateway_pagamento_loja_id"/>
    <result property="clienteId" column="integracao_gateway_pagamento_cliente_id"/>
    <result property="sandbox" column="integracao_gateway_pagamento_sandbox"/>
  </resultMap>

  <select id="selecione" resultMap="integracaoGatewayPagamentoRM" prefix="true">
    select * from integracao_gateway_pagamento
       where instalacao_id = #{instalacaoId}
  </select>

  <insert id="insiraNaEmpresa">
    insert into empresa_integracao_gateway_pagamento(empresa_id,integracao_gateway_pagamento_id, data_instalacao)
      values (#{idEmpresa}, #{idGateway}, #{dataInstalacao})
  </insert>

  <update id="remova">
     delete from empresa_integracao_gateway_pagamento where integracao_gateway_pagamento_id = #{id};
     delete from integracao_gateway_pagamento where id = #{id};
  </update>

  <update id="atualize">
     update integracao_gateway_pagamento set
    private_key = #{privateKey},
    public_key = #{publicKey},
    refresh_token = #{refreshToken},
    loja_id = #{lojaId},
    data_expiracao = #{dataExpiracao}
         where id = #{id}
  </update>

  <update id="atualizeChave">
    update integracao_gateway_pagamento set public_key = #{publicKey}
    where id = #{id}
  </update>
</mapper>



