<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="icmsUFDestino">
  <resultMap id="icmsUFDestinoRM" type="ICMSUFDestino">
    <id property="id" column="icms_uf_destino_id"/>
    <result property="valorBCDest" column="icms_uf_destino_valor_bc_dest"/>
    <result property="percentualFCPDest" column="icms_uf_destino_percentual_fcp_dest"/>
    <result property="aliquotaUFDest" column="icms_uf_destino_aliquota_uf_dest"/>
    <result property="aliquotaInterestadual" column="icms_uf_destino_aliquota_interestadual"/>
    <result property="percentualPartilha" column="icms_uf_destino_percentual_partilha"/>
    <result property="valorICMSFCP" column="icms_uf_destino_valor_icms_fcp"/>
    <result property="valorICMSDest" column="icms_uf_destino_valor_icms_dest"/>
    <result property="valorICMSRemet" column="icms_uf_destino_valor_icms_remet"/>
    <result property="valorBCFCPUFDest" column="icms_uf_destino_valor_bc_fcp_uf_dest"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists icms_uf_destino (
      id bigint(20) primary key,
      valor_bc_dest decimal(10,2),
      percentual_fcp_dest decimal(10,2),
      aliquota_uf_dest decimal(10,2),
      aliquota_interestadual decimal(10,2),
      percentual_partilha decimal(10,2),
      valor_icms_fcp decimal(10,2),
      valor_icms_dest decimal(10,2),
      valor_icms_remet decimal(10,2),
      valor_bc_fcp_uf_dest decimal(10,2)
    );
  </create>
</mapper>
