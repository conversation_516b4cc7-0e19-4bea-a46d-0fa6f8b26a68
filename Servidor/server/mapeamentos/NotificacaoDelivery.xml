<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="notificacaodelivery">
  <resultMap id="notificacaoDeliveryRM" type="NotificacaoDelivery">
    <id property="id" column="notificacao_delivery_id"/>

    <result property="tipo" column="notificacao_delivery_tipo" />
    <result property="horario" column="notificacao_delivery_horario" />

    <result property="dados" column="notificacao_delivery_dados" />
    <result property="executada" column="notificacao_delivery_executada" />
    <result property="erro" column="notificacao_delivery_erro" />
    <result property="ignorar" column="notificacao_delivery_ignorar" />
    <result property="deliveryId" column="notificacao_delivery_delivery_id" />
    <result property="pedidoId" column="notificacao_delivery_pedido_id" />
    <result property="empresaId" column="notificacao_delivery_empresa_id" />
    <result property="eventId" column="notificacao_delivery_event_id" />
    <result property="origem" column="notificacao_delivery_origem" />

    <discriminator javaType="String" column="notificacao_delivery_origem" >
      <case value="opendelivery" resultType="NotificacaoDeliveryOpendelivery"></case>
      <case value="uber" resultType="NotificacaoDeliveryUber"></case>
      <case value="foodydelivery" resultType="NotificacaoDeliveryFoodydelivery"></case>
      <case value="ifood" resultType="NotificacaoIfoodDelivery"></case>
    </discriminator>


  </resultMap>


  <select id="selecione" parameterType="map" resultMap="notificacaoDeliveryRM" prefix="true">
    select *
    from notificacao_delivery
    where
    <choose>
      <when test="id">
        id = #{id}
      </when>

      <when test="naoExecutadas">
        executada is not true and ignorar is not true and TIMESTAMPDIFF(MINUTE,horario,now()) > 10

        <if test="status">
          and status = #{status}
        </if>

        <if test="origem">
          and   origem = #{origem} and datediff(horario,now())  = 0
        </if>

        order by horario
      </when>

      <when test="origem">
        origem = #{origem}

        <if test="idPedido">
           and pedido_id = #{idPedido} order by id
        </if>
      </when>

    </choose>

  </select>

  <insert id="insira">
    insert into notificacao_delivery(tipo,delivery_id,horario,dados,executada,pedido_id,empresa_id,origem,event_id)
       values (#{tipo},#{deliveryId},#{horario},#{dados},false,#{pedidoId},#{empresaId},#{origem},#{eventId})
  </insert>

  <update id="atualize">
    update notificacao_delivery
    set executada = #{executada}, erro = #{erro}, ignorar = #{ignorar}
    where id = #{id}

  </update>

</mapper>
