<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="disponibilidade">
  <resultMap id="disponibilidadeRM" type="Disponibilidade">
    <id property="id" column="disponibilidade_id"/>

    <result property="dataInicio" column="disponibilidade_data_inicio"/>
    <result property="dataFim" column="disponibilidade_data_fim"/>
    <result property="codigoExterno" column="disponibilidade_codigo_externo"/>

    <collection  property="periodos"   resultMap="disponibilidadePeriodo.disponibilidadePeriodoRM"/>
  </resultMap>

  <resultMap id="disponibilidadesProdutoRM" type="DTOGenerico">
    <id property="id" column="disponibilidade_id"/>
  </resultMap>


  <select id="selecione" parameterType="map" resultMap="disponibilidadeRM" prefix="true">
    select * from  disponibilidade   join   disponibilidade_periodo on disponibilidade.id  = disponibilidade_id
                         left join disponibilidade_periodo_dia on disponibilidade_periodo.id = disponibilidade_periodo_id
            where catalogo_id = #{idCatalogo} and removida is not true;
  </select>


  <update id="atualize" parameterType="map"  >
    update disponibilidade
    set
      data_inicio = #{dataInicio},
      data_fim = #{dataFim}
    where id = #{id} and catalogo_id =  #{catalogo.id}
  </update>

  <delete id="remova" parameterType="map">
    update disponibilidade set removida = true where  id = #{id};
  </delete>

  <delete id="removaTodas" parameterType="map">
    update disponibilidade set removida = true where  catalogo_id = #{idCatalogo};
  </delete>





</mapper>
