<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tarefaDeEnvioDeNfse">
  <resultMap id="tarefaDeEnvioDeNfseRM" type="TarefaDeEnvioDeNfse">
    <id property="id" column="tarefa_de_envio_de_nfse_id"/>

    <result property="status" column="tarefa_de_envio_de_nfse_status"/>
    <result property="mensagem" column="tarefa_de_envio_de_nfse_mensagem" />
    <result property="tentativas" column="tarefa_de_envio_de_nfse_tentativas" />

    <association property="nota"  resultMap="notaFiscalDeServico.notaFiscalDeServicoRM"/>
  </resultMap>

  <resultMap id="tarefaDaFaturaRM" type="TarefaDeEnvioDeNfse">
    <id property="id" column="tarefa_de_envio_de_nfse_id"/>

    <result property="status" column="tarefa_de_envio_de_nfse_status"/>
    <result property="mensagem" column="tarefa_de_envio_de_nfse_mensagem" />
    <result property="tentativas" column="tarefa_de_envio_de_nfse_tentativas" />

    <association property="nota"  resultMap="notaFiscalDeServico.notaFiscalDeServicoDaFaturaRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="tarefaDeEnvioDeNfseRM" prefix="true">
    select * from tarefa_de_envio_de_nfse
    left join nota_fiscal_de_servico on nota_fiscal_de_servico.id = tarefa_de_envio_de_nfse.nota_id
    join fatura on fatura.id = nota_fiscal_de_servico.fatura_id
    join empresa on empresa.id = nota_fiscal_de_servico.empresa_tomador_id


    <choose>
      <when test="id">
        where tarefa_de_envio_de_nfse.id = #{id}
      </when>
      <when test="nota">
        where nota_id = #{nota.id}
      </when>
      <when test="fatura">
        where fatura_id = #{fatura.id}
      </when>
    </choose>
    order by tarefa_de_envio_de_nfse.id desc <if test="inicio"> limit #{inicio}, #{total}</if>
  </select>


  <update id="atualizeStatus" parameterType="map">
    update tarefa_de_envio_de_nfse
           set status = #{status},
           mensagem = #{mensagem},
           tentativas = #{tentativas}
    where id = #{id}
  </update>
</mapper>

