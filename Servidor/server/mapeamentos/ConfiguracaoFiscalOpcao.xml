<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="configuracaoFiscalOpcao">
  <resultMap id="configuracaoFiscalOpcaoRM" type="ConfiguracaoFiscalOpcao">
    <id property="id" column="configuracao_fiscal_opcao_id"/>
    <result property="ncm" column="configuracao_fiscal_opcao_ncm"/>
    <result property="gtin" column="configuracao_fiscal_opcao_gtin"/>
    <result property="cest" column="configuracao_fiscal_opcao_cest"/>
    <result property="unidade" column="configuracao_fiscal_opcao_unidade"/>
    <result property="producaoPropria" column="configuracao_fiscal_opcao_producao_propria"/>
    <result property="cnpjProdutor" column="configuracao_fiscal_opcao_cnpj_produtor"/>
    <result property="tipoCalculoIPI" column="configuracao_fiscal_opcao_tipo_calculo_ipi"/>
    <result property="aliquotaIPI" column="configuracao_fiscal_opcao_aliquota_ipi"/>
    <result property="valorPorUnidadeIPI" column="configuracao_fiscal_opcao_valor_por_unidade_ipi"/>
    <result property="quantidadeSeloDeControleIPI" column="configuracao_fiscal_opcao_quantidade_selo_de_controle_ipi"/>
    <result property="ipiCompoeBaseCalculoICMS" column="configuracao_fiscal_opcao_ipi_compoe_base_calculo_icms"/>
    <result property="aliquotaAtributosAproximados" column="configuracao_fiscal_opcao_aliquota_atributos_aproximados"/>
    <result property="informacoesAdicionais" column="configuracao_fiscal_opcao_informacoes_adicionais"/>
    <result property="emitirComoItem" column="configuracao_fiscal_opcao_emitir_como_item"/>

    <association property="origem" resultMap="origemProduto.origemProdutoRM"/>
    <association property="tipoDeTributacaoIPI" resultMap="tipoDeTributacaoIPI.tipoDeTributacaoIPIRM"/>
    <association property="seloDeControleIPI" resultMap="seloDeControleIPI.seloDeControleIPIRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="configuracaoFiscalOpcaoRM" prefix="true">
    select * from configuracao_fiscal_opcao
    left join origem_produto on origem_produto_id = origem_produto.id
    left join tipo_de_tributacao_ipi on tipo_de_tributacao_ipi_id = tipo_de_tributacao_ipi.id
    left join selo_de_controle_ipi on selo_de_controle_ipi_id = selo_de_controle_ipi.id
    where removido is not true
    <if test="id">
      and configuracao_fiscal_opcao_id = #{id}
    </if>
    <if test="opcaoAdicionalProdutoId">
      and opcao_adicional_produto_id = #{opcaoAdicionalProdutoId}
    </if>
  </select>

  <insert id="insira" parameterType="map">
    insert into configuracao_fiscal_opcao(
      opcao_adicional_produto_id,
      ncm,
      gtin,
      cest,
      unidade,
      producao_propria,
      cnpj_produtor,
      tipo_calculo_ipi,
      aliquota_ipi,
      valor_por_unidade_ipi,
      quantidade_selo_de_controle_ipi,
      ipi_compoe_base_calculo_icms,
      aliquota_atributos_aproximados,
      informacoes_adicionais,
      emitir_como_item,
      tipo_de_tributacao_ipi_id,
      selo_de_controle_ipi_id,
      origem_produto_id
    )
    values(
      #{opcaoAdicionalProduto.id},
      #{ncm},
      #{gtin},
      #{cest},
      #{unidade},
      #{producaoPropria},
      #{cnpjProdutor},
      #{tipoCalculoIPI},
      #{aliquotaIPI},
      #{valorPorUnidadeIPI},
      #{quantidadeSeloDeControleIPI},
      #{ipiCompoeBaseCalculoICMS},
      #{aliquotaAtributosAproximados},
      #{informacoesAdicionais},
      #{emitirComoItem},
      #{tipoDeTributacaoIPI.id},
      #{seloDeControleIPI.id},
      #{origem.id}
    )
  </insert>

  <create id="crieTabela">
    create table configuracao_fiscal_opcao(
      id bigint primary key auto_increment,
      opcao_adicional_produto_id bigint not null,
      origem_produto_id bigint,
      ncm varchar(8),
      gtin varchar(14),
      cest varchar(7),
      unidade varchar(2),
      producao_propria boolean,
      cnpj_produtor varchar(14),
      tipo_calculo_ipi varchar(20),
      aliquota_ipi numeric(10,2),
      valor_por_unidade_ipi numeric(10,2),
      quantidade_selo_de_controle_ipi int,
      ipi_compoe_base_calculo_icms boolean,
      aliquota_atributos_aproximados numeric(10,2),
      informacoes_adicionais text,
      emitir_como_item boolean,
      tipo_de_tributacao_ipi_id bigint,
      selo_de_controle_ipi_id bigint,
      removido boolean default false,
      foreign key (opcao_adicional_produto_id) references opcao_adicional_produto(id),
      foreign key (origem_produto_id) references origem_produto(id),
      foreign key (tipo_de_tributacao_ipi_id) references tipo_de_tributacao_ipi(id),
      foreign key (selo_de_controle_ipi_id) references selo_de_controle_ipi(id)
    );
  </create>

  <update id="atualize" parameterType="map">
    update configuracao_fiscal_opcao
    set ncm = #{ncm},
        gtin = #{gtin},
        cest = #{cest},
        unidade = #{unidade},
        producao_propria = #{producaoPropria},
        cnpj_produtor = #{cnpjProdutor},
        tipo_calculo_ipi = #{tipoCalculoIPI},
        aliquota_ipi = #{aliquotaIPI},
        valor_por_unidade_ipi = #{valorPorUnidadeIPI},
        quantidade_selo_de_controle_ipi = #{quantidadeSeloDeControleIPI},
        ipi_compoe_base_calculo_icms = #{ipiCompoeBaseCalculoICMS},
        aliquota_atributos_aproximados = #{aliquotaAtributosAproximados},
        informacoes_adicionais = #{informacoesAdicionais},
        emitir_como_item = #{emitirComoItem},
        tipo_de_tributacao_ipi_id = #{tipoDeTributacaoIPI.id},
        selo_de_controle_ipi_id = #{seloDeControleIPI.id},
        origem_produto_id = #{origem.id}
    where id = #{id}
  </update>

  <update id="remova" parameterType="map">
    update configuracao_fiscal_opcao
    set removido = true
    where id = #{id}
  </update>
</mapper>
