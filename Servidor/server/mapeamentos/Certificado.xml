<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

  <mapper namespace="certificado">
    <resultMap id="certificadoRM" type="Certificado">
      <id property="id" column="certificado_id"/>
      <result property="arquivo" column="certificado_arquivo"/>
      <result property="senhaCriptografada" column="certificado_senha_criptografada"/>
      <result property="ativo" column="certificado_ativo"/>
      <result property="dataCriacao" column="certificado_data_criacao"/>

      <association property="empresa" columnPrefix="empresa_" resultMap="empresa.empresaRM"/>
    </resultMap>

    <select id="selecione" parameterType="map" resultMap="certificadoRM" prefix="true">
      select * from certificado
      where id = #{id}
      and empresa_id = #{empresa.id}
      and removido = false
    </select>

    <update id="atualize" parameterType="map">
      update certificado
        set arquivo = #{arquivo},
        senha_criptografada = #{senhaCriptografada},
        ativo = #{ativo},
        data_criacao = #{dataCriacao}
      where id = #{id} and empresa_id = #{empresa.id}
    </update>

    <update id="remova" parameterType="map">
      update certificado
      set removido = true
      where id = #{id} and empresa_id = #{empresa.id}
    </update>

    <insert id="insira" parameterType="map">
      insert into certificado
      (arquivo, senha_criptografada, ativo, data_criacao, empresa_id)
      values
      (#{arquivo}, #{senhaCriptografada}, #{ativo}, #{dataCriacao}, #{empresa.id})
    </insert>

    <create id="crieTabela" parameterType="map">
      CREATE TABLE IF NOT EXISTS certificado (
      id BIGINT AUTO_INCREMENT PRIMARY KEY,
      arquivo VARCHAR(255),
      senha_criptografada VARCHAR(255),
      ativo BOOLEAN,
      empresa_id BIGINT(20) NOT NULL,
      removido BOOLEAN DEFAULT FALSE,
      data_criacao TIMESTAMP,

      foreign key (empresa_id) references empresa(id) on delete cascade
    )
  </create>
  </mapper>
