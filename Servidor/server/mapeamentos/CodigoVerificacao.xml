<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="codigoVerificacao">
  <resultMap id="codigoVerificacaoRM" type="CodigoVerificacao">
    <id property="telefone" column="codigo_verificacao_id"/>
    <result property="codigo" column="codigo_verificacao_codigo"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="codigoVerificacaoRM">
    select *
    from codigo_verificacao
    where
      codigo_verificacao.empresa_id = #{idEmpresa}
      and codigo_verificacao.codigo = #{codigo}
      and codigo_verificacao.telefone = #{telefone}
  </select>

  <insert id="insira" parameterType="map">
    insert into codigo_verificacao(telefone, codigo, empresa_id)
    values (#{telefone}, #{codigo}, #{empresa.id})
    ON DUPLICATE KEY UPDATE codigo = #{codigo}, empresa_id = #{empresa.id};
  </insert>
</mapper>
