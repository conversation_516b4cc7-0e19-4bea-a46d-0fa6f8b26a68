<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tributacaoNaturezaOperacao">

      <resultMap id="tributacaoNaturezaOperacaoRM" type="TributacaoNaturezaOperacao">
        <id property="id" column="tributacao_natureza_operacao_id"/>
        <result property="descricao" column="tributacao_natureza_operacao_descricao"/>
        <result property="aliquotaICMS" column="tributacao_natureza_operacao_aliquota_icms"/>
        <result property="aliquotaICMSST" column="tributacao_natureza_operacao_aliquota_icms_st"/>
        <result property="percentualReducaoBaseCalculoICMS" column="tributacao_natureza_operacao_percentual_reducao_base_calculo_icms"/>
        <result property="percentualReducaoBaseCalculoICMSST" column="tributacao_natureza_operacao_percentual_reducao_base_calculo_icms_st"/>
        <result property="percentualMargemValorAdicionadoICMSST" column="tributacao_natureza_operacao_percentual_margem_valor_adicionado_icms_st"/>
        <result property="percentualFundoCombatePobreza" column="tributacao_natureza_operacao_percentual_fundo_combate_pobreza"/>
        <result property="percentualFundoCombatePobrezaST" column="tributacao_natureza_operacao_percentual_fundo_combate_pobreza_st"/>

        <result property="ipiCompoeBaseCalculoICMS" column="tributacao_natureza_operacao_ipi_compoe_base_calculo_icms"/>
        <result property="ipicompoeBasePISeCOFINS" column="tributacao_natureza_operacao_ipi_compoe_base_pis_cofins"/>
        <result property="excluirICMSbasebasePISeCOFINS" column="tributacao_natureza_operacao_excluir_icms_base_pis_cofins"/>


        <association property="tipoDeTributacaoICMS" resultMap="tipoDeTributacaoICMS.tipoDeTributacaoICMSRM"/>
        <association property="cfop" resultMap="cfop.cfopRM"/>
        <association property="modalidadeBaseDeCalculoICMS" resultMap="modalidadeBaseDeCalculoICMS.modalidadeBaseDeCalculoICMSRM"/>
        <association property="modalidadeBaseDeCalculoICMSST" resultMap="modalidadeBaseDeCalculoICMSST.modalidadeBaseDeCalculoICMSSTRM"/>


      </resultMap>

  <resultMap id="tributacaoNaturezaOperacaoPropriaRM" type="TributacaoNaturezaOperacao">
    <id property="id" column="tributacao_natureza_operacao_id"/>
    <result property="descricao" column="tributacao_natureza_operacao_descricao"/>
    <result property="aliquotaICMS" column="tributacao_natureza_operacao_aliquota_icms"/>
    <result property="aliquotaICMSST" column="tributacao_natureza_operacao_aliquota_icms_st"/>
    <result property="percentualReducaoBaseCalculoICMS" column="tributacao_natureza_operacao_percentual_reducao_base_calculo_icms"/>
    <result property="percentualReducaoBaseCalculoICMSST" column="tributacao_natureza_operacao_percentual_reducao_base_calculo_icms_st"/>
    <result property="percentualMargemValorAdicionadoICMSST" column="tributacao_natureza_operacao_percentual_margem_valor_adicionado_icms_st"/>
    <result property="percentualFundoCombatePobreza" column="tributacao_natureza_operacao_percentual_fundo_combate_pobreza"/>
    <result property="percentualFundoCombatePobrezaST" column="tributacao_natureza_operacao_percentual_fundo_combate_pobreza_st"/>

    <result property="ipiCompoeBaseCalculoICMS" column="tributacao_natureza_operacao_ipi_compoe_base_calculo_icms"/>
    <result property="ipicompoeBasePISeCOFINS" column="tributacao_natureza_operacao_ipi_compoe_base_pis_cofins"/>
    <result property="excluirICMSbasebasePISeCOFINS" column="tributacao_natureza_operacao_excluir_icms_base_pis_cofins"/>


    <association property="tipoDeTributacaoICMS" columnPrefix="propria_" resultMap="tipoDeTributacaoICMS.tipoDeTributacaoICMSRM"/>
    <association property="cfop" columnPrefix="propria_" resultMap="cfop.cfopRM"/>
    <association property="modalidadeBaseDeCalculoICMS" columnPrefix="propria_" resultMap="modalidadeBaseDeCalculoICMS.modalidadeBaseDeCalculoICMSRM"/>
    <association property="modalidadeBaseDeCalculoICMSST" columnPrefix="propria_" resultMap="modalidadeBaseDeCalculoICMSST.modalidadeBaseDeCalculoICMSSTRM"/>


  </resultMap>

  <resultMap id="tributacaoNaturezaOperacaoTerceirosRM" type="TributacaoNaturezaOperacao">
    <id property="id" column="tributacao_natureza_operacao_id"/>
    <result property="descricao" column="tributacao_natureza_operacao_descricao"/>
    <result property="aliquotaICMS" column="tributacao_natureza_operacao_aliquota_icms"/>
    <result property="aliquotaICMSST" column="tributacao_natureza_operacao_aliquota_icms_st"/>
    <result property="percentualReducaoBaseCalculoICMS" column="tributacao_natureza_operacao_percentual_reducao_base_calculo_icms"/>
    <result property="percentualReducaoBaseCalculoICMSST" column="tributacao_natureza_operacao_percentual_reducao_base_calculo_icms_st"/>
    <result property="percentualMargemValorAdicionadoICMSST" column="tributacao_natureza_operacao_percentual_margem_valor_adicionado_icms_st"/>
    <result property="percentualFundoCombatePobreza" column="tributacao_natureza_operacao_percentual_fundo_combate_pobreza"/>
    <result property="percentualFundoCombatePobrezaST" column="tributacao_natureza_operacao_percentual_fundo_combate_pobreza_st"/>

    <result property="ipiCompoeBaseCalculoICMS" column="tributacao_natureza_operacao_ipi_compoe_base_calculo_icms"/>
    <result property="ipicompoeBasePISeCOFINS" column="tributacao_natureza_operacao_ipi_compoe_base_pis_cofins"/>
    <result property="excluirICMSbasebasePISeCOFINS" column="tributacao_natureza_operacao_excluir_icms_base_pis_cofins"/>


    <association property="tipoDeTributacaoICMS" columnPrefix="terceiros_" resultMap="tipoDeTributacaoICMS.tipoDeTributacaoICMSRM"/>
    <association property="cfop" columnPrefix="terceiros_" resultMap="cfop.cfopRM"/>
    <association property="modalidadeBaseDeCalculoICMS" columnPrefix="terceiros_" resultMap="modalidadeBaseDeCalculoICMS.modalidadeBaseDeCalculoICMSRM"/>
    <association property="modalidadeBaseDeCalculoICMSST" columnPrefix="terceiros_" resultMap="modalidadeBaseDeCalculoICMSST.modalidadeBaseDeCalculoICMSSTRM"/>


  </resultMap>


  <select id="selecione" parameterType="map" resultMap="tributacaoNaturezaOperacaoRM" prefix="true">
        select * from tributacao_natureza_operacao left join
        tipo_de_tributacao_icms on tributacao_natureza_operacao.tipo_de_tributacao_icms_id = tipo_de_tributacao_icms.id
        left join cfop on tributacao_natureza_operacao.cfop_id = cfop.id
        left join modalidade_base_de_calculo_icms on tributacao_natureza_operacao.modalidade_base_de_calculo_icms_id = modalidade_base_de_calculo_icms.id
        left join modalidade_base_de_calculo_icms_st on tributacao_natureza_operacao.modalidade_base_de_calculo_icms_st_id = modalidade_base_de_calculo_icms_st.id
        where 1 = 1
        <if test="id">
          and id = #{id}
        </if>
        <if test="descricao">
          and descricao = #{descricao}
        </if>
        <if test="tipoDetributacao">
          and tipo_de_tributacao_icms_id = #{tipoDetributacao.id}
        </if>
        <if test="cfop">
          and cfop_id = #{cfop.id}
        </if>
        <if test="modalidadeBaseDeCalculoICMS">
          and modalidade_base_de_calculo_icms_id = #{modalidadeBaseDeCalculoICMS.id}
        </if>
        <if test="modalidadeBaseDeCalculoICMSST">
          and modalidade_base_de_calculo_icms_st_id = #{modalidadeBaseDeCalculoICMSST.id}
        </if>
      </select>

  <insert id="insira" parameterType="map">
    insert into tributacao_natureza_operacao
    (descricao, aliquota_icms, aliquota_icms_st, percentual_reducao_base_calculo_icms, percentual_reducao_base_calculo_icms_st, percentual_margem_valor_adicionado_icms_st, percentual_fundo_combate_pobreza, percentual_fundo_combate_pobreza_st, ipi_compoe_base_calculo_icms, ipi_compoe_base_pis_cofins, excluir_icms_base_pis_cofins, tipo_de_tributacao_icms_id, cfop_id, modalidade_base_de_calculo_icms_id, modalidade_base_de_calculo_icms_st_id)
    values
    (#{descricao}, #{aliquotaICMS}, #{aliquotaICMSST}, #{percentualReducaoBaseCalculoICMS}, #{percentualReducaoBaseCalculoICMSST}, #{percentualMargemValorAdicionadoICMSST}, #{percentualFundoCombatePobreza}, #{percentualFundoCombatePobrezaST}, #{ipiCompoeBaseCalculoICMS}, #{ipicompoeBasePISeCOFINS}, #{excluirICMSbasePISeCOFINS}, #{tipoDeTributacaoICMS.id}, #{cfop.id}, #{modalidadeBaseDeCalculoICMS.id}, #{modalidadeBaseDeCalculoICMSST.id})
  </insert>

  <update id="atualize" parameterType="map">
    update tributacao_natureza_operacao
    set descricao = #{descricao},
    aliquota_icms = #{aliquotaICMS},
    aliquota_icms_st = #{aliquotaICMSST},
    percentual_reducao_base_calculo_icms = #{percentualReducaoBaseCalculoICMS},
    percentual_reducao_base_calculo_icms_st = #{percentualReducaoBaseCalculoICMSST},
    percentual_margem_valor_adicionado_icms_st = #{percentualMargemValorAdicionadoICMSST},
    percentual_fundo_combate_pobreza = #{percentualFundoCombatePobreza},
    percentual_fundo_combate_pobreza_st = #{percentualFundoCombatePobrezaST},
    ipi_compoe_base_calculo_icms = #{ipiCompoeBaseCalculoICMS},
    ipi_compoe_base_pis_cofins = #{ipicompoeBasePISeCOFINS},
    excluir_icms_base_pis_cofins = #{excluirICMSbasebasePISeCOFINS},
    tipo_de_tributacao_icms_id = #{tipoDetributacao.id},
    cfop_id = #{cfop.id},
    modalidade_base_de_calculo_icms_id = #{modalidadeBaseDeCalculoICMS.id},
    modalidade_base_de_calculo_icms_st_id = #{modalidadeBaseDeCalculoICMSST.id}
    where id = #{id}
  </update>

</mapper>
