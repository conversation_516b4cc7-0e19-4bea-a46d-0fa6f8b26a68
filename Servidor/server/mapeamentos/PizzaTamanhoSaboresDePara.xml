<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pizzaTamanhoSaboresDePara">
  <resultMap id="pizzaTamanhoSaboresDeParaRM" type="PizzaTamanhoSaboresDePara">
    <id property="id" column="pizza_tamanho_sabores_ecletica_id"/>

    <result property="nome" column="pizza_tamanho_sabores_ecletica_nome"/>
    <result property="codigoPdv" column="pizza_tamanho_sabores_ecletica_codigo_pdv"/>
    <result property="qtdeSabores" column="pizza_tamanho_sabores_ecletica_qtde_sabores"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="pizzaTamanhoSaboresDeParaRM" prefix="true">
      select * from  pizza_tamanho_sabores_ecletica
          where empresa_id = #{idEmpresa}

         <if test="idTamanhoTemplate != null">
           and produto_template_tamanho_id = #{idTamanhoTemplate}
         </if>

         <if test="qtdeSabores">
            and qtde_sabores = #{qtdeSabores}
         </if>
  </select>

  <insert id="insira" parameterType="map">
    insert into  pizza_tamanho_sabores_ecletica(nome,qtde_sabores,codigo_pdv, produto_template_tamanho_id, empresa_id)
        values (#{nome}, #{qtdeSabores}, #{codigoPdv}, #{templateTamanho.id}, #{empresa.id});
  </insert>


  <update id="atualize">
     update pizza_tamanho_sabores_ecletica
         set nome = #{nome}, qtde_sabores = #{qtdeSabores}, codigo_pdv = #{codigoPdv}

                where id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="remova">
        delete from pizza_tamanho_sabores_ecletica
                where  id = #{id} and empresa_id = #{empresa.id};
  </update>

</mapper>
