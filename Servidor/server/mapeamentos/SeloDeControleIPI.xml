<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="seloDeControleIPI">

      <resultMap id="seloDeControleIPIRM" type="SeloDeControleIPI">
        <id property="id" column="selo_de_controle_ipi_id"/>
        <result property="codigo" column="selo_de_controle_ipi_codigo"/>
        <result property="descricao" column="selo_de_controle_ipi_descricao"/>
      </resultMap>

      <select id="selecione" parameterType="map" resultMap="seloDeControleIPIRM" prefix="true">
        select * from selo_de_controle_ipi
        where 1 = 1
        <if test="id">
          and id = #{id}
        </if>
        <if test="codigo">
          and codigo = #{codigo}
        </if>
      </select>
</mapper>
