<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="formaPagamentoGrupoDePara">
  <resultMap id="formaPagamentoGrupoDeParaRM" type="FormaPagamentoGrupoDePara">
    <id property="id" column="forma_pagamento_grupo_de_para_id"/>

    <association property="de" columnPrefix="de_" resultMap="formaDePagamento.formaDePagamentoSeguraRM"/>
    <association property="para" columnPrefix="para_" resultMap="formaDePagamento.formaDePagamentoSeguraRM"/>


  </resultMap>


  <resultMap id="formaPagamentoDeParaDoGrupoRM" type="FormaPagamentoGrupoDePara">
    <id property="id" column="forma_pagamento_grupo_de_para_id"/>

    <result property="de" column="forma_pagamento_grupo_de_para_de_id" />
    <result property="para" column="forma_pagamento_grupo_de_para_para_id" />
    <result property="paraEmpresa" column="forma_de_pagamento_de_para_empresa_id" />

  </resultMap>



  <select id="selecione" parameterType="map" resultMap="formaPagamentoGrupoDeParaRM" prefix="true">
    select *
      from forma_pagamento_grupo_de_para join forma_de_pagamento de_forma_de_pagamento on de_forma_de_pagamento.id = de_id
                                         join forma_de_pagamento para_forma_de_pagamento  on para_forma_de_pagamento.id = para_id

    where
       <choose>
           <when test="idEmpresaPrincipal">
             empresa_principal_id = #{idEmpresaPrincipal}
           </when>
           <otherwise>
             grupo_de_lojas_id = #{idGrupoLoja}
           </otherwise>
       </choose>

        <if test="para">   and para_id = #{para}  </if>
        <if test="de">  and de_id = #{de}  </if>
        <if test="paraEmpresa">  and para_forma_de_pagamento.empresa_id = #{paraEmpresa}  </if>

  </select>

  <select id="existe" parameterType="map" resultType="int">
    select count(*) total from
        forma_pagamento_grupo_de_para
           where grupo_de_lojas_id = #{idGrupoLoja} and
                 empresa_principal_id = #{idEmpresaPrincipal}   and para_id = #{idPara}
  </select>


  <update id="remova" parameterType="map">
    delete from forma_pagamento_grupo_de_para  where id = #{id};
  </update>

</mapper>
