<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="itemAssinatura">

  <resultMap id="itemAssinaturaRM"  type="ItemAssinatura">
    <id property="id" column="item_assinatura_id"/>

    <result property="descricao" column="item_assinatura_descricao"/>
    <result property="quantidade" column="item_assinatura_quantidade"/>
    <result property="valor" column="item_assinatura_valor"/>
    <result property="total" column="item_assinatura_total"/>
    <result property="tipo" column="item_assinatura_tipo"/>
    <result property="recorrente" column="item_assinatura_recorrente"/>
    <result property="moduloId" column="item_assinatura_modulo_id"/>
    <result property="ciclos" column="item_assinatura_ciclos"/>
    <result property="ciclosPagos" column="item_assinatura_ciclos_pagos"/>

  </resultMap>

  <update id="atualize">
    update item_assinatura
      set descricao = #{descricao}, quantidade = #{quantidade}, valor = #{valor},  total = #{total},
             recorrente = #{recorrente}, ciclos = #{ciclos}
      where id  = #{id};
  </update>


  <update id="remova">
    delete from item_assinatura where id  = #{id};
  </update>

  <update id="atualizeRemovida">
     update item_assinatura set removido  = true where  id  = #{id};
  </update>

  <update id="atualizeClicosPagos">
    update item_assinatura set ciclos_pagos  = #{ciclosPagos} where  id  = #{id};
  </update>

</mapper>
