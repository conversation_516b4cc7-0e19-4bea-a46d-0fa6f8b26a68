<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pagamentoComanda">

  <resultMap id="pagamentoComandaRM" type="PagamentoComanda">
    <id property="id" column="pagamento_comanda_id"/>

    <result property="status" column="pagamento_comanda_status"/>
    <result property="valor" column="pagamento_comanda_valor"/>
    <result property="codigo" column="pagamento_comanda_codigo"/>
    <result property="idLink" column="pagamento_comanda_id_link"/>
    <result property="link" column="pagamento_comanda_link"/>
    <result property="codigoTransacao" column="pagamento_comanda_codigo_transacao"/>

    <association property="formaDePagamento" columnPrefix="comanda_" resultMap="formaDePagamento.formaDePagamentoRM"/>

    <result property="trocoPara" column="pagamento_comanda_troco_para"/>
  </resultMap>


  <resultMap id="pagamentoComandaCompletoRM" type="pagamentoComanda">
    <id property="id" column="pagamento_comanda_id"/>

    <result property="status" column="pagamento_comanda_status"/>
    <result property="valor" column="pagamento_comanda_valor"/>
    <result property="codigo" column="pagamento_comanda_codigo"/>
    <result property="idLink" column="pagamento_comanda_id_link"/>
    <result property="link" column="pagamento_comanda_link"/>
    <result property="codigoTransacao" column="pagamento_comanda_codigo_transacao"/>

    <association property="pedido" resultMap="pedido.pedidoPagamentoRM"/>
    <association property="formaDePagamento" resultMap="formaDePagamento.formaDePagamentoRM"/>

    <result property="trocoPara" column="pagamento_comanda_troco_para"/>
  </resultMap>


  <update id="atualizeStatus">
    update pagamento_comanda
          set status = #{status} where  id = #{id};
  </update>

</mapper>
