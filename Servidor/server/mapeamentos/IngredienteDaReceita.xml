<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ingredienteDaReceita">
  <resultMap id="ingredienteDaReceitaRM" type="IngredienteDaReceita">
    <id property="id" column="ingrediente_da_receita_id"/>

    <result property="quantidade" column="ingrediente_da_receita_quantidade"/>
    <result property="custoMedio" column="ingrediente_da_receita_custo_medio"/>
    <result property="custoNaPorcao" column="ingrediente_da_receita_custo_na_porcao"/>
    <result property="removido" column="ingrediente_da_receita_removido"/>

    <association property="insumo" resultMap="insumo.insumoDaReceitaRM"/>
    <association property="unidadeMedida" resultMap="unidadeMedida.unidadeMedidaRM"/>

  </resultMap>

  <update id="atualize">
    update ingrediente_da_receita
       set quantidade = #{quantidade}, insumo_id = #{insumo.id},
           custo_medio = #{custoMedio}, custo_na_porcao = #{custoNaPorcao}
            where id   = #{id}
  </update>
  <update id="remova">
    update ingrediente_da_receita set removido = true
    where id   = #{id}
  </update>

</mapper>
