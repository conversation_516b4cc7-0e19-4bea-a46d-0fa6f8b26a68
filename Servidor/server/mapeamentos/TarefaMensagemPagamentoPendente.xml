<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tarefaMensagemPagamentoPendente">
  <resultMap id="tarefaMensagemPagamentoPendenteRM" type="TarefaMensagemPagamentoPendente">
    <id property="id" column="tarefa_mensagem_pagamento_pendente_id"/>

    <result property="guidPedido" column="tarefa_mensagem_pagamento_pendente_guid_pedido"/>
    <result property="horarioCriacao" column="tarefa_mensagem_pagamento_pendente_horario_criacao"/>
    <result property="horarioVencimento" column="tarefa_mensagem_pagamento_pendente_horario_vencimento"/>

    <association property="pagamento"  resultMap="pagamentoPedido.pagamentoPedidoCompletoRM"/>
    <association property="contato"  resultMap="contato.contatoRM"/>
    <association property="empresa"  resultMap="empresa.empresaRM"/>
  </resultMap>


  <select id="selecione" resultMap="tarefaMensagemPagamentoPendenteRM" prefix="true">
    SELECT  *  FROM tarefa_mensagem_pagamento_pendente
                     join pagamento_pedido pagamento on pagamento_id  = pagamento.id
                     join pedido on pedido.id = pagamento.pedido_id
                     join empresa on empresa.id = tarefa_mensagem_pagamento_pendente.empresa_id
                     join contato on contato.id = tarefa_mensagem_pagamento_pendente.contato_id
                     left join empresa_modulo em on em.empresa_id = empresa.id
                     left join modulo on modulo.id = em.modulo_id
        where
        <choose>
            <when test="id">
              tarefa_mensagem_pagamento_pendente.id = #{id}
            </when>
            <when test="executar">
              executada is not true and  now() > horario_vencimento and data_expiracao > now() and 3 > pagamento.status
            </when>
        </choose>
  </select>

  <update id="atualize">
    UPDATE tarefa_mensagem_pagamento_pendente
     SET executada = true
     WHERE id = #{id} AND empresa_id = #{empresa.id}
  </update>

</mapper>
