
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="produtoEmbeddings">
  <resultMap id="produtoEmbeddingsRM" type="ProdutoEmbeddings">
    <id property="id" column="produto_embeddings_id"/>

    <result property="produto" column="produto_embeddings_produto"/>
    <result property="descricao" column="produto_embeddings_descricao"/>

    <result property="idProduto" column="produto_embeddings_produto_id"/>

    <result property="embedding" column="produto_embeddings_embedding"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="produtoEmbeddingsRM" prefix="true">
    select  *
    from produto_embeddings
    where empresa_id = #{idEmpresa}
  </select>

  <insert id="insira" parameterType="map">
    insert into produto_embeddings
    (produto, descricao, embedding, produto_id, empresa_id) values
    (#{produto}, #{descricao}, #{embedding}, #{idProduto}, #{empresa.id})
    ON DUPLICATE KEY UPDATE
    produto = #{produto},
    descricao = #{descricao},
    embedding = VALUES(embedding);
  </insert>
</mapper>
