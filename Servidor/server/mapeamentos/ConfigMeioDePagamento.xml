<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="configMeioDePagamento">
  <resultMap id="configMeioDePagamentoRM" type="ConfigMeioDePagamento">
    <id property="id" column="config_meio_pagamento_id"/>

    <result property="nomeFaturaCartao" column="config_meio_pagamento_nome_fatura_cartao"/>

    <result property="clientID" column="config_meio_pagamento_client_id"/>
    <result property="clientSecret" column="config_meio_pagamento_client_secret"/>

    <result property="merchantKey" column="config_meio_pagamento_merchant_key"/>
    <result property="merchantId" column="config_meio_pagamento_merchant_id"/>

    <result property="token" column="config_meio_pagamento_token"/>
    <result property="email" column="config_meio_pagamento_email"/>
    <result property="publicKey" column="config_meio_pagamento_public_key"/>
    <result property="tokenizar" column="config_meio_pagamento_tokenizar"/>


    <result property="establishmentCode" column="config_meio_pagamento_establishment_code"/>
    <result property="merchantName" column="config_meio_pagamento_merchant_name"/>
    <result property="mcc" column="config_meio_pagamento_mcc"/>
    <result property="antifraudeDesabilitado" column="config_meio_pagamento_antifraude_desabilitado"/>

    <result property="meioDePagamento" column="config_meio_pagamento_meio_de_pagamento"/>
  </resultMap>

  <resultMap id="configMeioDePagamentoSeguraRM" type="ConfigMeioDePagamento">
    <id property="id" column="config_meio_pagamento_id"/>

    <result property="meioDePagamento" column="config_meio_pagamento_meio_de_pagamento"/>
    <result property="publicKey" column="config_meio_pagamento_public_key"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="configMeioDePagamentoRM" prefix="true">
    select *
      from  config_meio_pagamento
          where  config_meio_pagamento.id = #{id}
  </select>

  <insert id="insira"  parameterType="map" useGeneratedKeys="true">
    insert config_meio_pagamento(client_id, client_secret, merchant_key, merchant_id, nome_fatura_cartao, token, public_key, email, meio_de_pagamento,
    establishment_code,merchant_name,mcc, antifraude_desabilitado)
      values( #{clientID}, #{clientSecret}, #{merchantKey}, #{merchantId}, #{nomeFaturaCartao}, #{token}, #{publicKey}, #{email}, #{meioDePagamento},
    #{establishmentCode},#{merchantName},#{mcc}, #{antifraudeDesabilitado});
  </insert>

  <update id="atualize"  parameterType="map">
    update config_meio_pagamento
      set
      merchant_key  = #{merchantKey},
      merchant_id = #{merchantId},
      client_id = #{clientID},
      client_secret = #{clientSecret},
      public_key = #{publicKey},
      establishment_code = #{establishmentCode},
      merchant_name = #{merchantName},
      mcc = #{mcc},
      meio_de_pagamento = #{meioDePagamento},
      token = #{token},
      email = #{email},
      antifraude_desabilitado = #{antifraudeDesabilitado},
      nome_fatura_cartao = #{nomeFaturaCartao},
      tokenizar = #{tokenizar}
    where id = #{id};
  </update>

  <update id="atualizeChave"  parameterType="map">
    update config_meio_pagamento
    set public_key = #{publicKey}   where id = #{id};
  </update>
</mapper>
