<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="valorDeAdicionaisEscolhaSimples">

  <resultMap id="valorDeAdicionaisEscolhaSimplesRM" type="ValorDeAdicionaisEscolhaSimples">
    <id property="id" column="valor_de_adicionais_escolha_simples_id"/>

    <association property="campo0" columnPrefix="campo0_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>
    <association property="campo1" columnPrefix="campo1_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>
    <association property="campo2" columnPrefix="campo2_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>
    <association property="campo3" columnPrefix="campo3_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>
    <association property="campo4" columnPrefix="campo4_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>
    <association property="campo5" columnPrefix="campo5_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>
    <association property="campo6" columnPrefix="campo6_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>
    <association property="campo7" columnPrefix="campo7_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>
    <association property="campo8" columnPrefix="campo8_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>
    <association property="campo9" columnPrefix="campo9_"  resultMap="opcaoDeAdicionalDeProduto.opcaoDeAdicionalDeProdutoCampoRM"/>

    <association property="produto" resultMap="produto.produtoResultMap"/>

  </resultMap>

  <delete id="removaDoPedido" parameterType="map">
    delete from valor_de_adicionais_escolha_simples where pedido_id = #{idPedido};
  </delete>

</mapper>
