<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="notificacao_app">
  <resultMap id="notificacaoAppRM" type="NotificacaoApp">
    <id property="id" column="notificacao_app_id"/>

    <result property="nome" column="notificacao_app_nome"/>

    <result property="mensagem" column="notificacao_app_mensagem"/>
    <result property="titulo" column="notificacao_app_titulo"/>
    <result property="link" column="notificacao_app_link"/>
    <result property="urlImagem" column="notificacao_app_url_imagem"/>

    <result property="qtdeEnviadas" column="notificacao_app_qtde_enviadas"/>
    <result property="qtdeLidas" column="notificacao_app_qtde_lidas"/>

    <result property="foiTestada" column="notificacao_app_foi_testada"/>
    <result property="status" column="notificacao_app_status"/>

    <result property="tipoDeEnvio" column="notificacao_app_tipo_de_envio"/>
    <result property="horarioEnvio" column="notificacao_app_horario_envio"/>
    <result property="qtdeDeDiasNovaNotificacao" column="notificacao_app_qtde_de_dias_nova_notificacao"/>

    <result property="statusAprovacao" column="notificacao_app_status_aprovacao"/>

    <result property="qtdeMensagens" column="notificacao_app_qtde_mensagens"/>

    <association property="empresa" column="empresa_id" resultMap="empresa.empresaRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="notificacaoAppRM" prefix="true">
    select *
      from notificacao_app
        join empresa on (notificacao_app.empresa_id = empresa.id)
        where
        <if test="idEmpresa != null">
          notificacao_app.empresa_id = #{idEmpresa}
        </if>
        <if test="idEmpresa == null">
          1 = 1
        </if>
        <choose>
          <when test="status != null">
            and notificacao_app.status in
            <foreach item="s" collection="status" open="(" separator="," close=")">
              #{s}
            </foreach>
          </when>
        </choose>
        <if test="pendentes != null">
          and notificacao_app.status_aprovacao = 'Pendente'
        </if>
        <choose>
          <when test="tipoDeEnvio != null">
            and notificacao_app.tipo_de_envio = #{tipoDeEnvio}
          </when>
          <when test="id != null">
            and notificacao_app.id = #{id}
          </when>
        </choose>
          <if test="orderBy">
             order by notificacao_app.id desc
          </if>
          <if test="inicio != null">
            limit #{inicio}, #{quantidade}
          </if>
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int" prefix="true">
    select count(*)
    from notificacao_app
    where
    <if test="idEmpresa != null">
      notificacao_app.empresa_id = #{idEmpresa}
    </if>
    <if test="idEmpresa == null">
      1 = 1
    </if>
    <choose>
      <when test="status != null">
        and notificacao_app.status in
        <foreach item="s" collection="status" open="(" separator="," close=")">
          #{s}
        </foreach>
      </when>
      <when test="pendentes != null">
        and notificacao_app.status_aprovacao = 'Pendente'
      </when>
    </choose>
    <choose>
      <when test="tipoDeEnvio != null">
        and notificacao_app.tipo_de_envio = #{tipoDeEnvio}
      </when>
      <when test="id != null">
        and atividade.id = #{id}
      </when>
    </choose>
    <if test="inicio != null">
      limit #{inicio}, #{quantidade}
    </if>
  </select>

  <update id="atualizeStatus">
    update notificacao_app
    set qtde_mensagens = #{qtdeMensagens}, qtde_enviadas = #{qtdeEnviadas},
    qtde_lidas = #{qtdeLidas}, status = #{status},
        status_aprovacao = #{statusAprovacao}
    where
    notificacao_app.empresa_id = #{empresa.id}
    and id = #{id};
  </update>

  <update id="atualize">
    update notificacao_app
        set nome = #{nome}, mensagem = #{mensagem},
            qtde_enviadas = #{qtdeEnviadas},
            titulo = #{titulo},
            link = #{link},
            qtde_lidas = #{qtdeLidas},
            status = #{status},
            foi_testada = #{foiTestada},
            horario_envio = #{horarioEnvio},
            qtde_mensagens = #{qtdeMensagens},
            status_aprovacao = #{statusAprovacao},
            url_imagem = #{urlImagem}
          where
            notificacao_app.empresa_id = #{empresa.id}
            and id = #{id};
  </update>
</mapper>
