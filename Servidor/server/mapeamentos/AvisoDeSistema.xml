<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="avisoDeSistema">
  <resultMap id="avisoDeSistemaRM" type="AvisoDeSistema">
    <id property="id" column="aviso_de_sistema_id"/>

    <result property="tipo" column="aviso_de_sistema_tipo"/>
    <result property="mensagem" column="aviso_de_sistema_mensagem"/>
    <result property="entregue" column="aviso_de_sistema_entregue" />
  </resultMap>


  <select id="selecione" parameterType="map" resultMap="avisoDeSistemaRM" prefix="true">
    select *
    from
    aviso_de_sistema
    where empresa_id = #{idEmpresa}
    <if test="naoEntregues">
      and aviso_de_sistema.entregue is false
    </if>
  </select>


  <update id="marqueComoEntregues" parameterType="map">
    update aviso_de_sistema
    set aviso_de_sistema.entregue  = true
    where   empresa_id = #{empresa.id}
    and id   in
    <foreach item="id" collection="ids" open="(" separator="," close=")">
      #{id}
    </foreach>;
  </update>


</mapper>
