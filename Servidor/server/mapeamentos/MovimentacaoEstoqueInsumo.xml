<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="movimentacaoEstoqueInsumo">
  <resultMap id="movimentacaoEstoqueInsumoRM" type="MovimentacaoEstoqueInsumo">
    <id property="id" column="movimentacao_estoque_insumo_id"/>

    <result property="quantidade" column="movimentacao_estoque_insumo_quantidade"/>
    <result property="tipo" column="movimentacao_estoque_insumo_tipo"/>
    <result property="status" column="movimentacao_estoque_insumo_status"/>
    <result property="horario" column="movimentacao_estoque_insumo_horario"/>
    <result property="observacao" column="movimentacao_estoque_insumo_observacao"/>
    <result property="retornarEstoque" column="movimentacao_estoque_insumo_retornar_estoque"/>

    <association property="insumo" resultMap="insumo.insumoRM"/>
    <association property="produto" resultMap="produto.produtoResumidoRM"/>
    <association property="estoque" resultMap="estoque.estoqueRM"/>
    <association property="unidadeMedida"   resultMap="unidadeMedida.unidadeMedidaRM"/>
    <association property="motivoDaMovimentacao" resultMap="movimentacaoEstoqueInsumo.motivoMovimentacaoRM"/>
    <association property="operador" resultMap="usuario.operadorResultMap"/>
    <association property="empresa" resultMap="empresa.empresaSimplesRM"/>
    <association property="pedido" resultMap="pedido.pedidoPagamentoRM"/>

  </resultMap>

  <resultMap id="motivoMovimentacaoRM" type="MotivoDaMovimentacao">
    <id property="id" column="motivo_da_movimentacao_id"/>

    <result property="descricao" column="motivo_da_movimentacao_descricao"/>
    <result property="tipo" column="motivo_da_movimentacao_tipo"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="movimentacaoEstoqueInsumoRM" prefix="true">
      select SQL_CALC_FOUND_ROWS * from movimentacao_estoque_insumo
                                        join empresa on empresa.id  = movimentacao_estoque_insumo.empresa_id
                                        left join insumo on insumo.id = insumo_id
                                        left join produto on produto.id = produto_id
                                        left join unidade_medida on unidade_medida.id = movimentacao_estoque_insumo.unidade_medida_id
                                        left join estoque on estoque.id = movimentacao_estoque_insumo.estoque_id
                                        left join motivo_da_movimentacao on motivo_da_movimentacao.id = motivo_da_movimentacao_id
                                        left join usuario operador on operador.id = operador_id
                                        left join pedido on pedido.id = movimentacao_estoque_insumo.pedido_id

          where empresa.id =  #{idEmpresa}
          <if test="idProduto">
              and     produto.id = #{idProduto}
          </if>

          <if test="idInsumo">
            and insumo.id = #{idInsumo}
          </if>

          <if test="codigo">
            and insumo.codigo = #{codigo}
          </if>

          <if test="idGrupo">
            and grupo_de_insumo_id = #{idGrupo}
          </if>

          <if test="termo">
            and insumo.nome like #{termo}
          </if>

          <if test="idPedido">
            and movimentacao_estoque_insumo.pedido_id = #{idPedido}
          </if>

          <if test="status">
            and movimentacao_estoque_insumo.status = #{status}
          </if>

           <if test="total">
             order by movimentacao_estoque_insumo.id desc limit #{inicio}, #{total}
           </if>

  </select>



  <select id="listeMotivos" parameterType="map" resultMap="motivoMovimentacaoRM" prefix="true">
    select * from motivo_da_movimentacao
      where    automatica is not true

        order by tipo

  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int">
    SELECT FOUND_ROWS() AS total;
  </select>


  <update id="atualizeStatus" parameterType="map">
    update movimentacao_estoque_insumo set status =  #{status}
      where  empresa_id = #{empresa.id}  and id = #{id}
  </update>


</mapper>

