<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="opcaoRespostaChatbotInstagram">
  <resultMap id="opcaoRespostaChatbotInstagramRM" type="OpcaoRespostaChatbotInstagram">
    <id property="id" column="opcao_resposta_chatbot_instagram_id"/>
    
    <result property="empresa_id" column="opcao_resposta_chatbot_instagram_empresa_id"/>
    <result property="texto" column="opcao_resposta_chatbot_instagram_texto"/>
    <result property="tipo" column="opcao_resposta_chatbot_instagram_tipo"/>
    <result property="destino" column="opcao_resposta_chatbot_instagram_destino"/>
    <result property="url" column="opcao_resposta_chatbot_instagram_url"/>
    <result property="icone" column="opcao_resposta_chatbot_instagram_icone"/>
    <result property="ordem" column="opcao_resposta_chatbot_instagram_ordem"/>
    <result property="ativo" column="opcao_resposta_chatbot_instagram_ativo"/>
    
    <association property="respostaChatbotInstagram" resultMap="respostaChatbotInstagram.respostaChatbotInstagramRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="opcaoRespostaChatbotInstagramRM" prefix="true">
    select * from opcao_resposta_chatbot_instagram 
    left join resposta_chatbot_instagram on resposta_chatbot_instagram.id = opcao_resposta_chatbot_instagram.resposta_chatbot_instagram_id
    left join dados_instagram on dados_instagram.id = resposta_chatbot_instagram.dados_instagram_id
    where opcao_resposta_chatbot_instagram.empresa_id = #{idEmpresa}
    
    <if test="id">
      and opcao_resposta_chatbot_instagram.id = #{id}
    </if>
    
    <if test="respostaId">
      and opcao_resposta_chatbot_instagram.resposta_chatbot_instagram_id = #{respostaId}
    </if>
    
    <if test="chaveResposta">
      and resposta_chatbot_instagram.chave_resposta = #{chaveResposta}
    </if>
    
    <if test="tipo">
      and opcao_resposta_chatbot_instagram.tipo = #{tipo}
    </if>
    
    <if test="ativo != null">
      and opcao_resposta_chatbot_instagram.ativo = #{ativo}
    </if>
    
    order by opcao_resposta_chatbot_instagram.ordem, opcao_resposta_chatbot_instagram.texto
  </select>

  <insert id="insira" parameterType="map" useGeneratedKeys="true" keyProperty="id">
    insert into opcao_resposta_chatbot_instagram (
      empresa_id,
      resposta_chatbot_instagram_id,
      texto, 
      tipo, 
      destino, 
      url, 
      icone, 
      ordem,
      ativo
    ) values (
      #{empresa.id},
      #{respostaChatbotInstagram.id},
      #{texto}, 
      #{tipo}, 
      #{destino}, 
      #{url}, 
      #{icone}, 
      #{ordem},
      #{ativo}
    )
  </insert>

  <update id="atualize" parameterType="map">
    update opcao_resposta_chatbot_instagram set
      texto = #{texto},
      tipo = #{tipo},
      destino = #{destino},
      url = #{url},
      icone = #{icone},
      ordem = #{ordem},
      ativo = #{ativo}
    where id = #{id} 
    and empresa_id = #{empresa.id}
  </update>

  <delete id="remova" parameterType="map">
    delete from opcao_resposta_chatbot_instagram 
    where id = #{id} 
    and empresa_id = #{empresa.id}
  </delete>

  <delete id="removaPorResposta" parameterType="map">
    delete from opcao_resposta_chatbot_instagram 
    where resposta_chatbot_instagram_id = #{respostaId} 
    and empresa_id = #{empresa.id}
  </delete>

  <select id="existe" parameterType="map" resultType="int">
    select count(*) total
    from opcao_resposta_chatbot_instagram 
    where empresa_id = #{empresa.id} 
    and resposta_chatbot_instagram_id = #{respostaChatbotInstagram.id}
    and texto = #{texto}
    <if test="id">
      and id != #{id}
    </if>
  </select>
</mapper> 