<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="traducaoMensagemChatBot">
  <resultMap id="traducaoMensagemChatBotRM" type="TraducaoMensagemChatBot">
    <id property="id" column="traducao_mensagem_bot_id"/>

    <result property="nome" column="traducao_mensagem_bot_nome"/>

    <result property="descricao" column="traducao_mensagem_bot_descricao"/>

    <result property="template" column="traducao_mensagem_bot_template"/>
    <result property="mensagem" column="traducao_mensagem_bot_mensagem"/>
    <result property="ativo" column="traducao_mensagem_bot_ativo"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="traducaoMensagemChatBotRM" prefix="true">
    select *
    from traducao_mensagem_bot
    where
    traducao_mensagem_bot.empresa_id = #{idEmpresa}
    <if test="id != null">
      and traducao_mensagem_bot.id = #{id}
    </if>
    <if test="template != null">
      and traducao_mensagem_bot.template = #{template}
    </if>
    <choose>
      <when test="inicio != null">
        order by traducao_mensagem_bot.id limit #{inicio},#{total}
      </when>
    </choose>
  </select>

  <update id="atualize" parameterType="map">
    update traducao_mensagem_bot
      set nome  = #{nome},
      descricao = #{descricao},
      template = #{template},
      mensagem = #{mensagem},
      ativo = #{ativo}
      where
        id = #{id} and empresa_id = #{empresa.id}
  </update>
</mapper>
