
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="formaEntregaEmpresa">
  <resultMap id="formaEntregaEmpresaRM" type="FormaDeEntregaEmpresa">
    <id property="id" column="empresa_formas_de_entrega_id"/>

    <result property="valorMinimoFreteGratis" column="empresa_formas_de_entrega_valor_minimo_frete_gratis"/>
    <result property="valorMinimoPedido" column="empresa_formas_de_entrega_valor_minimo_pedido" />
    <result property="valorMaximoPedido" column="empresa_formas_de_entrega_valor_maximo_pedido" />
    <result property="taxaExtraRetorno" column="empresa_formas_de_entrega_taxa_extra_retorno" />
    <result property="ativa" column="empresa_formas_de_entrega_ativa" />
    <result property="nome" column="forma_de_entrega_nome" />
    <result property="tipoDeCobranca" column="empresa_formas_de_entrega_tipo_de_cobranca" />

    <result property="permiteAgendamento" column="empresa_formas_de_entrega_permite_agendamento"/>
    <result property="agendamentoObrigatorio" column="empresa_formas_de_entrega_agendamento_obrigatorio"/>
    <result property="intervaloAgendamento" column="empresa_formas_de_entrega_intervalo_agendamento"/>


    <result property="tempoMinimo" column="empresa_formas_de_entrega_tempo_minimo"/>
    <result property="tempoMaximo" column="empresa_formas_de_entrega_tempo_maximo"/>

    <result property="tempoMinimoRetirada" column="empresa_formas_de_entrega_tempo_minimo_retirada"/>
    <result property="tempoMaximoRetirada" column="empresa_formas_de_entrega_tempo_maximo_retirada"/>

    <result property="agendamentoLimiteMinimo" column="empresa_formas_de_entrega_agendamento_limite_minimo"/>
    <result property="agendamentoLimiteMaximo" column="empresa_formas_de_entrega_agendamento_limite_maximo"/>

    <result property="limitePedidosAgendados" column="empresa_formas_de_entrega_limite_pedidos_agendados"/>
    <result property="naoPerguntarHorario" column="empresa_formas_de_entrega_nao_perguntar_horario"/>
    <result property="permiteComerNoLocal" column="empresa_formas_de_entrega_permite_comer_no_local"/>


    <result property="exibirTelaBusca" column="empresa_formas_de_entrega_exibir_tela_busca" />
    <result property="permiteUsarGps" column="empresa_formas_de_entrega_permite_usar_gps" />
    <result property="priorizarLocalizacao" column="empresa_formas_de_entrega_priorizar_localizacao" />
    <result property="perguntarEnderecoInicio" column="empresa_formas_de_entrega_perguntar_endereco_inicio" />
    <result property="bairroOpcional" column="empresa_formas_de_entrega_bairro_opcional" />
    <result property="cepObrigatorio" column="empresa_formas_de_entrega_cep_obrigatorio" />
    <result property="bloquearBairroAposCEP" column="empresa_formas_de_entrega_bloquear_bairro_apos_cep" />
    <result property="complementoObrigatorio" column="empresa_formas_de_entrega_complemento_obrigatorio"/>
    <result property="selecionarBairroDaZona" column="empresa_formas_de_entrega_selecionar_bairro_da_zona" />

    <result property="arquivoKML" column="empresa_formas_de_entrega_arquivo_kml" />
    <result property="arquivoGeoJson" column="empresa_formas_de_entrega_arquivo_geo_json" />

    <result property="taxaFixa" column="empresa_formas_de_entrega_taxa_fixa" />
    <result property="arredondarDistancias" column="empresa_formas_de_entrega_arredondar_distancias" />
    <result property="naoUsarCidadePadrao" column="empresa_formas_de_entrega_nao_usar_cidade_padrao" />

    <result property="naoEnviarBairro" column="empresa_formas_de_entrega_nao_enviar_bairro" />

    <association  property="formaDeEntrega"   resultMap="formaEntrega.formaEntregaRM"/>
    <collection property="raiosDeCobranca"  resultMap="raioDeCobranca.raioDeCobrancaRM"/>
    <collection property="alcances"  resultMap="alcance.alcanceRM"/>

    <collection property="zonasDeEntrega"  resultMap="zonaDeEntrega.zonaDeEntregaRM"/>

    <collection property="cidadesQueEntrega" resultMap="cidade.cidadeRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="formaEntregaEmpresaRM" prefix="true">
    select * from  empresa_formas_de_entrega
                   left join raio_de_cobranca on raio_de_cobranca.empresa_forma_de_entrega_id = empresa_formas_de_entrega.id
                   left join alcance on alcance.forma_de_entrega_empresa_id = empresa_formas_de_entrega.id
                   left join forma_de_entrega on forma_de_entrega.id = forma_de_entrega_id
                   left join zona_de_entrega on zona_de_entrega.empresa_forma_de_entrega_id = empresa_formas_de_entrega.id
                   left join cidades_entrega on cidades_entrega.empresa_forma_de_entrega_id = empresa_formas_de_entrega.id
                   left join cidade on cidades_entrega.cidade_id = cidade.id
        where empresa_formas_de_entrega.empresa_id = #{idEmpresa}
    <if test="formaDeEntrega">
      and empresa_formas_de_entrega.forma_de_entrega_id = #{formaDeEntrega}
    </if>
    order by zona_de_entrega.nome;
  </select>

  <insert id="insira" parameterType="FormaDeEntregaEmpresa" keyProperty="id">
    insert into empresa_formas_de_entrega
              (empresa_id, forma_de_entrega_id, valor_minimo_frete_gratis, valor_minimo_pedido, valor_maximo_pedido, taxa_extra_retorno,tipo_de_cobranca, ativa, bairro_opcional,
               permite_agendamento,  exibir_tela_busca, tempo_minimo, tempo_maximo, selecionar_bairro_da_zona, agendamento_obrigatorio, cep_obrigatorio,
               agendamento_limite_minimo, agendamento_limite_maximo, tempo_minimo_retirada, tempo_maximo_retirada, nao_usar_cidade_padrao,
      limite_pedidos_agendados, complemento_obrigatorio, permite_usar_gps, perguntar_endereco_inicio, permite_comer_no_local, taxa_fixa, arredondar_distancias, priorizar_localizacao,
    intervalo_agendamento, nao_enviar_bairro, bloquear_bairro_apos_cep)
         values(  #{empresa.id}, #{formaDeEntrega.id}, #{valorMinimoFreteGratis}, #{valorMinimoPedido}, #{valorMaximoPedido}, #{taxaExtraRetorno}, #{tipoDeCobranca}, #{ativa}, #{bairroOpcional},
                  #{permiteAgendamento}, #{exibirTelaBusca}, #{tempoMinimo}, #{tempoMaximo}, #{selecionarBairroDaZona}, #{agendamentoObrigatorio}, #{cepObrigatorio},
                  #{agendamentoLimiteMinimo}, #{agendamentoLimiteMaximo}, #{tempoMinimoRetirada}, #{tempoMaximoRetirada}, #{naoUsarCidadePadrao},
      #{limitePedidosAgendados}, #{complementoObrigatorio}, #{permiteUsarGps}, #{perguntarEnderecoInicio}, #{permiteComerNoLocal}, #{taxaFixa}, #{arredondarDistancias}, #{priorizarLocalizacao}
    , #{intervaloAgendamento}, #{naoEnviarBairro}, #{bloquearBairroAposCEP});
  </insert>

  <update id="atualizeArquivoKML">
    update empresa_formas_de_entrega
    set arquivo_kml = #{arquivoKML},
    arquivo_geo_json = #{arquivoGeoJson},
    perguntar_endereco_inicio = #{perguntarEnderecoInicio}
        where id = #{id};
  </update>

  <update id="atualizeCepObigatorio">
    update empresa_formas_de_entrega
       set cep_obrigatorio = #{cepObrigatorio}  where id = #{id};
  </update>

  <update id="atualizeTipoCobranca">
    update empresa_formas_de_entrega
      set tipo_de_cobranca =  #{tipoDeCobranca}
        where id = #{id};
  </update>
  <update id="atualize">
    update empresa_formas_de_entrega
    set valor_minimo_frete_gratis = #{valorMinimoFreteGratis},
        valor_minimo_pedido =  #{valorMinimoPedido},
        valor_maximo_pedido =  #{valorMaximoPedido},
        taxa_extra_retorno =  #{taxaExtraRetorno},
        tipo_de_cobranca =  #{tipoDeCobranca},
        permite_agendamento = #{permiteAgendamento},
        intervalo_agendamento = #{intervaloAgendamento},
        exibir_tela_busca = #{exibirTelaBusca}, selecionar_bairro_da_zona = #{selecionarBairroDaZona},
        ativa =  #{ativa}, tempo_minimo = #{tempoMinimo}, tempo_maximo = #{tempoMaximo},
        agendamento_obrigatorio = #{agendamentoObrigatorio},
        bairro_opcional  = #{bairroOpcional},
        cep_obrigatorio = #{cepObrigatorio},
        nao_usar_cidade_padrao = #{naoUsarCidadePadrao},
        complemento_obrigatorio = #{complementoObrigatorio},
        agendamento_limite_minimo = #{agendamentoLimiteMinimo},
        agendamento_limite_maximo = #{agendamentoLimiteMaximo},
        tempo_minimo_retirada = #{tempoMinimoRetirada},
        arredondar_distancias = #{arredondarDistancias},
        tempo_maximo_retirada = #{tempoMaximoRetirada},
        limite_pedidos_agendados = #{limitePedidosAgendados},
        permite_usar_gps = #{permiteUsarGps},
        perguntar_endereco_inicio = #{perguntarEnderecoInicio},
        priorizar_localizacao = #{priorizarLocalizacao},
        nao_perguntar_horario = #{naoPerguntarHorario},
        permite_comer_no_local = #{permiteComerNoLocal},
        bloquear_bairro_apos_cep = #{bloquearBairroAposCEP},
        taxa_fixa = #{taxaFixa},
        nao_enviar_bairro = #{naoEnviarBairro}

    where id = #{id};
  </update>

</mapper>
