<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="opcaoDeAdicionalDeProduto">
  <resultMap id="opcaoDeAdicionalDeProdutoRM" type="OpcaoDeAdicionalDeProduto">
    <id property="id" column="opcao_adicional_produto_id"/>

    <result property="nome" column="opcao_adicional_produto_nome"/>
    <result property="descricao" column="opcao_adicional_produto_descricao"/>
    <result property="valor" column="opcao_adicional_produto_valor"/>
    <result property="disponivel" column="opcao_adicional_produto_disponivel"/>
    <result property="linkImagem" column="opcao_adicional_produto_link_imagem"/>
    <result property="codigoPdv" column="opcao_adicional_produto_codigo_pdv"/>
    <result property="idIfood" column="opcao_adicional_produto_id_ifood"/>
    <result property ="idAdicional" column="opcao_adicional_produto_adicional_produto_id"/>

    <result property ="idProduto" column="opcao_adicional_produto_produto_id"/>
    <result property ="qtdeMaxima" column="opcao_adicional_produto_qtde_maxima"/>
    <result property ="qtdeMinima" column="opcao_adicional_produto_qtde_minima"/>

    <association property="opcaoNaEmpresa"   resultMap="opcaoNaEmpresa.opcaoNaEmpresaDaOpcaoRM"/>
    <association property="template"   resultMap="produtoTemplate.produtoTemplateOpcaoRM"/>
    <collection  property="dependencias"   resultMap="dependenciaOpcaoDeAdicional.dependenciaOpcaoDeAdicionalRM"/>

    <association property="produtoTamanho"   resultMap="produtoTemplate.opcaoProdutoTamanhoRM"/>
    <association property="configuracaoFiscal"   resultMap="configuracaoFiscalOpcao.configuracaoFiscalOpcaoRM"/>
    <association property="insumo" columnPrefix="opcao_"  resultMap="insumo.insumoDoEstoqueRM"/>

  </resultMap>

  <resultMap id="opcaoComAdicionalRM" type="OpcaoDeAdicionalDeProduto">
    <id property="id" column="opcao_adicional_produto_id"/>

    <result property="nome" column="opcao_adicional_produto_nome"/>
    <result property="descricao" column="opcao_adicional_produto_descricao"/>
    <result property="valor" column="opcao_adicional_produto_valor"/>
    <result property="disponivel" column="opcao_adicional_produto_disponivel"/>
    <result property="linkImagem" column="opcao_adicional_produto_link_imagem"/>
    <result property="codigoPdv" column="opcao_adicional_produto_codigo_pdv"/>
    <result property="idIfood" column="opcao_adicional_produto_id_ifood"/>
    <result property ="idAdicional" column="opcao_adicional_produto_adicional_produto_id"/>

    <result property ="idProduto" column="opcao_adicional_produto_produto_id"/>
    <result property ="qtdeMaxima" column="opcao_adicional_produto_qtde_maxima"/>
    <result property ="qtdeMinima" column="opcao_adicional_produto_qtde_minima"/>

    <association property="adicional"   resultMap="adicionalDeProduto.adicionalCampoRM"/>
  </resultMap>

  <resultMap id="opcaoDependenteRM" type="OpcaoDeAdicionalDeProduto">
    <id property="id" column="dependencia_opcao_adicional_opcao_dependente_id"/>

  </resultMap>

  <resultMap id="opcaoDeAdicionalDeProdutoCampoRM" type="OpcaoDeAdicionalDeProduto">
    <id property="id" column="id"/>

    <result property="nome" column="nome"/>
    <result property="descricao" column="descricao"/>
    <result property="valor" column="valor"/>
    <result property="codigoPdv" column="codigo_pdv"/>
    <result property="disponivel" column="disponivel"/>
    <result property="linkImagem" column="ink_imagem"/>
    <result property="idIfood" column="id_ifood"/>

    <result property ="idProduto" column="produto_id"/>
    <result property ="idTamanho" column="produto_tamanho_id"/>

    <association property="adicional"   resultMap="adicionalDeProduto.adicionalCampoRM"   />
    <association property="opcaoNaEmpresa"   resultMap="opcaoNaEmpresa.opcaoNaEmpresaCampoRM"/>
    <association property="configuracaoFiscal"   resultMap="configuracaoFiscalOpcao.configuracaoFiscalOpcaoRM"/>

  </resultMap>

  <resultMap id="complementoDTORM" type="OpcaoDeAdicionalDTO">
    <id property="id" column="id"/>
    <result property="nome" column="nome"/>
    <result property="codigo" column="codigo"/>
    <result property="nomeAdicional" column="nome_adicional"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="opcaoDeAdicionalDeProdutoRM" prefix="true">
    select * from opcao_adicional_produto
    <if test="idEmpresa != null">
      left join opcao_na_empresa on opcao_na_empresa.opcao_id = opcao_adicional_produto.id
      and opcao_na_empresa.empresa_id = #{idEmpresa}
    </if>
    left join configuracao_fiscal_opcao on configuracao_fiscal_opcao.opcao_adicional_produto_id = opcao_adicional_produto.id
    left join origem_produto on origem_produto_id = origem_produto.id
    left join tipo_de_tributacao_ipi on tipo_de_tributacao_ipi_id = tipo_de_tributacao_ipi.id
    left join selo_de_controle_ipi on selo_de_controle_ipi_id = selo_de_controle_ipi.id
      where
      opcao_adicional_produto.id = #{id}
  </select>


  <select id="selecioneComplementos" parameterType="map" resultMap="complementoDTORM" >
      select distinct opcao.id id,
             opcao.nome nome,
             opcao.codigo_pdv codigo,
             ap.nome nome_adicional
        from opcao_adicional_produto opcao  join adicional_produto ap on ap.id = opcao.adicional_produto_id
                                            join produto_adicional_produto pap on ap.id = pap.adicional_produto_id


         where pap.catalogo_id = #{idCatalogo}
             <if test="codigoPdv">
                 and opcao.codigo_pdv = #{codigoPdv}
             </if>
            <if test="like">
              and opcao.nome like #{like}
            </if>

            order by ap.id  limit #{inicio}, #{total}

  </select>

  <insert id="insiraOpcao">
    INSERT INTO opcao_adicional_produto
       (nome, valor,  adicional_produto_id, disponivel, descricao, produto_template_opcao_id,
         link_imagem, codigo_pdv, id_ifood, produto_id, produto_tamanho_id, qtde_maxima, oculta)
    VALUES
      ( #{nome} ,   #{valor}, #{adicionalProduto.id} , #{disponivel} , #{descricao}, #{template.id},
      #{linkImagem}, #{codigoPdv}, #{idIfood}, #{produto.id}, #{tamanho.id}, #{qtdeMaxima}, #{oculta})
  </insert>

  <insert id="insiraOpcoes">
    INSERT INTO opcao_adicional_produto
    (nome, valor,  adicional_produto_id, disponivel, descricao, produto_template_opcao_id, link_imagem, codigo_pdv, id_ifood, qtde_maxima,oculta)
    VALUES
    <foreach item="opcao" collection="dados" open="" separator="," close="">
      ( #{opcao.nome} ,   #{opcao.valor}, #{opcao.adicionalProduto.id} , #{opcao.disponivel} , #{opcao.descricao}, #{opcao.template.id},
        #{opcao.linkImagem}, #{codigoPdv}, #{idIfood},  #{qtdeMaxima}, #{oculta})
    </foreach>
  </insert>

  <insert id="atualizeOpcoes">
    INSERT INTO opcao_adicional_produto
    (id, nome, valor,  adicional_produto_id, disponivel, descricao, link_imagem, codigo_pdv)
    VALUES
    <foreach item="opcao" collection="dados" open="" separator="," close="">
      (#{opcao.id}, #{opcao.nome} ,   #{opcao.valor}, #{opcao.adicionalProduto.id}, #{opcao.disponivel} , #{opcao.descricao},
          #{opcao.linkImagem}, #{codigoPdv})
    </foreach>

      ON DUPLICATE KEY UPDATE
      nome = VALUES(nome),
      valor = VALUES(valor),
      descricao = VALUES(descricao),
      disponivel = VALUES(disponivel),
      link_imagem = VALUES(link_imagem),
      codigo_pdv = VALUES(codigo_pdv)
  </insert>

  <insert id="insiraDependencia">
     insert into dependencia_opcao_adicional
       (opcao_id, adicional_id, opcao_dependente_id) values (#{opcao.id},#{adicional.id}, #{opcaoDependente.id});
   </insert>

  <update id="removaTodasDependencias" parameterType="map">
      delete from dependencia_opcao_adicional where opcao_id = #{idOpcao}
  </update>

  <update id="atualizeDisponibilidade" parameterType="map">
    update opcao_adicional_produto set disponivel = #{disponivel}
    where id = #{id};
  </update>

  <update id="atualizeDisponibilidades">
    update opcao_adicional_produto
    set
    disponivel = #{disponivel}
       where  id in

    <foreach item="id" collection="ids" open="( " separator="," close=")">
      #{id}
    </foreach>
  </update>

  <update id="atualizeValor" parameterType="map">
    update opcao_adicional_produto set valor = #{valor} where id = #{id}
  </update>

  <update id="atualizeDescricao" parameterType="map">
    update opcao_adicional_produto set descricao = #{descricao} where id = #{id}
  </update>

  <update id="atualizeDisponilidadeOpcoes">
    update opcao_adicional_produto set   disponivel = #{disponivel} where id = #{id}
  </update>

  <update id="removaTodos" parameterType="map">
    update opcao_adicional_produto
        set opcao_adicional_produto.excluido = true
    where   adicional_produto_id = #{idAdicional}
        and id   in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
              #{id}
            </foreach>;
  </update>


  <update  id="removerDuplicados" parameterType="map">
    update opcao_adicional_produto set excluido = true where id in (select t.id from  ( select opcao.id  from opcao_adicional_produto opcao join adicional_produto on adicional_produto.id = adicional_p
      roduto_id join produto on produto.id = produto_id  where opcao.excluido is not true and opcao.codigo_pdv is not null and produto.removido is not true and opcao.codigo_pdv != ''  group by opcao.codigo_pdv
                                                                                          , opcao.nome, adicional_produto_id having count(*) > 1 ) t );
  </update>
</mapper>
