<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="contatoEmpresa">
  <resultMap id="contatoEmpresaRM" type="ContatoEmpresa">
    <id property="id" column="contato_empresa_id"/>
    <result property="nome" column="contato_empresa_nome"/>
    <result property="empresa" column="contato_empresa_empresa"/>
    <result property="email" column="contato_empresa_email"/>
    <result property="telefone" column="contato_empresa_telefone"/>
    <result property="horario" column="contato_empresa_horario"/>
    <result property="cadastrado" column="contato_empresa_cadastrado"/>
    <result property="instagram" column="contato_empresa_instagram"/>
    <result property="qtdePedidos" column="contato_empresa_qtde_pedidos"/>
    <result property="ticketMedio" column="contato_empresa_ticket_medio"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="contatoEmpresaRM" prefix="true">
      select * from contato_empresa
    <choose>
      <when test="naoCadastrou">
        where cadastrado is false
      </when>
    </choose>
  </select>

  <update id="marqueCadastrou" parameterType="map">
    update contato_empresa set cadastrado = #{cadastrou} where id = #{id}
  </update>

</mapper>
