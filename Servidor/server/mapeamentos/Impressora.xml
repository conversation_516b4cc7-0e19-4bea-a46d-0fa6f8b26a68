<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="impressora">
  <resultMap id="impressoraRM" type="Impressora">
    <id property="id" column="impressora_id"/>

    <result property="nome" column="impressora_nome"/>
    <result property="setor" column="impressora_setor"/>
    <result property="layout" column="impressora_layout"/>
    <result property="tamanhoPapel" column="impressora_tamanho" />
    <result property="comandosFimImpressao" column="impressora_comandos_fim_impressao" />
    <result property="imprimirAutomatico" column="impressora_imprimir_automatico" />
    <result property="naoImprimePedidosMesa" column="impressora_nao_imprime_pedidos_mesa" />
    <result property="naoImprimePedidosDelivery" column="impressora_nao_imprime_pedidos_delivery" />
    <result property="naoInicializaImpressao" column="impressora_nao_inicializa_impressao" />
    <result property="imprimirNFCe" column="impressora_imprimir_nfce" />
  </resultMap>

  <resultMap id="impressoraAtivaRM" type="Impressora">
      <id property="id" column="impressora_ativa_id"/>
      <result property="nome" column="impressora_ativa_nome"/>
      <result property="setor" column="impressora_ativa_setor"/>
      <result property="naoImprimePedidosMesa" column="impressora_nao_imprime_pedidos_mesa" />
      <result property="naoImprimePedidosDelivery" column="impressora_nao_imprime_pedidos_delivery" />

  </resultMap>

  <insert id="insira" parameterType="Impressora" keyProperty="id">
    insert into impressora (nome, tamanho, comandos_fim_impressao, setor, layout, config_impressao_id, imprimir_automatico,
    nao_imprime_pedidos_mesa, nao_imprime_pedidos_delivery, imprimir_nfce)
      values(#{nome}, #{tamanhoPapel}, #{comandosFimImpressao}, #{setor}, #{layout}, #{configImpressao.id}, #{imprimirAutomatico},
    #{naoImprimePedidosMesa}, #{naoImprimePedidosDelivery}, #{imprimirNFCe});
  </insert>


  <update id="atualize">
    update impressora
    set nome = #{nome}, tamanho = #{tamanhoPapel}, imprimir_automatico = #{imprimirAutomatico},
    comandos_fim_impressao = #{comandosFimImpressao}, setor = #{setor},
    layout = #{layout},
    nao_imprime_pedidos_mesa = #{naoImprimePedidosMesa},
    nao_imprime_pedidos_delivery = #{naoImprimePedidosDelivery},
    imprimir_nfce = #{imprimirNFCe}
    where id = #{id}
  </update>

  <update id="remova">
    delete from categoria_impressora where impressora_id= #{id};
    delete from  impressora  where id = #{id};
  </update>
</mapper>
