<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="registroDeOperacao">

  <resultMap id="registroDeOperacaoRM" type="RegistroDeOperacao">
    <id property="id" column="registro_de_operacao_id"/>

    <result property="descricao" column="registro_de_operacao_descricao"/>
    <result property="operacao" column="registro_de_operacao_operacao"/>
    <result property="horario" column="registro_de_operacao_horario"/>
    <result property="ip" column="registro_de_operacao_ip" />
    <result property="tipoObjeto" column="registro_de_operacao_tipo_objeto" />
    <result property="idObjeto" column="registro_de_operacao_id_objeto" />
    <result property="valorNovo" column="registro_de_operacao_valor_novo" />

    <association property="usuario"  resultMap="usuario.usuarioResultMap"/>
    <association property="clienteApi" resultMap="clienteApi.clienteApiResultMap"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="registroDeOperacaoRM" prefix="true">
    select  *
        from registro_de_operacao left join usuario on usuario.id = usuario_id
                                  left join cliente_api on cliente_api.id = cliente_api_id
    where registro_de_operacao.empresa_id = #{idEmpresa}
    <if test="idProduto">
        and id_objeto = #{idProduto}
        and tipo_objeto = 'Produto'
    </if>

    <if test="idInsumo">
      and id_objeto = #{idInsumo}
      and tipo_objeto = 'Insumo'
    </if>

    <if test="texto">
        and (descricao like #{texto} or valor_novo like #{texto})
    </if>
    <if test="operacao">
      and operacao = #{operacao}
    </if>
    <if test="tipoObjeto">
      and tipo_objeto = #{tipoObjeto}
    </if>
    order by horario desc
    <if test="total != null">
      limit #{inicio},#{total}
    </if>
  </select>
  <select id="selecioneQuantidade" parameterType="map" resultType="int" prefix="true">
    select  count(distinct registro_de_operacao.id) from registro_de_operacao join usuario on usuario.id = usuario_id
    where registro_de_operacao.empresa_id = #{idEmpresa}
    <if test="idProduto">
      and id_objeto = #{idProduto}
      and tipo_objeto = 'Produto'
    </if>

    <if test="idInsumo">
      and id_objeto = #{idInsumo}
      and tipo_objeto = 'Insumo'
    </if>

    <if test="texto">
      and (descricao like #{texto} or valor_novo like #{texto})
    </if>
    <if test="operacao">
      and operacao = #{operacao}
    </if>
    <if test="tipoObjeto">
      and tipo_objeto = #{tipoObjeto}
    </if>
  </select>
</mapper>
