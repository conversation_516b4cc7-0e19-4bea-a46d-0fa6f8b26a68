<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="configuracoesNotaFiscal">
  <resultMap id="configuracoesNotaFiscalRM" type="ConfiguracoesNotaFiscal">
    <id property="id" column="configuracoes_nota_fiscal_id"/>

    <result property="tipoImpressaoDanfe" column="configuracoes_nota_fiscal_tipo_impressao_danfe"/>
    <result property="idToken" column="configuracoes_nota_fiscal_id_token"/>
    <result property="csc" column="configuracoes_nota_fiscal_csc"/>
    <result property="ambiente" column="configuracoes_nota_fiscal_ambiente"/>
    <result property="versaoNFe" column="configuracoes_nota_fiscal_versao_nfe"/>
    <result property="seriePadrao" column="configuracoes_nota_fiscal_serie_padrao"/>
    <result property="numeroInicial" column="configuracoes_nota_fiscal_numero_inicial"/>
    <result property="inscricaoEstadual" column="configuracoes_nota_fiscal_inscricao_estadual"/>
    <result property="cnae" column="configuracoes_nota_fiscal_cnae"/>
    <result property="naturezaDaOperacao" column="configuracoes_nota_fiscal_natureza_da_operacao"/>
    <result property="enviarAutomaticamente" column="configuracoes_nota_fiscal_enviar_automaticamente"/>

    <association property="empresa" resultMap="empresa.empresaRM"/>
    <association property="certificado" resultMap="certificado.certificadoRM"/>
    <association property="regimeTributario" resultMap="regimeTributario.regimeTributarioRM"/>
    <association property="tributacaoVendaProducaoPropria" columnPrefix="propria_" resultMap="tributacaoNaturezaOperacao.tributacaoNaturezaOperacaoRM"/>
    <association property="tributacaoVendaProdutosTerceiros" columnPrefix="terceiros_" resultMap="tributacaoNaturezaOperacao.tributacaoNaturezaOperacaoRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="configuracoesNotaFiscalRM" prefix="true">
    select * from configuracoes_nota_fiscal left join certificado on configuracoes_nota_fiscal.certificado_id = certificado.id
      join empresa on configuracoes_nota_fiscal.empresa_id = empresa.id
        left join regime_tributario on configuracoes_nota_fiscal.regime_tributario_id = regime_tributario.id
    left join tributacao_natureza_operacao as propria_tributacao_natureza_operacao on configuracoes_nota_fiscal.tributacao_natureza_operacao_propria_id = propria_tributacao_natureza_operacao.id
    left join tipo_de_tributacao_icms as propria_tipo_de_tributacao_icms on propria_tributacao_natureza_operacao.tipo_de_tributacao_icms_id = propria_tipo_de_tributacao_icms.id
    left join cfop as propria_cfop on propria_tributacao_natureza_operacao.cfop_id = propria_cfop.id
    left join modalidade_base_de_calculo_icms as propria_modalidade_base_de_calculo_icms on propria_tributacao_natureza_operacao.modalidade_base_de_calculo_icms_id = propria_modalidade_base_de_calculo_icms.id
    left join modalidade_base_de_calculo_icms_st as propria_modalidade_base_de_calculo_icms_st on propria_tributacao_natureza_operacao.modalidade_base_de_calculo_icms_st_id = propria_modalidade_base_de_calculo_icms_st.id
    left join tributacao_natureza_operacao as terceiros_tributacao_natureza_operacao on configuracoes_nota_fiscal.tributacao_natureza_operacao_terceiros_id = terceiros_tributacao_natureza_operacao.id
    left join tipo_de_tributacao_icms as terceiros_tipo_de_tributacao_icms on terceiros_tributacao_natureza_operacao.tipo_de_tributacao_icms_id = terceiros_tipo_de_tributacao_icms.id
    left join cfop as terceiros_cfop on terceiros_tributacao_natureza_operacao.cfop_id = terceiros_cfop.id
    left join modalidade_base_de_calculo_icms as terceiros_modalidade_base_de_calculo_icms on terceiros_tributacao_natureza_operacao.modalidade_base_de_calculo_icms_id = terceiros_modalidade_base_de_calculo_icms.id
    left join modalidade_base_de_calculo_icms_st as terceiros_modalidade_base_de_calculo_icms_st on terceiros_tributacao_natureza_operacao.modalidade_base_de_calculo_icms_st_id = terceiros_modalidade_base_de_calculo_icms_st.id
    where configuracoes_nota_fiscal.empresa_id = #{idEmpresa}
  </select>




  <update id="atualizeNatTribProprio" parameterType="map">
    update configuracoes_nota_fiscal
    set tributacao_natureza_operacao_propria_id = #{tributacaoVendaProducaoPropria.id}
    where empresa_id = #{empresa.id}
  </update>

  <update id="atualizeNatTribTerceiros" parameterType="map">
    update configuracoes_nota_fiscal
    set tributacao_natureza_operacao_terceiros_id = #{tributacaoVendaProdutosTerceiros.id}
    where empresa_id = #{empresa.id}
  </update>


  <update id="atualize" parameterType="map">
    update configuracoes_nota_fiscal
    set tipo_impressao_danfe = #{tipoImpressaoDanfe},
    id_token = #{idToken},
    csc = #{csc},
    ambiente = #{ambiente},
    versao_nfe = #{versaoNFe},
    certificado_id = #{certificado.id},
    serie_padrao = #{seriePadrao},
    numero_inicial = #{numeroInicial},
    inscricao_estadual = #{inscricaoEstadual},
    cnae = #{cnae},
    natureza_da_operacao = #{naturezaDaOperacao},
    enviar_automaticamente = #{enviarAutomaticamente}

    where empresa_id = #{empresa.id}
  </update>

  <update id="remova" parameterType="map">
    update configuracoes_nota_fiscal
    set removida = true
    where empresa_id = #{empresa.id}
  </update>

<insert id="insira" parameterType="map">
    insert into configuracoes_nota_fiscal
    (empresa_id, tipo_impressao_danfe, id_token, csc, ambiente, versao_nfe, certificado_id, serie_padrao,
  numero_inicial, inscricao_estadual, cnae, regime_tributario_id, tributacao_natureza_operacao_propria_id, tributacao_natureza_operacao_terceiros_id, natureza_da_operacao, enviar_automaticamente)
    values
    (#{empresa.id}, #{tipoImpressaoDanfe}, #{idToken}, #{csc}, #{ambiente}, #{versaoNFe}, #{certificado.id}, #{seriePadrao},
  #{numeroInicial},  #{inscricaoEstadual}, #{cnae}, #{regimeTributario.id}, #{tributacaoVendaProducaoPropria.id}, #{tributacaoVendaProdutosTerceiros.id}, #{naturezaDaOperacao}, #{enviarAutomaticamente})
  </insert>


  <!--
  <create id="crieTabela" parameterType="map">
    CREATE TABLE IF NOT EXISTS configuracoes_nota_fiscal (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    empresa_id BIGINT NOT NULL,
    certificado_id BIGINT,
    tipo_impressao_danfe VARCHAR(255),
    id_token VARCHAR(255),
    csc VARCHAR(255),
    ambiente VARCHAR(255),
    versao_nfe VARCHAR(255),
    removida BOOLEAN DEFAULT FALSE,

    FOREIGN KEY (empresa_id) REFERENCES empresa(empresa_id),
    FOREIGN KEY (certificado_id) REFERENCES certificado(certificado_id)
    )
  </create>
  -->
</mapper>
