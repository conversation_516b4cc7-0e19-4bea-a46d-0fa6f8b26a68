<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="notificacao">
  <resultMap id="notificacaoResultMap" type="Notificacao">
    <id property="id" column="notificacao_id"/>

    <result property="ativada" column="notificacao_ativada"/>
    <result property="tipoDeNotificacao" column="notificacao_tipo_de_notificacao"/>

    <result property="mensagem" column="notificacao_mensagem"/>
    <result property="qtdeDeDiasNovaNotificacao" column="notificacao_qtde_dias_nova_notificacao"/>
    <result property="qtdeDiasAtiva" column="notificacao_qtde_dias_ativa"/>
    <result property="encurtarLinks" column="notificacao_encurtar_links"/>

    <result property="podeDesativar" column="notificacao_pode_desativar"/>

    <result property="fazerPreview" column="notificacao_fazer_preview"/>
    <result property="marcarComoLida" column="notificacao_marcar_como_lida"/>

    <result property="temMenu" column="notificacao_tem_menu"/>
    <result property="menu" column="notificacao_menu"/>

    <association property="empresa"   resultMap="empresa.empresaRM"/>
  </resultMap>

  <update id="insira" parameterType="Notificacao" keyProperty="id">
    insert into notificacao(ativada, empresa_id, mensagem, tipo_de_notificacao, pode_desativar, qtde_dias_nova_notificacao, qtde_dias_ativa, encurtar_links, marcar_como_lida, tem_menu, menu)
      values(#{ativada}, #{empresa.id}, #{mensagem}, #{tipoDeNotificacao}, #{podeDesativar}, #{qtdeDeDiasNovaNotificacao}, #{qtdeDiasAtiva}, #{encurtarLinks}, #{marcarComoLida}, #{temMenu}, #{menu})
            on duplicate  key  update ativada = #{ativada}
  </update>

  <update id="atualize" parameterType="Notificacao" keyProperty="id">
    update notificacao set ativada = #{ativada}, mensagem = #{mensagem}, pode_desativar = #{podeDesativar},
      qtde_dias_nova_notificacao = #{qtdeDeDiasNovaNotificacao}, qtde_dias_ativa = #{qtdeDiasAtiva},
    encurtar_links = #{encurtarLinks}, fazer_preview = #{fazerPreview}, marcar_como_lida = #{marcarComoLida},
    tem_menu = #{temMenu}, menu = #{menu}
      where    id = #{id}   and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeAtivado">
      update notificacao set   ativada = #{ativada}  where   empresa_id = #{idEmpresa} and tipo_de_notificacao =  #{tipo};
  </update>

  <update id="atualizeEncurtarLinks">
    update notificacao set   encurtar_links = #{encurtarLinks}  where   empresa_id = #{idEmpresa} and tipo_de_notificacao =  #{tipo};
  </update>


  <select id="selecione" parameterType="map" resultMap="notificacaoResultMap" prefix="true">
    select    notificacao.*,
              empresa.id,
              empresa.nome,
              empresa.dominio,
              empresa.meio_de_envio,
              em.*,
              modulo.*,
              numero_whatsapp.*
     from notificacao join empresa on empresa.id = notificacao.empresa_id
      left join numero_whatsapp on numero_whatsapp.empresa_id = empresa.id and numero_whatsapp.principal is true
    left join empresa_modulo em on em.empresa_id = empresa.id
    left join modulo on modulo.id = em.modulo_id

    where
            <choose>
              <when test="todas">
                 1  =  1
              </when>

              <otherwise>
                empresa.id = #{idEmpresa}
              </otherwise>

            </choose>

              <if test="tipoDeNotificacao != null">
                and notificacao.tipo_de_notificacao = #{tipoDeNotificacao}
              </if>

              <if test="ativa != null">
                and notificacao.ativada is true and empresa.bloqueada is not true
              </if>
  </select>
</mapper>
