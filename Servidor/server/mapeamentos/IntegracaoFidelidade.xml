<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="integracaoFidelidade">
  <resultMap id="integracaoFidelidadeRM" type="IntegracaoFidelidade">
    <id property="id" column="integracao_fidelidade_id"/>

    <result property="sistema" column="integracao_fidelidade_sistema"/>

    <result property="loja" column="integracao_fidelidade_loja"/>
    <result property="dataAtivacao" column="integracao_fidelidade_data_ativacao"/>
  </resultMap>


  <update id="remova">
    delete from   integracao_fidelidade
      where integracao_fidelidade.id = #{id} and empresa_id = #{idEmpresa};

  </update>


</mapper>
