<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="valorDeAdicionaisMultiplaEscolha">

  <resultMap id="valorDeAdicionaisMultiplaEscolhaRM" type="ValorDeAdicionaisMultiplaEscolha">
    <id property="id" column="valor_de_adicionais_multipla_escolha_id"/>

    <association property="lista0" columnPrefix="lista0_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista1" columnPrefix="lista1_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista2" columnPrefix="lista2_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista3" columnPrefix="lista3_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista4" columnPrefix="lista4_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista5" columnPrefix="lista5_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista6" columnPrefix="lista6_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista7" columnPrefix="lista7_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista8" columnPrefix="lista8_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista9" columnPrefix="lista9_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>

    <association property="produto" resultMap="produto.produtoResultMap"/>
    <association property="item" resultMap="itemPedido.itemPedidoRM"/>

  </resultMap>

  <resultMap id="valorDeAdicionaisMultiplaEscolhaPedidoRM" type="ValorDeAdicionaisMultiplaEscolha">
    <id property="id" column="valor_de_adicionais_multipla_escolha_id"/>

    <association property="lista0" columnPrefix="lista0_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista1" columnPrefix="lista1_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista2" columnPrefix="lista2_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista3" columnPrefix="lista3_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista4" columnPrefix="lista4_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista5" columnPrefix="lista5_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista6" columnPrefix="lista6_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista7" columnPrefix="lista7_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista8" columnPrefix="lista8_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
    <association property="lista9" columnPrefix="lista9_" resultMap="listaOpcoesEscolhidas.listaOpcoesEscolhidasRM"/>
  </resultMap>

  <delete id="removaDoPedido" parameterType="map">
    delete vo.* from valor_de_opcao_multipla_escolha vo,
                         lista_opcoes_escolhidas l,
                         valor_de_adicionais_multipla_escolha va
                    where vo.lista_id = l.id and  (va.lista0_id = l.id
                                                   or va.lista1_id = l.id
                                                   or va.lista2_id = l.id
    or va.lista3_id = l.id
    or va.lista4_id = l.id
    or va.lista5_id = l.id
    or va.lista6_id = l.id
    or va.lista7_id = l.id
    or va.lista8_id = l.id
    or va.lista9_id = l.id
      )
            and pedido_id = #{idPedido};
    delete l.*, va.* from valor_de_opcao_multipla_escolha vo,
    lista_opcoes_escolhidas l,
    valor_de_adicionais_multipla_escolha va
    where (va.lista0_id = l.id
    or va.lista1_id = l.id
    or va.lista2_id = l.id
    or va.lista3_id = l.id
    or va.lista4_id = l.id
    or va.lista5_id = l.id
    or va.lista6_id = l.id
    or va.lista7_id = l.id
    or va.lista8_id = l.id
    or va.lista9_id = l.id
    )
    and pedido_id = #{idPedido};
  </delete>
</mapper>
