<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="notificacaovenda">

  <resultMap id="notificacaovendaRM" type="NotificacaoVenda">
      <id property="id" column="notificacao_venda_id"/>

    <result property="codigo" column="notificacao_venda_codigo" />
    <result property="horarioVenda" column="notificacao_venda_horario" />
    <result property="horarioNotificado" column="notificacao_venda_horario_notificado" />
    <result property="origem" column="notificacao_venda_origem" />
    <result property="dados" column="notificacao_venda_dados" />
    <result property="executada" column="notificacao_venda_executada" />
    <result property="erro" column="notificacao_venda_erro" />
    <result property="ignorar" column="notificacao_venda_ignorar" />
    <result property="rede" column="notificacao_venda_rede" />
    <result property="loja" column="notificacao_venda_loja" />

    <discriminator javaType="String" column="notificacao_venda_origem" >
      <case value="ecletica" resultType="NotificacaoVenda"></case>

    </discriminator>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="notificacaovendaRM" prefix="true">
    select *
      from notificacao_venda
         where
            <choose>
              <when test="id">
                id = #{id}
              </when>
              <when test="naoExecutadas">
                executada is not true and ignorar is not true and TIMESTAMPDIFF(MINUTE,horario,now()) > 10

                order by notificacao_venda.id
              </when>
            </choose>

        </select>

  <insert id="insira">
    insert into notificacao_venda (id,  empresa_id, origem,rede, loja, codigo, valor, estorno, horario_venda, horario_notificacao, dados)
         values (#{id}, #{empresa.id}, #{origem}, #{rede}, #{loja}, #{codigo}, #{valor}, #{estorno}, #{horarioVenda}, #{horarioNotificacao}, #{dados})

  </insert>

  <update id="atualize">
    update notificacao_venda
    set executada = #{executada}, erro = #{erro}, ignorar = #{ignorar}
    where id = #{id} and empresa_id = #{empresa.id}

  </update>

</mapper>
