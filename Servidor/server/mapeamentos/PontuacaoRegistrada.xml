<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pontuacaoRegistrada">
  <resultMap id="pontuacaoRegistradaRM" type="PontuacaoRegistrada">
    <id property="id" column="pontuacao_id"/>

    <result property="valor" column="pontuacao_valor"/>
    <result property="pontos" column="pontuacao_pontos"/>
    <result property="pontosUsados" column="pontuacao_pontos_usados"/>
    <result property="pontosVencidos" column="pontuacao_pontos_vencidos"/>
    <result property="horario" column="pontuacao_horario"/>
    <result property="dataVencimento" column="pontuacao_data_vencimento"/>
    <result property="referenciaExterna" column="pontuacao_referencia_externa"/>
    <result property="codigo" column="pontuacao_codigo"/>

    <association property="cartao"  resultMap="cartao.cartaoResultMap"/>
    <association property="empresa"  resultMap="empresa.empresaRM"/>
    <association property="operador"  resultMap="usuario.operadorResultMap"/>

  </resultMap>

  <resultMap id="resumoVendasRM" type="DTOResumoVendaFidelidade">
    <id property="id" column="resumo_id"/>

    <result property="total" column="resumo_total"/>
    <result property="pontos" column="resumo_pontos"/>

    <result property="mes" column="resumo_mes"/>
    <result property="ano" column="resumo_ano"/>
    <result property="dia" column="resumo_dia"/>
  </resultMap>

  <select id="selecione" resultMap="pontuacaoRegistradaRM" prefix="true">
      select pontuacao.*, cartao.*, plano.*,
             contato.* , empresa.*, operador.*
        from pontuacao_registrada pontuacao  join cartao on cartao.id = pontuacao.cartao_id
                                   join plano on  plano.id = cartao.plano_id
                                   join contato on contato.id = cartao.contato_id
                                   join empresa on empresa.id =  pontuacao.empresa_id
                                   left join usuario operador on operador.id = pontuacao.operador_id
           where  empresa.id = #{idEmpresa}  and pontuacao.removida is not true
                <choose>
                    <when test="idPedido">
                      and pontuacao.pedido_id = #{idPedido}
                    </when>

                    <when test="idComanda">
                      and pontuacao.comanda_id = #{idComanda}
                    </when>

                    <when test="idContato">
                      and contato.id = #{idContato}
                    </when>

                    <when test="codigo">
                      and pontuacao.codigo = #{codigo}
                    </when>
                    <when test="referenciaExterna">
                      and pontuacao.referencia_externa = #{referenciaExterna}
                    </when>

                    <when test="migrar">
                      and  90 > datediff(now(),ultima_visita) and  365 > datediff(now(),horario)  and pontuacao.empresa_id = 2 order by  pontuacao.horario ;
                    </when>

               </choose>

              <if test="idPlano">
                and plano.id = #{idPlano}
              </if>

              <if test="dataInicio">
                and  pontuacao.horario  >= #{dataInicio}
              </if>

              <if test="dataFim">
                and  pontuacao.horario    &lt;=  #{dataFim}
              </if>

              <if test="total">
                order by pontuacao.id desc limit #{inicio},#{total}
              </if>
  </select>

  <select id="existe" parameterType="map" resultType="int">
      select count(*) total from pontuacao_registrada
          where  empresa_id = #{idEmpresa} and referencia_externa = #{referenciaExterna}
  </select>

  <select id="selecioneDisponiveis" resultMap="pontuacaoRegistradaRM" prefix="true">
    select  pontuacao.* from  pontuacao_registrada pontuacao
          where data_vencimento is not null and removida is not true and pontos > (pontos_usados + pontos_vencidos)  and  cartao_id =  #{idCartao}
                   and empresa_id = #{idEmpresa}

            order by id
  </select>

  <select id="selecionePontuacaoCorrigir" resultMap="pontuacaoRegistradaRM" prefix="true">
   select pontuacao.*, cartao.*, empresa.*, cartao.contato_id contato_id
    from pontuacao_registrada pontuacao  join cartao on cartao.id = pontuacao.cartao_id
      join empresa on empresa.id =  pontuacao.empresa_id
            where  (pontos_usados + pontos_vencidos) > pontuacao.pontos and pontuacao.removida is not true order by pontuacao.id
  </select>

  <select id="selecioneUltimasUsadas" resultMap="pontuacaoRegistradaRM" prefix="true">
    select  pontuacao.* from  pontuacao_registrada pontuacao
    where data_vencimento is not null and removida is not true and pontos_usados > 0  and  cartao_id =  #{idCartao}
    and empresa_id = #{idEmpresa}

    order by id desc
  </select>

  <select id="selecioneVencidas" resultMap="pontuacaoRegistradaRM" prefix="true">
    select  pontuacao.* ,  cartao.* , plano.*, tipo_de_pontuacao.*,
            contato.*, empresa.*
      from  pontuacao_registrada pontuacao join empresa on empresa.id = empresa_id and empresa.removida is not true
                                          join cartao on cartao.id = pontuacao.cartao_id
                                          join plano on  plano.id = cartao.plano_id
                                          join tipo_de_pontuacao on tipo_de_pontuacao.id = plano.id_tipo_de_pontuacao
                                          join contato on contato.id = cartao.contato_id
        where pontuacao.data_vencimento is not null and
                        pontuacao.removida is not true and (pontuacao.pontos - pontos_usados) > 0 and
                        pontuacao.pontos_vencidos = 0 and datediff(now(), pontuacao.data_vencimento) >  0
              <if test="idCartao"> and cartao.id = #{idCartao}</if>
              <if test="idEmpresa"> and empresa.id = #{idEmpresa}</if>
            order by empresa.id, data_vencimento

  </select>
  <select id="selecioneAhVencer" resultMap="pontuacaoRegistradaRM" prefix="true">
    select  pontuacao.* ,  cartao.* , plano.*, tipo_de_pontuacao.*,
            contato.*, empresa.*
        from  pontuacao_registrada pontuacao join empresa on empresa.id = empresa_id and empresa.removida is not true
                                             join cartao on cartao.id = pontuacao.cartao_id
                                             join plano on  plano.id = cartao.plano_id
                                             join tipo_de_pontuacao on tipo_de_pontuacao.id = plano.id_tipo_de_pontuacao
                                             join contato on contato.id = cartao.contato_id
              where pontuacao.data_vencimento is not null  and empresa.id =  #{idEmpresa} and
                    pontuacao.removida is not true and (pontuacao.pontos - pontos_usados) > 0 and
                    pontuacao.pontos_vencidos = 0 and
                       <choose>

                         <when test="qtdeDiasVencer != null">
                            datediff(pontuacao.data_vencimento, now()) =  #{qtdeDiasVencer}

                           <if test="idCartao"> and cartao.id = #{idCartao}</if>

                         </when>
                         <when test="aVencer">
                            datediff(pontuacao.data_vencimento , now())  > 0 and cartao.id = #{idCartao}

                           <if test="dataInicio != null">
                             and datediff(pontuacao.data_vencimento , #{dataInicio})  >= 0
                           </if>

                           <if test="dataFim != null">
                             and datediff(#{dataFim}, pontuacao.data_vencimento)  >= 0
                           </if>


                            order by data_vencimento
                         </when>
                       </choose>

  </select>

  <select id="selecioneResumo" resultMap="resumoVendasRM">
    select
      uuid() resumo_id,
      sum(valor) resumo_total,
      sum(pontos) resumo_pontos,
      year(horario) resumo_ano,
      month(horario) resumo_mes,
      day(horario) resumo_dia
      from  pontuacao_registrada
        where removida is not true and
        <choose>
          <when test="rede">
            join empresa on empresa.id =  empresa_id
            where empresa.rede = #{rede}
          </when>

          <otherwise>
            empresa_id = #{idEmpresa}
          </otherwise>
        </choose>

        <choose>
            <when test="codigo">
              and  codigo = #{codigo}
            </when>
            <when test="dia">
              and datediff(now(),horario) &lt; 15   group by day(horario)
            </when>
            <when test="mes">
             group by month(horario), year(horario)
            </when>
            <when test="ano">
             group by year(horario)
            </when>
        </choose>
  </select>


  <select id="totalDoPedido" parameterType="map" resultType="int">
    select count(*) total from pontuacao_registrada  where pedido_id = #{idPedido} and empresa_id = #{idEmpresa}
  </select>

  <select id="totalDaComanda" parameterType="map" resultType="int">
    select count(*) total from pontuacao_registrada  where comanda_id = #{idComanda} and empresa_id = #{idEmpresa}
  </select>

  <insert id="insiraAtividades">
    insert into pontuacao_registrada_atividade (pontuacao_registrada_id, atividade_id)
      values
          <foreach item="item" collection="dados" open="" separator="," close="">
            ( #{item.idPontuacao} ,   #{item.idAtividade} )
          </foreach>

  </insert>

  <insert id="insira" parameterType="map">
      insert into pontuacao_registrada
            (valor, cartao_id, horario, pontos, codigo, operador_id, empresa_id, referencia_externa, data_vencimento, pedido_id,
              comanda_id,pontos_usados,pontos_vencidos) values
            ( #{valor}, #{cartao.id}, #{horario}, #{pontos}, #{codigo},  #{operador.id}, #{empresa.id} ,
                #{referenciaExterna}, #{dataVencimento}, #{pedido.id},  #{comanda.id},#{pontosUsados},#{pontosVencidos});
  </insert>


  <select id="selecioneVencidos">
    select id,pontos,cartao_id,horario,data_vencimento,empresa_id, count(*)
            from pontuacao_registrada where   datediff(now(), data_vencimento) >  0
                group by empresa_id;
  </select>

  <update id="atualizePontosUsados">
    update pontuacao_registrada , empresa
        set pontos_usados = #{pontosUsados}
          where pontuacao_registrada.id = #{id} and empresa.id = empresa_id
  </update>

  <update id="atualizePontosVencidos">
    update pontuacao_registrada , empresa
        set pontos_vencidos = #{pontosVencidos}
          where pontuacao_registrada.id = #{id} and empresa.id = empresa_id
  </update>

  <update id="atualizeRemovidoCartao">
    update pontuacao_registrada , empresa
    set pontuacao_registrada.removida = true
    where cartao_id = #{id} and empresa.id = empresa_id
  </update>

  <update id="renoveVencimento" parameterType="map" resultMap="planoRM" prefix="true">
    update pontuacao_registrada , empresa
    set data_vencimento = #{dataVencimento}
    where cartao_id = #{cartao.id} and empresa.id = empresa_id and data_vencimento >= now();
  </update>


  <update id="atualizePontosUsadosEmDulicidade">
    update pontuacao_registrada
    set pontos_usados = pontos
    where id in ( 5704,5718,5746,5776,5780,5785,5791,5793,5795,5797,5799,5801,5803,5805,5884,5886,5899,5902,5911,5919,5921,5928,5933,5936,5956,5958,5970,5974,5985,6103,6105,7041,9548)  ;

  </update>

  <update id="remova">
    update pontuacao_registrada,  empresa
        set   pontuacao_registrada.removida  = #{removida}
          where pontuacao_registrada.id = #{id}  and empresa.id = empresa_id
  </update>

  <update id="voltePontosVencidos">
    update pontuacao_registrada, acao_contato,  empresa, cartao
    set   acao_contato.removida  = true, cartao.pontos =  cartao.pontos + pontuacao_registrada.pontos_vencidos
     where pontuacao_registrada.id = #{id}  and pontuacao_registrada_id = pontuacao_registrada.id and tipo_de_acao = 8
         and pontuacao_registrada.cartao_id = cartao.id    and empresa.id = cartao.empresa_id;

    update pontuacao_registrada set pontos_vencidos = 0 where id = #{id};

  </update>


  <update id="atualizeParaOutroCartao">
    update pontuacao_registrada join acao_contato on pontuacao_registrada_id = pontuacao_registrada.id

        set pontuacao_registrada.cartao_id = #{idCartao},
            acao_contato.cartao_id =   #{idCartao},
            acao_contato.contato_id = #{idContato}

    where  pontuacao_registrada.id = #{id}  and  pontuacao_registrada.empresa_id = #{empresa.id}

  </update>

  <update id="atualizarVencimentoPontosSemVencimentoBaseadoUltimaVisita">
    update pontuacao_registrada join cartao on cartao_id = cartao.id join contato on contato_id = contato.id
      set data_vencimento =  DATE_ADD(ultima_visita, INTERVAL 90 DAY)
        where plano_id = 735  and removida is not true and data_vencimento is null and (pontuacao_registrada.pontos -  pontos_usados) > 0 ;


    update pontuacao_registrada join acao_contato on acao_contato.horario = pontuacao_registrada.horario and mensagem = 'CORTAR O CABELO' and acao_contato.cartao_id = pontuacao_registrada.cartao_id
      set valor = 45,  pontuacao_registrada_id = pontuacao_registrada.id
          where pontuacao_registrada.empresa_id = 2 and pontuacao_registrada.valor = 0  and acao_contato.horario > '2021-12-01 11:00:00
    ' and pontuacao_registrada.removida is not true ;


    update pontuacao_registrada join acao_contato on acao_contato.horario = pontuacao_registrada.horario and mensagem = 'FAZER A BARBA' and acao_contato.cartao_id = pontuacao_registrada.cartao_id
      set valor = 40,  pontuacao_registrada_id = pontuacao_registrada.id
         where pontuacao_registrada.empresa_id = 2 and pontuacao_registrada.valor = 0  and acao_contato.horario > '2021-12-01' and pontuacao_registrada.removida is not true ;


  </update>


   <update id="updatePontosVencerHoje">
     update pontuacao_registrada join cartao on cartao.id = cartao_id
       set data_vencimento = curdate()
               where plano_id = 729 and data_vencimento is  null and (pontuacao_registrada.pontos - pontos_usados -pontos_vencidos) > 0

   </update>

</mapper>
