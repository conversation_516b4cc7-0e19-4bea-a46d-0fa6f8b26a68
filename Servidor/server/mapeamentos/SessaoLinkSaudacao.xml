<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sessaoLinkSaudacao">
  <resultMap id="sessaoLinkSaudacaoRM" type="SessaoLinkSaudacao">
    <id property="id" column="sessao_link_saudacao_id"/>

    <result property="hash" column="sessao_link_saudacao_hash"/>
    <result property="horario" column="sessao_link_saudacao_horario"/>
    <result property="telefone" column="sessao_link_saudacao_telefone"/>
    <result property="codigoPais" column="sessao_link_saudacao_codigo_pais"/>
    <result property="expirada" column="sessao_link_saudacao_expirada"/>

    <result property="dadosProduto" column="sessao_link_saudacao_dados_produto"/>

    <result property="qtdeAcessos" column="sessao_link_saudacao_qtde_acessos"/>

    <association property="contato" resultMap="contato.contatoRM"/>
    <result property="endereco" column="sessao_link_saudacao_endereco"/>
    <result property="formaDeEntrega" column="sessao_link_saudacao_forma_de_entrega"/>
    <result property="formaDePagamento" column="sessao_link_saudacao_forma_de_pagamento"/>
    <result property="pedidoGuid" column="sessao_link_saudacao_pedido_guid"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="sessaoLinkSaudacaoRM" prefix="true">
    select * from sessao_link_saudacao left join contato on sessao_link_saudacao.contato_id = contato.id
       where
    <if test="idEmpresa != null">
    sessao_link_saudacao.empresa_id = #{idEmpresa}
    </if>
    <if test="idEmpresa == null">
      1 = 1
    </if>
    <if test="telefone != null">
      and sessao_link_saudacao.telefone = #{telefone}
    </if>
    <if test="hash != null">
      and sessao_link_saudacao.hash = #{hash}
      and hash = #{hash}
    </if>

      order by sessao_link_saudacao.id desc limit 1;
  </select>

  <update id="atualize" parameterType="map">
    update sessao_link_saudacao
    set qtde_acessos = #{qtdeAcessos},
    mensagem_enviada_id = #{mensagemEnviada.id},
    endereco = #{endereco},
    forma_de_entrega = #{formaDeEntrega},
    forma_de_pagamento = #{formaDePagamento}
    where id = #{id}
    and empresa_id = #{empresa.id};
  </update>
</mapper>
