<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tipoDeTributacaoPIS">

    <resultMap id="tipoDeTributacaoPISRM" type="TipoDeTributacaoPIS">
      <id property="id" column="tipo_de_tributacao_pis_id"/>
      <result property="codigo" column="tipo_de_tributacao_pis_codigo"/>
      <result property="descricao" column="tipo_de_tributacao_pis_descricao"/>
    </resultMap>

    <select id="selecione" parameterType="map" resultMap="tipoDeTributacaoPISRM" prefix="true">
      select * from tipo_de_tributacao_pis
      where 1 = 1
      <if test="id">
        and id = #{id}
      </if>
      <if test="codigo">
        and codigo = #{codigo}
      </if>
    </select>
</mapper>
