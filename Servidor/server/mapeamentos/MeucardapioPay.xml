<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="meucardapioPay">


    <resultMap id="meucardapioPayRM" type="MeucardapioPay">
        <id property="id" column="meucardapio_pay_id"/>
        <result property="dataAtivacao" column="meucardapio_pay_data_ativacao"/>
        <result property="pixLiberado" column="meucardapio_pay_pix_liberado"/>
        <result property="cartaoCreditoLiberado" column="meucardapio_pay_cartao_credito_liberado"/>
        <result property="contrato" column="meucardapio_pay_contrato_id"/>
        <result property="idLoja" column="meucardapio_pay_id_loja"/>
        <result property="lojaAtivou" column="meucardapio_pay_loja_ativou"/>
        <result property="merchantId" column="meucardapio_pay_merchant_id"/>
        <result property="ativacaoStatus" column="meucardapio_pay_ativacao_status"/>
        <result property="ativacaoErro" column="meucardapio_pay_ativacao_erro"/>
        <result property="pixTaxaMinima" column="meucardapio_pay_pix_taxa_minima"/>

        <association  property="contratoMeucardapioPay"  resultMap="contratoMeucardapioPay.contratoMeucardapioPayRM"/>
    </resultMap>



    <update id="atualize">
        update meucardapio_pay
        set
            data_ativacao  = #{dataAtivacao},
            pix_liberado = #{pixLiberado},
            cartao_credito_liberado = #{cartaoCreditoLiberado},
            ativacao_status  = #{ativacaoStatus},
            loja_ativou  = #{lojaAtivou},
             merchant_id = #{merchantId}

        where id = #{id} and empresa_id = #{empresa.id}
    </update>

      <update id="atualizeFormaPagamentoAtiva">
        update meucardapio_pay
        set

          pix_liberado = #{pixLiberado},
          cartao_credito_liberado = #{cartaoCreditoLiberado}

         where id = #{id} and empresa_id = #{empresa.id}
      </update>

    <update id="atualizeAtivacaoErro">
      update meucardapio_pay
       set  ativacao_erro = #{ativacaoErro}
          where id = #{id} and empresa_id = #{empresa.id}
    </update>


    <update id="atualizeLojaAtivou">
      update meucardapio_pay
       set  loja_ativou = #{lojaAtivou}
           where id = #{id} and empresa_id = #{empresa.id}
    </update>

    <insert id="insiraContratos">
      INSERT INTO meucardapio_pay_contratos
      (meucardapio_pay_id, contrato_meucardapio_pay_id)
      VALUES
      <foreach item="item" collection="dados" open="" separator="," close="">
        (#{item.mercardapioPayId}, #{item.contratoId} )
      </foreach>
    </insert>

</mapper>
