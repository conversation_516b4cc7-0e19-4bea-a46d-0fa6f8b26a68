<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="imagemDoProduto">
  <resultMap id="imagemDoProdutoRM" type="ImagemDoProduto">
    <id property="id" column="imagem_do_produto_id"/>

    <result property="linkImagem" column="imagem_do_produto_link_imagem"/>
    <result property="ordem" column="imagem_do_produto_ordem"/>
  </resultMap>

  <insert id="insiraImagens">
    INSERT INTO imagem_do_produto
    (link_imagem, ordem, produto_id)
    VALUES
    <foreach item="imagem" collection="dados" open="" separator="," close="">
      ( #{imagem.linkImagem} ,   #{imagem.ordem}, #{imagem.produto.id})
    </foreach>
  </insert>



  <delete id="removaTodas" parameterType="map">
    delete from imagem_do_produto
    where   produto_id = #{idProduto}
  </delete>

</mapper>
