<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cidade_entrega">
  <select id="selecione" parameterType="map" resultMap="cidade.cidadeRM"  prefix="true">
      select cidade.*, estado.*
           from cidades_entrega
            inner join cidade on(cidades_entrega.cidade_id = cidade.id)
            inner join estado on(cidade.estado_id = estado.id)
            where
                cidades_entrega.empresa_id =  #{idEmpresa}
              <choose>
              <when test="cidade != null">
                and cidades_entrega.cidade_id = #{cidade.id}
              </when>
              </choose>
            order by cidade.nome desc;
  </select>

  <select id="existe" parameterType="map" resultType="int">
    select count(*) total from contrato  where empresa_id = #{idE   mpresa}
  </select>
  cidade_id bigint(20) not null,
  empresa_forma_de_entrega_id bigint(20) not null,
  empresa_id bigint(20) not null,

  <insert id="insira" parameterType="map" keyProperty="id">
     insert into cidades_entrega(cidade_id, empresa_forma_de_entrega_id, empresa_id)
        values (#{cidade.id}, #{formaDeEntregaEmpresa.id}, #{empresa.id});
  </insert>

  <update id="atualize">
     update contrato
      set plano_id = #{plano.id},  dia_vencimento = #{diaVencimento},
          valor_negociado = #{valorNegociado}, taxa_adesao = #{taxaAdesao},
          limite_contatos_negociado = #{limiteContatosNegociado},  numero_parcelas = #{numeroParcelas}

                where id = #{id};
  </update>

  <update id="remova">
    delete from cidades_entrega
    where empresa_id = #{empresa.id}
        and cidade_id = #{cidade.id};
  </update>
</mapper>
