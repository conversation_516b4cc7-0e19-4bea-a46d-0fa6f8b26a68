<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="listaOpcoesEscolhidas">

  <resultMap id="listaOpcoesEscolhidasRM" type="ListaOpcoesEscolhidas">
    <id property="id" column="lista_opcoes_ecolhidas_id"/>
    <result property="tipoDeCobranca" column="lista_opcoes_escolhidas_tipo_de_cobranca"/>
    <result property="ordem" column="lista_opcoes_escolhidas_ordem"/>
    <collection property="opcoes" resultMap="valorDeOpcaoMultiplaEscolha.valorDeOpcaoMultiplaEscolhaRM"/>
  </resultMap>


  <resultMap id="listaOpcoesEscolhidas0RM" type="ListaOpcoesEscolhidas">
    <id property="id" column="lista_opcoes_ecolhidas_id"/>
    <result property="tipoDeCobranca" column="lista_opcoes_escolhidas_tipo_de_cobranca"/>
    <collection property="opcoes"  resultMap="valorDeOpcaoMultiplaEscolha.valorDeOpcaoMultiplaEscolha0RM"/>
  </resultMap>

  <resultMap id="listaOpcoesEscolhidas1RM" type="ListaOpcoesEscolhidas">
    <id property="id" column="lista_opcoes_ecolhidas_id"/>
    <result property="tipoDeCobranca" column="lista_opcoes_escolhidas_tipo_de_cobranca"/>
    <collection property="opcoes"  resultMap="valorDeOpcaoMultiplaEscolha.valorDeOpcaoMultiplaEscolha1RM"/>
  </resultMap>


  <insert id="insiraOpcoes">
    INSERT INTO valor_de_opcao_multipla_escolha
    (qtde,  opcao_id, lista_id)
    VALUES
    <foreach item="valorDeOpcao" collection="dados" open="" separator="," close="">
      ( #{valorDeOpcao.qtde} ,    #{valorDeOpcao.opcao.id}, #{valorDeOpcao.lista.id} )
    </foreach>
  </insert>
</mapper>
