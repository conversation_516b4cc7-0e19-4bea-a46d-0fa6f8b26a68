<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="nemsagemGeradaIA">
  <resultMap id="MensagemGeradaIaResultMap" type="com.example.MensagemGeradaIa">
    <id column="id" property="id"/>
    <result column="prompt" property="mensagem_gerada_prompt"/>
    <result column="mensagem" property="mensagem_gerada_mensagem"/>
  </resultMap>

  <insert id="insira" parameterType="com.example.MensagemGeradaIa">
    INSERT INTO mensagem_gerada_ia (prompt, mensagem, empresa.id)
    VALUES (#{prompt}, #{mensagem}, #{empresaId})
  </insert>

  <update id="atualize" parameterType="com.example.MensagemGeradaIa">
    UPDATE mensagem_gerada_ia
    SET prompt = #{prompt}, mensagem = #{mensagem}, empresa.id = #{empresaId}
    WHERE id = #{id}
  </update>

  <delete id="remova" parameterType="long">
    DELETE FROM mensagem_gerada_ia
    WHERE id = #{id}
  </delete>

  <select id="selecione" resultMap="MensagemGeradaIaResultMap" parameterType="map" prefix="true">
    SELECT * FROM mensagem_gerada_ia
    where
      empresa_id = #{empresa.id}
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int" prefix="true">
    SELECT COUNT(*) FROM mensagem_gerada_ia WHERE
      empresa_id = #{idEmpresa}
      and data_criacao > #{dataCriacao}
  </select>
</mapper>
