<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="observacoesContribuinte">
  <resultMap id="observacoesContribuinteRM" type="ObservacoesContribuinte">
    <id property="id" column="observacoes_contribuinte_id"/>
    <result property="campo" column="observacoes_contribuinte_campo"/>
    <result property="texto" column="observacoes_contribuinte_texto"/>

    <association property="notaFiscalEletronica" column="nota_fiscal_eletronica_id" resultMap="notaFiscalEletronica.notaFiscalEletronicaRM"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists observacoes_contribuinte (
      id bigint(20) primary key,
      campo varchar(100),
      texto text,
      nota_fiscal_eletronica_id bigint(20),
      foreign key (nota_fiscal_eletronica_id) references nota_fiscal_eletronica (id)
    );
  </create>
</mapper>
