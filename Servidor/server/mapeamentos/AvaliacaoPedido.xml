<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="avaliacaoDePedido">
  <resultMap id="avaliacaoDePedidoRM" type="AvaliacaoPedido">
    <id property="id" column="avaliacao_pedido_id" />

    <result property="nota" column="avaliacao_pedido_nota" />

    <result property="gostouDaEntrega" column="avaliacao_pedido_gostou_da_entrega" />
    <result property="comentario" column="avaliacao_pedido_comentario"/>
    <result property="data" column="avaliacao_pedido_data"/>

    <association property="pedido"   resultMap="pedido.pedidoRM"/>
  </resultMap>

  <resultMap id="resumoAvaliacoesRM" type="DTOGenerico">
    <id property="id" column="resumo_empresa_id" />

    <result property="media" column="resumo_media" />

    <result property="qtdeAvaliacoes" column="resumo_qtde_avaliacoes" />
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="avaliacaoDePedidoRM" prefix="true">
    select *
    from
    avaliacao_pedido
    inner join pedido on(avaliacao_pedido.pedido_id = pedido.id)
    inner join contato on(pedido.contato_id = contato.id)
    inner join empresa on(avaliacao_pedido.empresa_id = empresa.id)
    where avaliacao_pedido.empresa_id = #{idEmpresa}
    <if test="idPedido">
      and pedido_id = #{idPedido}
    </if>
    <if test="guidPedido">
      and pedido.guid = #{guidPedido}
    </if>
    order by avaliacao_pedido.id desc;
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int" prefix="true">
    select count(*)
    from
    avaliacao_pedido
    inner join pedido on(avaliacao_pedido.pedido_id = pedido.id)
    inner join contato on(pedido.contato_id = contato.id)
    inner join empresa on(avaliacao_pedido.empresa_id = empresa.id)
    left join numero_whatsapp on numero_whatsapp.empresa_id = empresa.id and numero_whatsapp.principal is true
    where avaliacao_pedido.empresa_id = #{idEmpresa}
    <if test="idPedido">
      and pedido_id = #{idPedido}
    </if>
  </select>

  <select id="selecioneNotaMedia" parameterType="map" resultMap="resumoAvaliacoesRM">
    select empresa_id resumo_empresa_id, avg(nota) resumo_media, count(*) resumo_qtde_avaliacoes
    from
    avaliacao_pedido
    where avaliacao_pedido.empresa_id = #{idEmpresa}
    <if test="dataInicio">
      and data &lt; #{dataFim} and data &gt;= #{dataInicio}
    </if>;
  </select>

  <update id="atualize"  parameterType="map">
    update avaliacao_pedido
    set gostou_da_entrega = #{gostouDaEntrega},
    nota = #{nota},
    comentario = #{comentario}
    where id = #{id}
    and empresa_id = #{empresa.id};
  </update>
</mapper>
