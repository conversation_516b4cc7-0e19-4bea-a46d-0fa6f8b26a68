<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="grupoDeInsumo">
  <resultMap id="grupoDeInsumoRM" type="GrupoDeInsumo">
    <id property="id" column="grupo_de_insumo_id"/>

    <result property="nome" column="grupo_de_insumo_nome"/>

    <association property="empresa" resultMap="empresa.empresaSimplesRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="grupoDeInsumoRM" prefix="true">
    select * from grupo_de_insumo
      where empresa_id = #{idEmpresa} and removido is not true
          order by grupo_de_insumo.nome
  </select>

  <select id="selecioneTotalUso" parameterType="map" resultType="int">
    select count(*) total
        from  grupo_de_insumo join insumo on grupo_de_insumo.id = grupo_de_insumo_id
                where grupo_de_insumo.empresa_id = #{idEmpresa} and  grupo_de_insumo.id = #{id}
  </select>



  <update id="atualize" parameterType="map">
     update grupo_de_insumo
        set nome =  #{nome}
        where id = #{id} and   empresa_id = #{empresa.id}
  </update>

  <update id="remova" parameterType="map">
     update grupo_de_insumo
        set  removido  = true
        where id = #{id} and   empresa_id = #{empresa.id}
  </update>
</mapper>
