<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="produtoTemplateAdicional">
  <resultMap id="produtoTemplateAdicionalRM" type="ProdutoTemplateAdicional">
      <id property="id" column="produto_template_adicional_id"/>

      <result property="descricao" column="produto_template_adicional_descricao"/>
      <result property="tipo" column="produto_template_adicional_tipo"/>
      <result property="obrigatorio" column="produto_template_adicional_obrigatorio"/>
      <result property="disponivel" column="produto_template_adicional_disponivel"/>

      <result property="disponivel" column="produto_template_adicional_disponivel"/>

      <collection  property="opcoes" resultMap="produtoTemplate.produtoTemplateOpcaoRM"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="produtoTemplateAdicionalRM" prefix="true">
    select *
    from  produto_template_adicional
    left join produto_template_opcao on  produto_template_adicional.id = produto_template_opcao.produto_template_adicional_id and produto_template_opcao.removido is not true
    left join produto_template_tamanho produto_template_tamanho_opcao on produto_template_tamanho_opcao.id = produto_template_opcao.tamanho_id
    <if test="id != null">
      where produto_template_adicional.id = #{id};
    </if>
  </select>

  <update id="atualize">
    update produto_template_adicional
    set dados_json = #{dadosJson}
    where id = #{id}
  </update>
</mapper>
