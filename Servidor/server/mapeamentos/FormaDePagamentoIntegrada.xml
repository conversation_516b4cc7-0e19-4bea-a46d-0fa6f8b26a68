<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="formaIntegrada">
  <resultMap id="formaDePagamentoIntegradaRM" type="FormaDePagamentoIntegrada">
    <id property="id" column="forma_de_pagamento_integrada_nova_id"/>

    <result property="descricao" column="forma_de_pagamento_integrada_nova_descricao" />
    <result property="tipoBandeira" column="forma_de_pagamento_integrada_nova_tipo_bandeira" />
    <result property="codigo" column="forma_de_pagamento_integrada_nova_codigo" />
    <result property="sistema" column="forma_de_pagamento_integrada_nova_sistema" />
  </resultMap>

  <resultMap id="formaDePagamentoIntegradaSalvaRM" type="FormaDePagamentoIntegrada">
    <id property="id" column="forma_de_pagamento_integrada_salva_id"/>

    <result property="descricao" column="forma_de_pagamento_integrada_salva_descricao" />
    <result property="tipoBandeira" column="forma_de_pagamento_integrada_salva_tipo_bandeira" />
    <result property="codigo" column="forma_de_pagamento_integrada_salva_codigo" />
    <result property="sistema" column="forma_de_pagamento_integrada_salva_sistema" />
  </resultMap>

  <resultMap id="formaDePagamentoPdvIntegradaRM" type="FormaDePagamentoIntegrada">
    <id property="id" column="forma_de_pagamento_pdv_integrada_id"/>

    <result property="descricao" column="forma_de_pagamento_pdv_integrada_descricao" />
    <result property="tipoBandeira" column="forma_de_pagamento_pdv_integrada_tipo_bandeira" />
    <result property="codigo" column="forma_de_pagamento_pdv_integrada_codigo" />
    <result property="sistema" column="forma_de_pagamento_pdv_integrada_sistema" />
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="formaDePagamentoIntegradaRM" prefix="true">
    select * from forma_de_pagamento_integrada_nova
    <if test="sistema">
      where sistema = #{sistema}
    </if>
        order by descricao
  </select>

</mapper>
