<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="enderecoNFe">
  <resultMap id="enderecoNFeRM" type="EnderecoNFe">
    <id property="id" column="endereco_nfe_id" />

    <result property="telefone" column="endereco_nfe_telefone" />
    <result property="logradouro" column="endereco_nfe_logradouro" />
    <result property="numero" column="endereco_nfe_numero" />
    <result property="complemento" column="endereco_nfe_complemento" />
    <result property="bairro" column="endereco_nfe_bairro" />
    <result property="cep" column="endereco_nfe_cep" />
    <result property="codMunicipio" column="endereco_nfe_cod_municipio" />
    <result property="nomeMunicipio" column="endereco_nfe_nome_municipio" />
    <result property="siglaUF" column="endereco_nfe_sigla_uf" />
    <result property="codigoPais" column="endereco_nfe_codigo_pais" />
    <result property="nomePais" column="endereco_nfe_nome_pais" />
  </resultMap>

  <insert id="insira" parameterType="map">
    insert into endereco_nfe (
      id,
      telefone,
      logradouro,
      numero,
      complemento,
      bairro,
      cep,
      cod_municipio,
      nome_municipio,
      sigla_uf,
      codigo_pais,
      nome_pais,
      empresa_id
    ) values (
      #{id},
      #{telefone},
      #{logradouro},
      #{numero},
      #{complemento},
      #{bairro},
      #{cep},
      #{codMunicipio},
      #{nomeMunicipio},
      #{siglaUF},
      #{codigoPais},
      #{nomePais},
      #{empresa.id}

    );
  </insert>

  <create id="crieTabela" parameterType="map">
    create table if not exists endereco_nfe (
      id bigint(20) primary key,
      telefone varchar(20),
      logradouro varchar(100),
      numero varchar(10),
      complemento varchar(50),
      bairro varchar(50),
      cep varchar(10),
      cod_municipio varchar(10),
      nome_municipio varchar(100),
      sigla_uf varchar(2),
      codigo_pais varchar(10),
      nome_pais varchar(100)
    );
  </create>

</mapper>
