<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="catalogoDaRede">

  <resultMap id="catalogoDaRedeRM" type="CatalogoDaRede">
    <id property="id" column="catalogo_da_rede_id"/>

    <result property="rede" column="catalogo_da_rede_rede"/>

    <association property="catalogo"   resultMap="catalogo.catalogoRM"/>
  </resultMap>

  <insert id="insira" parameterType="map">
    insert into catalogo_da_rede (rede, catalogo_id)
    values (#{rede}, #{catalogo.id});
  </insert>

  <select id="selecione" parameterType="map" resultMap="catalogoDaRedeRM"  prefix="true">
    select * from catalogo_da_rede
    left join catalogo
    on catalogo.id = catalogo_da_rede.catalogo_id
    where removido is not true
    <choose>
      <when test="id">
        and catalogo_da_rede.id = #{id}
      </when>
      <when test="rede">
        and rede = #{rede}
      </when>

      <when test="idCatalogo">
        and catalogo_da_rede.catalogo_id = #{idCatalogo}
      </when>

    </choose>

  </select>

  <update id="remova">
    update catalogo_da_rede set removido = true
    where id = #{id}
  </update>


  <update id="sincronizar produtos/adicionais catalogos pais">

  </update>
  <update id="sincronizar produtos/adicionais catalogos filhos">
    <!-- produtos 1101 e 35821 -->
    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1101 e 35821  -->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 16.50  where (modelo_catalogo_da_rede_id in( 6,12) or pap.catalogo_id in(1465,1471)) and opa.codigo_pdv in ('1101','35821') and valor > 10 and opa.excluido is not true;


    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 1101 e 35821  -->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 16.00  where  ( modelo_catalogo_da_rede_id in( 7,8,13,14) or pap.catalogo_id in(1466,1467,1472,1473)) and opa.codigo_pdv in ('1101','35821') and valor > 10 and opa.excluido is not true;


    <!-- atualiza na tabela 5,6,7 gcom/ecletica os rolinhos 1101 e 35821  -->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 15  where (modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17) or pap.catalogo_id in(1468,1469,1470,1474,1475,1476) )and opa.codigo_pdv in ('1101','35821') and valor > 10 and opa.excluido is not true;


    <!-- produtos 11109 e 35823 -->
    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1109 e 35823  -->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 11  where  (modelo_catalogo_da_rede_id in( 6,12) or pap.catalogo_id in(1465,1471)) and opa.codigo_pdv in ('1109','35823') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 1109 e 35823  -->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 10.50  where  ( modelo_catalogo_da_rede_id in( 7,8,13,14) or pap.catalogo_id in(1466,1467,1472,1473)) and opa.codigo_pdv in ('1109','35823') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela  5,6,7 gcom/ecletica os rolinhos 1109 e 35823  -->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 9.50  where  (modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17) or pap.catalogo_id in(1468,1469,1470,1474,1475,1476)) and opa.codigo_pdv in ('1109','35823') and valor > 0 and opa.excluido is not true;



    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 1105,35822  -->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 18.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14) ) and opa.codigo_pdv in ('1105','35822') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 5,6,7  gcom/ecletica os rolinhos 1105,35822 para 19.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 17.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in(  9,10,11, 15,16,17) ) and opa.codigo_pdv in ('1105','35822') and valor > 10 and opa.excluido is not true;


    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 1110,35824  -->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 12  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14) ) and opa.codigo_pdv in ('1110','35824') and valor > 0 and opa.excluido is not true;


    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 1132,35827 para 19.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 26.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14) ) and opa.codigo_pdv in ('1132','35827') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 1110,35824 para 12.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id left  join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 15.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14) ) and opa.codigo_pdv in ('1136','35828') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela  2,3 gcom/ecletica os rolinhos 1125,35825 para 17.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 16.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14) ) and opa.codigo_pdv in ('1125','35825') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela  2,3 gcom/ecletica os rolinhos 1126,35826 para 12.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left  join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 11.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14)) and opa.codigo_pdv in ('1126','35826') and valor > 0 and opa.excluido is not true;


    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 6614,35912 para 16.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 15.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14) ) and opa.codigo_pdv in ('6614','35912') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 6617,35914 para 11.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left  join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 10.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14)) and opa.codigo_pdv in ('6617','35914') and valor > 0 and opa.excluido is not true;


    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 1123,35996 para 16.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 15.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14)) and opa.codigo_pdv in ('1123','35996') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 1124,35997 para 10.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 10  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14) ) and opa.codigo_pdv in ('1124','35997') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 6607,35910 para 16.90-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 16.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14)) and opa.codigo_pdv in ('6607','35910') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 2,3 gcom/ecletica os rolinhos 6616,35913 para 11.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 10.90  where (pap.catalogo_id in(1466,1467,1472,1473) or modelo_catalogo_da_rede_id in( 7,8,13,14) ) and opa.codigo_pdv in ('6616','35913') and valor > 0 and opa.excluido is not true;





    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1105,35822 para 19.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 19.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('1105','35822') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1110,35824 para 12.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 12.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('1110','35824') and valor > 0 and opa.excluido is not true;


    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1132,35827 para 19.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 27.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('1132','35827') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1110,35824 para 12.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 16.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('1136','35828') and valor > 0 and opa.excluido is not true;


    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 6614,35912 para 16.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 16.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('6614','35912') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 6617,35914 para 11.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 11.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('6617','35914') and valor > 0 and opa.excluido is not true;


    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1125,35825 para 17.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left  join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 17.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('1125','35825') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1126,35826 para 12.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 12.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('1126','35826') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 6607,35910 para 17.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id left  join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 17.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('6607','35910') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 6616,35913 para 11.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id   left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 11.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('6616','35913') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1123,35996 para 16.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 16.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('1123','35996') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1124,35997 para 10.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 10.50  where (pap.catalogo_id in(1465,1471) or modelo_catalogo_da_rede_id in( 6,12) ) and opa.codigo_pdv in ('1124','35997') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela 5,6,7  gcom/ecletica os rolinhos 1110,35824 para 12.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 11  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in(9,10,11, 15,16,17) ) and opa.codigo_pdv in ('1110','35824') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela  tabela 5,6,7 gcom/ecletica os rolinhos 1132,35827 para 19.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 25.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in(9,10,11, 15,16,17) ) and opa.codigo_pdv in ('1132','35827') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela  tabela 5,6,7 gcom/ecletica os rolinhos 1110,35824 para 12.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 14.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in(9,10,11, 15,16,17) ) and opa.codigo_pdv in ('1136','35828') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela  tabela 5,6,7 gcom/ecletica os rolinhos 6614,35912 para 16.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 14.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17) ) and opa.codigo_pdv in ('6614','35912') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela  tabela 5,6,7 gcom/ecletica os rolinhos 6617,35914 para 11.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 9.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17) ) and opa.codigo_pdv in ('6617','35914') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela  5,6,7 gcom/ecletica os rolinhos 1125,35825 para 17.50-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 15.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17) ) and opa.codigo_pdv in ('1125','35825') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela  5,6,7 gcom/ecletica os rolinhos 1126,35826 para 12.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 10.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17) ) and opa.codigo_pdv in ('1126','35826') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela  5,6,7 gcom/ecletica os rolinhos 6607,35910 para 15.90-->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 15.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17)) and opa.codigo_pdv in ('6607','35910') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela  5,6,7 gcom/ecletica os rolinhos 6616,35913 para 11.50-->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id left  join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 9.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17) ) and opa.codigo_pdv in ('6616','35913') and valor > 0 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1123,35996 para  14.90 -->
    update produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id  left join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 14.90  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17)) and opa.codigo_pdv in ('1123','35996') and valor > 10 and opa.excluido is not true;

    <!-- atualiza na tabela 1 gcom/ecletica os rolinhos 1124,35997 para 9 -->
    update  produto_adicional_produto pap join opcao_adicional_produto opa on opa.adicional_produto_id = pap.adicional_produto_id left  join empresa on empresa.catalogo_id = pap.catalogo_id
    set valor = 9  where (pap.catalogo_id in(1468,1469,1470) or modelo_catalogo_da_rede_id in( 9,10,11, 15,16,17) ) and opa.codigo_pdv in ('1124','35997') and valor > 0 and opa.excluido is not true;


  </update>
</mapper>
