<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="formaDePagamentoPdv">
  <resultMap id="formaDePagamentoPdvRM" type="FormaDePagamentoPdv">
    <id property="id" column="forma_de_pagamento_pdv_id"/>

    <result property="nome" column="forma_de_pagamento_pdv_nome"/>
    <result property="tipo" column="forma_de_pagamento_pdv_tipo"/>
    <result property="metodo" column="forma_de_pagamento_pdv_metodo"/>
    <result property="opendeliveryMethod" column="forma_de_pagamento_pdv_opendelivery_method"/>
    <result property="opendeliveryMethodInfo" column="forma_de_pagamento_pdv_opendelivery_method_info"/>

    <collection property="formasIntegradas" resultMap="formaIntegrada.formaDePagamentoPdvIntegradaRM"/>
    <collection property="bandeiras" resultMap="formaDePagamentoPdvBandeira.formaDePagamentoPdvBandeiraRM"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="formaDePagamentoPdvRM" prefix="true">
    select *
      from  forma_de_pagamento_pdv left join forma_de_pagamento_pdv_bandeira on  forma_de_pagamento_pdv.id = forma_de_pagamento_pdv_id
                                   left join bandeira on bandeira.id = bandeira_id
                left join forma_de_pagamento_pdv_forma_integrada
                      on forma_de_pagamento_pdv_forma_integrada.forma_de_pagamento_pdv_id = forma_de_pagamento_pdv.id
                left join forma_de_pagamento_integrada_nova forma_de_pagamento_pdv_integrada
                      on forma_de_pagamento_pdv_integrada.id = forma_de_pagamento_integrada_id

      where forma_de_pagamento_pdv.removido is not true

      <if test="id"> and forma_de_pagamento_pdv.id = #{id}</if>
      <if test="tipo"> and forma_de_pagamento_pdv.tipo = #{tipo}</if>
      <if test="nome"> and forma_de_pagamento_pdv.nome = #{nome}</if>
      <if test="padrao"> and forma_de_pagamento_pdv.tipo = 'FIDELIDADE' OR forma_de_pagamento_pdv.metodo = 'DINHEIRO' </if>
      <if test="ativarLoja"> and forma_de_pagamento_pdv.tipo = 'MANUAL' OR forma_de_pagamento_pdv.tipo = 'OFFLINE' </if>

        order by forma_de_pagamento_pdv.tipo, forma_de_pagamento_pdv.metodo

  </select>

  <update id="atualize">
    update forma_de_pagamento_pdv
    set nome = #{nome}, tipo = #{tipo}, metodo = #{metodo},
        opendelivery_method = #{opendeliveryMethod}, opendelivery_method_info = #{opendeliveryMethodInfo}
    where id = #{id}
  </update>

  <update id="removaTodasIntegradas">
    delete from forma_de_pagamento_pdv_forma_integrada where  forma_de_pagamento_pdv_id = #{formaPdvId}
  </update>

  <insert id="insiraIntegradas">
    INSERT INTO forma_de_pagamento_pdv_forma_integrada
    (forma_de_pagamento_pdv_id, forma_de_pagamento_integrada_id)
    VALUES
    <foreach item="item" collection="dados" open="" separator="," close="">
      ( #{item.formaPdvId} ,   #{item.formaIntegradaId}  )
    </foreach>
  </insert>



</mapper>
