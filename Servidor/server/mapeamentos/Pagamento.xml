<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pagamento">
  <resultMap id="pagamentoRM" type="faturamento.Pagamento">
    <id property="id" column="pagamento_id" />
    <result property="codigo" column="pagamento_codigo" />
    <result property="horario" column="pagamento_horario" />
    <result property="dataPagamento" column="pagamento_data_pagamento" />
    <result property="ultimaAtualizacao" column="pagamento_ultima_atualizacao" />
    <result property="tipo" column="pagamento_tipo" />
    <result property="status" column="pagamento_status" />
    <result property="valor" column="pagamento_valor" />

    <result property="url" column="pagamento_url" />
    <result property="dataVencimento" column="pagamento_data_vencimento" />
    <result property="numeroTransacao" column="pagamento_numero_transacao" />
    <result property="numeroParcelas" column="pagamento_numero_parcelas" />
    <result property="valorParcela" column="pagamento_valor_parcela" />
    <result property="totalParcelado" column="pagamento_total_parcelado" />
    <result property="codigoDeBarras" column="pagamento_codigo_de_barras" />

    <association property="fatura"   resultMap="fatura.faturaPagamentoRM"/>

  </resultMap>

  <resultMap id="pagamentoDaFaturaRM" type="faturamento.Pagamento">
    <id property="id" column="pagamento_id" />
    <result property="codigo" column="pagamento_codigo" />
    <result property="horario" column="pagamento_horario" />
    <result property="dataPagamento" column="pagamento_data_pagamento" />
    <result property="ultimaAtualizacao" column="pagamento_ultima_atualizacao" />
    <result property="tipo" column="pagamento_tipo" />
    <result property="status" column="pagamento_status" />
    <result property="valor" column="pagamento_valor" />

    <result property="url" column="pagamento_url" />
    <result property="dataVencimento" column="pagamento_data_vencimento" />
    <result property="numeroTransacao" column="pagamento_numero_transacao" />
    <result property="numeroParcelas" column="pagamento_numero_parcelas" />
    <result property="valorParcela" column="pagamento_valor_parcela" />
    <result property="totalParcelado" column="pagamento_total_parcelado" />
    <result property="codigoDeBarras" column="pagamento_codigo_de_barras" />

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="pagamentoRM" prefix="true">
       select * from pagamento join fatura on fatura.id = pagamento.fatura_id
           where
                 <choose>
                     <when test="id">
                         pagamento.id = #{id}
                     </when>
                     <when test="codigo">
                       pagamento.codigo = #{codigo}
                     </when>
                 </choose>
  </select>

  <update id="atualizeStatus">
     update pagamento set status = #{status} where id = #{id}
  </update>
</mapper>
