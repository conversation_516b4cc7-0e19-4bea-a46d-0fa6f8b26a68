<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="configuracoes_mia">
  <resultMap id="configuraoesMiaRM" type="ConfiguracoesMia">
    <id property="id" column="configuracoes_mia_id"/>

    <result property="nome" column="configuracoes_mia_nome"/>

    <result property="status" column="configuracoes_mia_status"/>
    <result property="tempoPausaMia" column="configuracoes_mia_tempo_pausa_mia"/>

    <result property="tempoPausaMia" column="configuracoes_mia_tempo_pausa_mia"/>

    <result property="recuperarCarrinho" column="configuracoes_mia_recuperar_carrinho"/>
    <result property="tempoRecuperarCarrinho" column="configuracoes_mia_tempo_recuperar_carrinho"/>

    <result property="modelo" column="configuracoes_mia_modelo"/>

    <result property="comportamentoForaDoEscopo" column="configuracoes_mia_comportamento_fora_do_escopo"/>

    <result property="responderSobreProdutos" column="configuracoes_mia_responder_sobre_produtos"/>

    <result property="telefonesTeste" column="configuracoes_mia_telefones_teste"/>

    <result property="usarFluxoTypebot" column="configuracoes_mia_usar_fluxo_typebot"/>

    <result property="idFluxoTypebotWhatsapp" column="configuracoes_mia_id_fluxo_typebot_whatsapp"/>
    <result property="idFluxoTypebotInstagram" column="configuracoes_mia_id_fluxo_typebot_instagram"/>

    <result property="typebotConfigurado" column="configuracoes_mia_typebot_configurado"/>
    <result property="workspaceId" column="configuracoes_mia_workspace_id"/>

    <result property="chaveApiTypebot" column="configuracoes_mia_chave_api_typebot"/>

    <result property="publicIdFluxoWhatsapp" column="configuracoes_mia_public_id_fluxo_whatsapp"/>
    <result property="publicIdFluxoInstagram" column="configuracoes_mia_public_id_fluxo_instagram"/>
    
    <result property="dataInicioTrial" column="configuracoes_mia_data_inicio_trial"/>
    <result property="dataFimTrial" column="configuracoes_mia_data_fim_trial"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="configuraoesMiaRM" prefix="true">
    SELECT * FROM configuracoes_mia
    WHERE
    empresa_id = #{idEmpresa}
    <if test="id != null">
      AND id = #{id}
    </if>
    <if test="nome != null">
      AND nome = #{nome}
    </if>
  </select>

  <update id="atualize">
    UPDATE configuracoes_mia SET
    nome = #{nome},
    status = #{status},
    modelo = #{modelo},
    comportamento_fora_do_escopo = #{comportamentoForaDoEscopo},
    tempo_pausa_mia = #{tempoPausaMia},
    recuperar_carrinho = #{recuperarCarrinho},
    tempo_recuperar_carrinho = #{tempoRecuperarCarrinho},
    responder_sobre_produtos = #{responderSobreProdutos},
    telefones_teste = #{telefonesTeste},
    usar_fluxo_typebot = #{usarFluxoTypebot},
    id_fluxo_typebot_whatsapp = #{idFluxoTypebotWhatsapp},
    id_fluxo_typebot_instagram = #{idFluxoTypebotInstagram},
    chave_api_typebot = #{chaveApiTypebot},
    workspace_id = #{workspaceId},
    typebot_configurado = #{typebotConfigurado},
    public_id_fluxo_whatsapp = #{publicIdFluxoWhatsapp},
    public_id_fluxo_instagram = #{publicIdFluxoInstagram},
    data_inicio_trial = #{dataInicioTrial},
    data_fim_trial = #{dataFimTrial}
    WHERE id = #{id}
    and empresa_id = #{empresa.id};
  </update>
</mapper>
