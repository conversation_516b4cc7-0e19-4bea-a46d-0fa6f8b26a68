<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sessaoMesa">
  <resultMap id="sessaoMesaRM" type="SessaoMesa">
    <id property="id" column="sessao_mesa_id"/>

    <result property="hash" column="sessao_mesa_hash"/>
    <result property="horario" column="sessao_mesa_horario"/>
    <result property="expirada" column="sessao_mesa_expirada"/>

    <association property="mesa" resultMap="mesa.mesaRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="sessaoMesaRM" prefix="true">
    select sessao_mesa.*,
            mesa.*
    from
    sessao_mesa join mesa on sessao_mesa.mesa_id = mesa.id
    where  hash = #{hash}
  </select>

  <update id="marqueComoExpirada"  parameterType="map">
    update sessao_mesa
    set expirada = true
    where id = #{id}
  </update>
</mapper>
