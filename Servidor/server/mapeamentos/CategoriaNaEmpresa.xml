<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="categoriaNaEmpresa">

  <resultMap id="categoriaNaEmpresaRM" type="CategoriaNaEmpresa">
    <id property="id" column="categoria_na_empresa_id"/>

    <result property="disponivel" column="categoria_na_empresa_disponivel"/>

    <association property="categoria"   resultMap="categoria.categoriaRM"/>
    <association property="empresa"   resultMap="empresa.empresaRM"/>
  </resultMap>

  <resultMap id="categoriaNaEmpresaDaCategoriaRM" type="CategoriaNaEmpresa">
    <id property="id" column="categoria_na_empresa_id"/>

    <result property="disponivel" column="categoria_na_empresa_disponivel"/>

  </resultMap>



  <insert id="insira" parameterType="map" useGeneratedKeys="true" >

    insert into categoria_na_empresa(categoria_id, empresa_id, disponivel)
    values (#{categoria.id}, #{empresa.id}, #{disponivel})
    ON DUPLICATE KEY     UPDATE disponivel = #{disponivel}
  </insert>

  <update id="atualizeDisponibilidade" parameterType="map">
    update categoria_na_empresa
    set disponivel = #{disponivel}
    where id = #{id}
    and empresa_id = #{empresa.id};
  </update>

</mapper>
