<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="notaFiscalEletronica">
  <resultMap id="notaFiscalEletronicaRM" type="NotaFiscalEletronica">
    <id property="id" column="nota_fiscal_eletronica_id"/>

    <result property="ambiente" column="nota_fiscal_eletronica_ambiente"/>
    <result property="chaveDeAcesso" column="nota_fiscal_eletronica_chave_da_nota"/>
    <result property="ufEmitente" column="nota_fiscal_eletronica_uf_emitente"/>
    <result property="naturezaOperacao" column="nota_fiscal_eletronica_natureza_operacao"/>
    <result property="indFormaDePagamento" column="nota_fiscal_eletronica_ind_forma_de_pagamento"/>
    <result property="modeloDocumentoFiscal" column="nota_fiscal_eletronica_modelo_documento_fiscal"/>
    <result property="serie" column="nota_fiscal_eletronica_serie"/>
    <result property="numeroNFe" column="nota_fiscal_eletronica_numero_nfe"/>
    <result property="dataDeEmissao" column="nota_fiscal_eletronica_data_de_emissao"/>
    <result property="dataDeSaida" column="nota_fiscal_eletronica_data_de_saida"/>
    <result property="tipoDoDocumento" column="nota_fiscal_eletronica_tipo_do_documento"/>
    <result property="tipoDestinoOperacao" column="nota_fiscal_eletronica_tipo_destino_operacao"/>
    <result property="municipioFatorGerador" column="nota_fiscal_eletronica_municipio_fator_gerador"/>
    <result property="cnpjEmitente" column="nota_fiscal_eletronica_cnpj_emitente"/>
    <result property="cpfEmitente" column="nota_fiscal_eletronica_cpf_emitente"/>
    <result property="nomeEmitente" column="nota_fiscal_eletronica_nome_emitente"/>
    <result property="nomeFantasiaEmitente" column="nota_fiscal_eletronica_nome_fantasia_emitente"/>
    <result property="ieEmitente" column="nota_fiscal_eletronica_ie_emitente"/>
    <result property="ieStEmitente" column="nota_fiscal_eletronica_ie_st_emitente"/>
    <result property="inscricaoMunicipal" column="nota_fiscal_eletronica_inscricao_municipal"/>
    <result property="cnaeEmitente" column="nota_fiscal_eletronica_cnae_emitente"/>
    <result property="cnpjDestinatario" column="nota_fiscal_eletronica_cnpj_destinatario"/>
    <result property="cpfDestinatario" column="nota_fiscal_eletronica_cpf_destinatario"/>
    <result property="idEstrangeiroDestinatario" column="nota_fiscal_eletronica_id_estrangeiro_destinatario"/>
    <result property="nomeDestinatario" column="nota_fiscal_eletronica_nome_destinatario"/>
    <result property="indIeDestinatario" column="nota_fiscal_eletronica_ind_ie_destinatario"/>
    <result property="ieDestinatario" column="nota_fiscal_eletronica_ie_destinatario"/>
    <result property="imDestinataio" column="nota_fiscal_eletronica_im_destinataio"/>
    <result property="inssSuframaDestinatario" column="nota_fiscal_eletronica_inss_suframa_destinatario"/>
    <result property="nomeExpedidor" column="nota_fiscal_eletronica_nome_expedidor"/>
    <result property="emailExpedidor" column="nota_fiscal_eletronica_email_expedidor"/>
    <result property="ieExpedidor" column="nota_fiscal_eletronica_ie_expedidor"/>
    <result property="cnpjExpedidor" column="nota_fiscal_eletronica_cnpj_expedidor"/>
    <result property="cpfExpedidor" column="nota_fiscal_eletronica_cpf_expedidor"/>
    <result property="cnpjEntrega" column="nota_fiscal_eletronica_cnpj_entrega"/>
    <result property="cpfEntrega" column="nota_fiscal_eletronica_cpf_entrega"/>
    <result property="nomeRecebedor" column="nota_fiscal_eletronica_nome_recebedor"/>
    <result property="emailRecebedor" column="nota_fiscal_eletronica_email_recebedor"/>
    <result property="ieRecebedor" column="nota_fiscal_eletronica_ie_recebedor"/>
    <result property="valorBaseDeCalculoICMS" column="nota_fiscal_eletronica_valor_base_de_calculo_icms"/>
    <result property="valorICMS" column="nota_fiscal_eletronica_valor_icms"/>
    <result property="valorICMSDesonerado" column="nota_fiscal_eletronica_valor_icms_desonerado"/>
    <result property="valorBaseDeCalculoICMSSt" column="nota_fiscal_eletronica_valor_base_de_calculo_icms_st"/>
    <result property="valorICMSSt" column="nota_fiscal_eletronica_valor_icms_st"/>
    <result property="valorTotalProdutos" column="nota_fiscal_eletronica_valor_total_produtos"/>
    <result property="valorDoFrete" column="nota_fiscal_eletronica_valor_do_frete"/>
    <result property="valorDoSeguro" column="nota_fiscal_eletronica_valor_do_seguro"/>
    <result property="valorDoDesconto" column="nota_fiscal_eletronica_valor_do_desconto"/>
    <result property="valorII" column="nota_fiscal_eletronica_valor_ii"/>
    <result property="valorIPI" column="nota_fiscal_eletronica_valor_ipi"/>
    <result property="valorPis" column="nota_fiscal_eletronica_valor_pis"/>
    <result property="valorCofins" column="nota_fiscal_eletronica_valor_cofins"/>
    <result property="valorOutrasDespesas" column="nota_fiscal_eletronica_valor_outras_despesas"/>
    <result property="valorTotalNFe" column="nota_fiscal_eletronica_valor_total_nfe"/>
    <result property="valorTotalServicos" column="nota_fiscal_eletronica_valor_total_servicos"/>
    <result property="baseDeCalculoISS" column="nota_fiscal_eletronica_base_de_calculo_iss"/>
    <result property="valorISS" column="nota_fiscal_eletronica_valor_iss"/>
    <result property="valorServicosPis" column="nota_fiscal_eletronica_valor_servicos_pis"/>
    <result property="valorServicosCofins" column="nota_fiscal_eletronica_valor_servicos_cofins"/>
    <result property="dataPrestacaoServico" column="nota_fiscal_eletronica_data_prestacao_servico"/>
    <result property="valorDeducao" column="nota_fiscal_eletronica_valor_deducao"/>
    <result property="valorOutrasRetencoes" column="nota_fiscal_eletronica_valor_outras_retencoes"/>
    <result property="valorDescontoIncondicionado" column="nota_fiscal_eletronica_valor_desconto_incondicionado"/>
    <result property="valorDescontoCondicionado" column="nota_fiscal_eletronica_valor_desconto_condicionado"/>
    <result property="valorISSRetido" column="nota_fiscal_eletronica_valor_iss_retido"/>
    <result property="valorFCPUFDest" column="nota_fiscal_eletronica_valor_fcp_uf_dest"/>
    <result property="valorICMSUFDest" column="nota_fiscal_eletronica_valor_icms_uf_dest"/>
    <result property="valorICMSUFRemet" column="nota_fiscal_eletronica_valor_icms_uf_remet"/>
    <result property="codigoRegimeEspecialTributacao" column="nota_fiscal_eletronica_codigo_regime_especial_tributacao"/>
    <result property="valorRetidoPis" column="nota_fiscal_eletronica_valor_retido_pis"/>
    <result property="valorRetidoCofins" column="nota_fiscal_eletronica_valor_retido_cofins"/>
    <result property="valorRetidoCsll" column="nota_fiscal_eletronica_valor_retido_csll"/>
    <result property="baseDeCalculoIRRF" column="nota_fiscal_eletronica_base_de_calculo_irrf"/>
    <result property="valorIRRF" column="nota_fiscal_eletronica_valor_irrf"/>
    <result property="nfeBasecalcretprev" column="nota_fiscal_eletronica_nfe_basecalcretprev"/>
    <result property="nfeValretidoprevsocial" column="nota_fiscal_eletronica_nfe_valretidoprevsocial"/>
    <result property="modoFrete" column="nota_fiscal_eletronica_modo_frete"/>
    <result property="informacoesAdicionaisContrib" column="nota_fiscal_eletronica_informacoes_adicionais_contrib"/>
    <result property="infAdicionaisFisco" column="nota_fiscal_eletronica_inf_adicionais_fisco"/>
    <result property="horaSaida" column="nota_fiscal_eletronica_hora_saida"/>
    <result property="tipoImpressaoDanfe" column="nota_fiscal_eletronica_tipo_impressao_danfe"/>
    <result property="tipoEmissao" column="nota_fiscal_eletronica_tipo_emissao"/>
    <result property="finalidade" column="nota_fiscal_eletronica_finalidade"/>
    <result property="consumidorFinal" column="nota_fiscal_eletronica_consumidor_final"/>
    <result property="compradorPresente" column="nota_fiscal_eletronica_comprador_presente"/>
    <result property="status" column="nota_fiscal_eletronica_status"/>
    <!--<result property="operacao" column="nota_fiscal_eletronica_operacao"/>-->
    <result property="motivo" column="nota_fiscal_eletronica_motivo"/>
    <result property="documentoXmlGerado" column="nota_fiscal_eletronica_documento_xml_gerado"/>
    <result property="ultimaAlteracao" column="nota_fiscal_eletronica_ultima_alteracao"/>
    <result property="dataCriacao" column="nota_fiscal_eletronica_data_criacao"/>
    <result property="numeroProtocolo" column="nota_fiscal_eletronica_numero_protocolo"/>
    <result property="dataContingencia" column="nota_fiscal_eletronica_data_contingencia"/>
    <result property="justContingencia" column="nota_fiscal_eletronica_just_contingencia"/>
    <result property="codigoRegimeTributario" column="nota_fiscal_eletronica_codigo_regime_tributario"/>
    <result property="isufDestinatario" column="nota_fiscal_eletronica_isuf_destinatario"/>
    <result property="email" column="nota_fiscal_eletronica_email"/>
    <result property="ufEmbarque" column="nota_fiscal_eletronica_uf_embarque"/>
    <result property="localEmbarque" column="nota_fiscal_eletronica_local_embarque"/>
    <result property="localDespacho" column="nota_fiscal_eletronica_local_despacho"/>
    <result property="valorTotalTributacao" column="nota_fiscal_eletronica_valor_total_tributacao"/>
    <result property="modECF" column="nota_fiscal_eletronica_mod_ecf"/>
    <result property="numeroECF" column="nota_fiscal_eletronica_numero_ecf"/>
    <result property="numeroCOO" column="nota_fiscal_eletronica_numero_coo"/>
    <result property="valorFCP" column="nota_fiscal_eletronica_valor_fcp"/>
    <result property="valorFCPST" column="nota_fiscal_eletronica_valor_fcp_st"/>
    <result property="valorFCPRet" column="nota_fiscal_eletronica_valor_fcp_ret"/>
    <result property="valorIPIdevolvido" column="nota_fiscal_eletronica_valor_ipi_devolvido"/>
    <result property="valorTroco" column="nota_fiscal_eletronica_valor_troco"/>
    <result property="indicadorIntermediador" column="nota_fiscal_eletronica_indicador_intermediador"/>
    <result property="cnpjIntermediador" column="nota_fiscal_eletronica_cnpj_intermediador"/>
    <result property="identificarNoIntermediador" column="nota_fiscal_eletronica_identificar_no_intermediador"/>
    <result property="chaveNotaReferenciada" column="nota_fiscal_eletronica_chave_nota_referenciada"/>\
    <result property="dataHoraSaida" column="nota_fiscal_eletronica_data_hora_saida"/>
    <result property="totalTributadoMono" column="nota_fiscal_eletronica_total_tributado_mono"/>
    <result property="valorICMSMono" column="nota_fiscal_eletronica_valor_icms_mono"/>
    <result property="totalTributadoMonoRetencao" column="nota_fiscal_eletronica_total_tributado_mono_retencao"/>
    <result property="valorICMSMonoRetencao" column="nota_fiscal_eletronica_valor_icms_mono_retencao"/>
    <result property="totalTributadoMonoRetido" column="nota_fiscal_eletronica_total_tributado_mono_retido"/>
    <result property="valorICMSMonoRetido" column="nota_fiscal_eletronica_valor_icms_mono_retido"/>
    <result property="autorizacaoDeUso" column="nota_fiscal_eletronica_autorizacao_de_uso"/>
    <result property="impressa" column="nota_fiscal_eletronica_impressa"/>
    <result property="qrCode" column="nota_fiscal_eletronica_qr_code"/>
    <result property="urlChave" column="nota_fiscal_eletronica_url_chave" />
    <result property="dataAutorizacao" column="nota_fiscal_eletronica_data_autorizacao"/>

    <collection property="observacoesContribuinte" resultMap="observacoesContribuinte.observacoesContribuinteRM"/>
    <collection property="historicoEstados" resultMap="estadoNotaFiscalEletronica.estadoNotaFiscalEletronicaRM"/>
    <collection property="itens" resultMap="itemNFe.itemNFeRM"/>
    <collection property="autorizacoesDownload" resultMap="autorizacaoDownload.autorizacaoDownloadRM"/>
    <collection property="pagamentos" resultMap="pagamentoNfe.pagamentoNfeRM"/>

    <association property="empresa" column="empresa_id" resultMap="empresa.empresaRM"/>
    <association property="enderecoEmitente" columnPrefix="emitente_" column="endereco_emitente_id" resultMap="enderecoNFe.enderecoNFeRM"/>
    <association property="enderecoDestinatario" columnPrefix="destinatario_" column="endereco_destinatario_id" resultMap="enderecoNFe.enderecoNFeRM"/>
    <association property="localRetirada" columnPrefix="retirada_" column="endereco_retirada_id" resultMap="enderecoNFe.enderecoNFeRM"/>
    <association property="localEntrega" columnPrefix="entrega_" column="endereco_entrega_id" resultMap="enderecoNFe.enderecoNFeRM"/>
    <association property="transporte" column="transporte_id" resultMap="transporte.transporteRM"/>
    <association property="cobranca" column="cobranca_id" resultMap="cobranca.cobrancaRM"/>


  </resultMap>
<!-- retorna as nfes completas, inclusive itens e pagamentos -->
  <select id="selecione" parameterType="map" prefix="true" resultMap="notaFiscalEletronicaRM">
    select
      nota_fiscal_eletronica.*,
      emitente_endereco_nfe.*,
      destinatario_endereco_nfe.*,
      retirada_endereco_nfe.*,
      entrega_endereco_nfe.*,
      transporte.*,
      cobranca.*,
      item_nfe.*,
      pagamento_nfe.*,
      icms.*,
      icms_simples.*,
      pis.*,
      cofins.*,
      icms_uf_destino.*
    from nota_fiscal_eletronica
      left join endereco_nfe emitente_endereco_nfe on nota_fiscal_eletronica.endereco_emitente_id = emitente_endereco_nfe.id
      left join endereco_nfe destinatario_endereco_nfe on nota_fiscal_eletronica.endereco_destinatario_id = destinatario_endereco_nfe.id
      left join endereco_nfe retirada_endereco_nfe on nota_fiscal_eletronica.endereco_retirada_id = retirada_endereco_nfe.id
      left join endereco_nfe entrega_endereco_nfe on nota_fiscal_eletronica.endereco_entrega_id = entrega_endereco_nfe.id
      left join transporte on nota_fiscal_eletronica.transporte_id = transporte.id
      left join cobranca on nota_fiscal_eletronica.cobranca_id = cobranca.id
      left join item_nfe on item_nfe.nota_fiscal_eletronica_id = nota_fiscal_eletronica.id
      left join pagamento_nfe on pagamento_nfe.nota_fiscal_eletronica_id = nota_fiscal_eletronica.id
      left join icms on icms.id = item_nfe.icms_id
      left join icms_simples on icms_simples.id = item_nfe.icms_simples_id
      left join pis on pis.id = item_nfe.pis_id
      left join cofins on cofins.id = item_nfe.cofins_id
      left join icms_uf_destino on icms_uf_destino.id = item_nfe.icms_uf_destino_id
    where nota_fiscal_eletronica.empresa_id = #{idEmpresa}
    <if test="id">
      and nota_fiscal_eletronica.id = #{id}
    </if>
    <if test="idUltima">
      and nota_fiscal_eletronica.id > #{idUltima}
    </if>
    <if test="impressa != null">
      and coalesce(nota_fiscal_eletronica.impressa, false) = #{impressa}
    </if>
    <if test="status">
      and nota_fiscal_eletronica.status = #{status}
    </if>
    <if test="dataInicial">
      and nota_fiscal_eletronica.data_de_emissao &gt;= #{dataInicial}
    </if>
    <if test="dataFinal">
      and nota_fiscal_eletronica.data_de_emissao &lt;= #{dataFinal}
    </if>
    <if test="numeroNFe">
      and nota_fiscal_eletronica.numero_nfe = #{numeroNFe}
    </if>
    <if test="chaveDeAcesso">
      and nota_fiscal_eletronica.chave_da_nota = #{chaveDeAcesso}
    </if>

    <if test="ordenarDecrescente">
      order by nota_fiscal_eletronica.id desc
    </if>

  </select>

    <select id="obtenhaProximoNumeroNFe" parameterType="map" resultType="int">
      SELECT COALESCE(MAX(numero_nfe), 0) + 1 as proximo_numero
      FROM nota_fiscal_eletronica
      WHERE empresa_id = #{idEmpresa}
    </select>

  <update id="atualizeDataEmissao" parameterType="map">
    update nota_fiscal_eletronica set
      data_de_emissao = #{dataDeEmissao}
    where id = #{id} and empresa_id = #{empresa.id}
  </update>

  <!-- atualiza xml, chave, status e motivo da nota fiscal eletronica  -->
  <update id="atualizeStatus" parameterType="map">
    update nota_fiscal_eletronica set
      status = #{status},
      motivo = #{motivo},
      chave_da_nota = #{chaveDeAcesso},
      ultima_alteracao = #{ultimaAlteracao},
      numero_protocolo = #{numeroProtocolo},
      autorizacao_de_uso = #{autorizacaoDeUso},
      documento_xml_gerado = #{documentoXmlGerado},
      qr_code = #{qrCode},
      url_chave = #{urlChave},
      data_autorizacao = #{dataAutorizacao}
    where id = #{id}  and empresa_id = #{empresa.id};
  </update>

  <update id="marqueImpressa" parameterType="map">
    update nota_fiscal_eletronica set
      impressa = true
    where id = #{id}  and empresa_id = #{empresa.id};
  </update>

  <insert id="insira" parameterType="map">
    insert into nota_fiscal_eletronica (
      ambiente,
      chave_da_nota,
      uf_emitente,
      natureza_operacao,
      ind_forma_de_pagamento,
      modelo_documento_fiscal,
      serie,
      numero_nfe,
      data_de_emissao,
      data_de_saida,
      tipo_do_documento,
      tipo_destino_operacao,
      municipio_fator_gerador,
      cnpj_emitente,
      cpf_emitente,
      nome_emitente,
      nome_fantasia_emitente,
      ie_emitente,
      ie_st_emitente,
      inscricao_municipal,
      cnae_emitente,
      cnpj_destinatario,
      cpf_destinatario,
      id_estrangeiro_destinatario,
      nome_destinatario,
      ind_ie_destinatario,
      ie_destinatario,
      im_destinataio,
      inss_suframa_destinatario,
      nome_expedidor,
      email_expedidor,
      ie_expedidor,
      cnpj_expedidor,
      cpf_expedidor,
      cnpj_entrega,
      cpf_entrega,
      nome_recebedor,
      email_recebedor,
      ie_recebedor,
      valor_base_de_calculo_icms,
      valor_icms,
      valor_icms_desonerado,
      valor_base_de_calculo_icms_st,
      valor_icms_st,
      valor_total_produtos,
      valor_do_frete,
      valor_do_seguro,
      valor_do_desconto,
      valor_ii,
      valor_ipi,
      valor_pis,
      valor_cofins,
      valor_outras_despesas,
      valor_total_nfe,
      valor_total_servicos,
      base_de_calculo_iss,
      valor_iss,
      valor_servicos_pis,
      valor_servicos_cofins,
      data_prestacao_servico,
      valor_deducao,
      valor_outras_retencoes,
      valor_desconto_incondicionado,
      valor_desconto_condicionado,
      valor_iss_retido,
      valor_fcp_uf_dest,
      valor_icms_uf_dest,
      valor_icms_uf_remet,
      codigo_regime_especial_tributacao,
      valor_retido_pis,
      valor_retido_cofins,
      valor_retido_csll,
      base_de_calculo_irrf,
      valor_irrf,
      nfe_basecalcretprev,
      nfe_valretidoprevsocial,
      modo_frete,
      informacoes_adicionais_contrib,
      inf_adicionais_fisco,
      hora_saida,
      tipo_impressao_danfe,
      tipo_emissao,
      finalidade,
      consumidor_final,
      comprador_presente,
      status,
      motivo,
      documento_xml_gerado,
      ultima_alteracao,
      data_criacao,
      numero_protocolo,
      data_contingencia,
      just_contingencia,
      codigo_regime_tributario,
      isuf_destinatario,
      email,
      uf_embarque,
      local_embarque,
      local_despacho,
      valor_total_tributacao,
      mod_ecf,
      numero_ecf,
      numero_coo,
      valor_fcp,
      valor_fcp_st,
      valor_fcp_ret,
      valor_ipi_devolvido,
      valor_troco,
      indicador_intermediador,
      cnpj_intermediador,
      identificar_no_intermediador,
      chave_nota_referenciada,
      data_hora_saida,
      total_tributado_mono,
      valor_icms_mono,
      total_tributado_mono_retencao,
      valor_icms_mono_retencao,
      total_tributado_mono_retido,
      valor_icms_mono_retido,
      autorizacao_de_uso,
      empresa_id,
      endereco_emitente_id,
      endereco_destinatario_id,
      endereco_retirada_id,
      endereco_entrega_id,
      transporte_id,
      cobranca_id,
      impressa,
      data_autorizacao
    ) values (
      #{ambiente},
      #{chaveDeAcesso},
      #{ufEmitente},
      #{naturezaOperacao},
      #{indFormaDePagamento},
      #{modeloDocumentoFiscal},
      #{serie},
      #{numeroNFe},
      #{dataDeEmissao},
      #{dataDeSaida},
      #{tipoDoDocumento},
      #{tipoDestinoOperacao},
      #{municipioFatorGerador},
      #{cnpjEmitente},
      #{cpfEmitente},
      #{nomeEmitente},
      #{nomeFantasiaEmitente},
      #{ieEmitente},
      #{ieStEmitente},
      #{inscricaoMunicipal},
      #{cnaeEmitente},
      #{cnpjDestinatario},
      #{cpfDestinatario},
      #{idEstrangeiroDestinatario},
      #{nomeDestinatario},
      #{indIeDestinatario},
      #{ieDestinatario},
      #{imDestinataio},
      #{inssSuframaDestinatario},
      #{nomeExpedidor},
      #{emailExpedidor},
      #{ieExpedidor},
      #{cnpjExpedidor},
      #{cpfExpedidor},
      #{cnpjEntrega},
      #{cpfEntrega},
      #{nomeRecebedor},
      #{emailRecebedor},
      #{ieRecebedor},
      #{valorBaseDeCalculoICMS},
      #{valorICMS},
      #{valorICMSDesonerado},
      #{valorBaseDeCalculoICMSSt},
      #{valorICMSSt},
      #{valorTotalProdutos},
      #{valorDoFrete},
      #{valorDoSeguro},
      #{valorDoDesconto},
      #{valorII},
      #{valorIPI},
      #{valorPis},
      #{valorCofins},
      #{valorOutrasDespesas},
      #{valorTotalNFe},
      #{valorTotalServicos},
      #{baseDeCalculoISS},
      #{valorISS},
      #{valorServicosPis},
      #{valorServicosCofins},
      #{dataPrestacaoServico},
      #{valorDeducao},
      #{valorOutrasRetencoes},
      #{valorDescontoIncondicionado},
      #{valorDescontoCondicionado},
      #{valorISSRetido},
      #{valorFCPUFDest},
      #{valorICMSUFDest},
      #{valorICMSUFRemet},
      #{codigoRegimeEspecialTributacao},
      #{valorRetidoPis},
      #{valorRetidoCofins},
      #{valorRetidoCsll},
      #{baseDeCalculoIRRF},
      #{valorIRRF},
      #{nfeBasecalcretprev},
      #{nfeValretidoprevsocial},
      #{modoFrete},
      #{informacoesAdicionaisContrib},
      #{infAdicionaisFisco},
      #{horaSaida},
      #{tipoImpressaoDanfe},
      #{tipoEmissao},
      #{finalidade},
      #{consumidorFinal},
      #{compradorPresente},
      #{status},
      #{motivo},
      #{documentoXmlGerado},
      #{ultimaAlteracao},
      #{dataCriacao},
      #{numeroProtocolo},
      #{dataContingencia},
      #{justContingencia},
      #{codigoRegimeTributario},
      #{isufDestinatario},
      #{email},
      #{ufEmbarque},
      #{localEmbarque},
      #{localDespacho},
      #{valorTotalTributacao},
      #{modECF},
      #{numeroECF},
      #{numeroCOO},
      #{valorFCP},
      #{valorFCPST},
      #{valorFCPRet},
      #{valorIPIdevolvido},
      #{valorTroco},
      #{indicadorIntermediador},
      #{cnpjIntermediador},
      #{identificarNoIntermediador},
      #{chaveNotaReferenciada},
      #{dataHoraSaida},
      #{totalTributadoMono},
      #{valorICMSMono},
      #{totalTributadoMonoRetencao},
      #{valorICMSMonoRetencao},
      #{totalTributadoMonoRetido},
      #{valorICMSMonoRetido},
      #{autorizacaoDeUso},
      #{empresa.id},
      #{enderecoEmitente.id},
      #{enderecoDestinatario.id},
      #{localRetirada.id},
      #{localEntrega.id},
      #{transporte.id},
      #{cobranca.id},
      false,
      #{dataAutorizacao}
    );
  </insert>


  <create id="crieTabela" parameterType="map">

    create table nota_fiscal_eletronica (
      id int not null auto_increment,
      ambiente int(1),
      chave_da_nota varchar(44),
      uf_emitente int(2),
      natureza_operacao varchar(60),
      ind_forma_de_pagamento int(1),
      modelo_documento_fiscal int(2),
      serie int(3),
      numero_nfe int,
      data_de_emissao datetime,
      data_de_saida datetime,
      tipo_do_documento int(1),
      tipo_destino_operacao int(1),
      municipio_fator_gerador int(7),
      cnpj_emitente varchar(14),
      cpf_emitente varchar(11),
      nome_emitente varchar(60),
      nome_fantasia_emitente varchar(60),
      ie_emitente varchar(14),
      ie_st_emitente varchar(14),
      inscricao_municipal varchar(14),
      cnae_emitente varchar(7),
      cnpj_destinatario varchar(14),
      cpf_destinatario varchar(11),
      id_estrangeiro_destinatario varchar(20),
      nome_destinatario varchar(60),
      ind_ie_destinatario int(1),
      ie_destinatario varchar(14),
      im_destinataio varchar(14),
      inss_suframa_destinatario varchar(9),
      nome_expedidor varchar(60),
      email_expedidor varchar(60),
      ie_expedidor varchar(14),
      cnpj_expedidor varchar(14),
      cpf_expedidor varchar(11),
      cnpj_entrega varchar(14),
      cpf_entrega varchar(11),
      nome_recebedor varchar(60),
      email_recebedor varchar(60),
      ie_recebedor varchar(14),
      valor_base_de_calculo_icms decimal(10,2),
      valor_icms decimal(10,2),
      valor_icms_desonerado decimal(10,2),
      valor_base_de_calculo_icms_st decimal(10,2),
      valor_icms_st decimal(10,2),
      valor_total_produtos decimal(10,2),
      valor_do_frete decimal(10,2),
      valor_do_seguro decimal(10,2),
      valor_do_desconto decimal(10,2),
      valor_ii decimal(10, 2),
      valor_ipi decimal(10,2),
      valor_pis decimal(10,2),
      valor_cofins decimal(10,2),
      valor_outras_despesas decimal(10,2),
      valor_total_nfe decimal(10,2),
      valor_total_servicos decimal(10,2),
      base_de_calculo_iss decimal(10,2),
      valor_iss decimal(10,2),
      valor_servicos_pis decimal(10,2),
      valor_servicos_cofins decimal(10,2),
      data_prestacao_servico datetime,
      valor_deducao decimal(10,2),
      valor_outras_retencoes decimal(10,2),
      valor_desconto_incondicionado decimal(10,2),
      valor_desconto_condicionado decimal(10,2),
      valor_iss_retido decimal(10,2),
      valor_fcp_uf_dest decimal(10,2),
      valor_icms_uf_dest decimal(10,2),
      valor_icms_uf_remet decimal(10,2),
      codigo_regime_especial_tributacao int(2),
      valor_retido_pis decimal(10,2),
      valor_retido_cofins decimal(10,2),
      valor_retido_csll decimal(10,2),
      base_de_calculo_irrf decimal(10,2),
      valor_irrf decimal(10,2),
      nfe_basecalcretprev decimal(10,2),
      nfe_valretidoprevsocial decimal(10,2),
      modo_frete int(1),
      informacoes_adicionais_contrib varchar(500),
      inf_adicionais_fisco varchar(500),
      hora_saida datetime,
      tipo_impressao_danfe int(1),
      tipo_emissao int(1),
      finalidade int(1),
      consumidor_final int(1),
      comprador_presente int(1),
      status int(1),
      motivo varchar(500),
      documento_xml_gerado text,
      ultima_alteracao datetime,
      data_criacao datetime,
      numero_protocolo varchar(15),
      data_contingencia datetime,
      just_contingencia varchar(500),
      codigo_regime_tributario int(1),
      isuf_destinatario varchar(9),
      email varchar(60),
      uf_embarque varchar(2),
      local_embarque varchar(60),
      local_despacho varchar(60),
      valor_total_tributacao decimal(10,2),
      mod_ecf varchar(2),
      numero_ecf varchar(3),
      numero_coo varchar(6),
      valor_fcp decimal(10,2),
      valor_fcp_st decimal(10,2),
      valor_fcp_ret decimal(10,2),
      valor_ipi_devolvido decimal(10,2),
      valor_troco decimal(10,2),
      indicador_intermediador int(1),
      cnpj_intermediador varchar(14),
      identificar_no_intermediador varchar(20),
      chave_nota_referenciada varchar(44),
      data_hora_saida datetime,
      total_tributado_mono decimal(10,2),
      valor_icms_mono decimal(10,2),
      total_tributado_mono_retencao decimal(10,2),
      valor_icms_mono_retencao decimal(10,2),
      total_tributado_mono_retido decimal(10,2),
      valor_icms_mono_retido decimal(10,2),
      autorizacao_de_uso text,
      empresa_id bigint(20) not null,
      endereco_emitente_id bigint(20) not null,
      endereco_destinatario_id bigint(20),
      endereco_retirada_id bigint(20),
      endereco_entrega_id bigint(20),
      transporte_id bigint(20),
      cobranca_id bigint(20),
      data_autorizacao datetime,
      primary key (id),
      constraint foreign key (empresa_id) references empresa(id),
      constraint foreign key (endereco_emitente_id) references endereco_nfe(id),
      constraint foreign key (endereco_destinatario_id) references endereco_nfe(id),
      constraint foreign key (endereco_retirada_id) references endereco_nfe(id),
      constraint foreign key (endereco_entrega_id) references endereco_nfe(id),
      constraint foreign key (transporte_id) references transporte(id),
      constraint foreign key (cobranca_id) references cobranca(id)
    );
  </create>
</mapper>
