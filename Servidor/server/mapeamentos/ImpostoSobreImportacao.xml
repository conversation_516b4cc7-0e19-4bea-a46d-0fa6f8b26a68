<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="impostoSobreImportacao">
  <resultMap id="impostoSobreImportacaoRM" type="ImpostoSobreImportacao">
    <id property="id" column="imposto_sobre_importacao_id"/>
    <result property="iiValbasecalc" column="imposto_sobre_importacao_val_base_calc"/>
    <result property="iiValdespaduane" column="imposto_sobre_importacao_val_desp_aduane"/>
    <result property="iiValor" column="imposto_sobre_importacao_valor"/>
    <result property="iiValoriof" column="imposto_sobre_importacao_valor_iof"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists imposto_sobre_importacao (
      id bigint(20) primary key,
      val_base_calc decimal(10,2),
      val_desp_aduane decimal(10,2),
      valor decimal(10,2),
      valor_iof decimal(10,2)
    );
  </create>
</mapper>
