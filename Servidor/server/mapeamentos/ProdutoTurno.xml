<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="produtoTurno">
  <resultMap id="produtoTurnoRM" type="ProdutoTurno">
    <id property="id" column="produto_turno_id"/>

    <result property="horaInicio" column="produto_turno_hora_inicio"/>
    <result property="horaFim" column="produto_turno_hora_fim"/>

  </resultMap>

  <select id="selecione" resultMap="produtoTurnoRM" prefix="true">
    select * from produto_turno  where produto_id in
    <foreach item="id" collection="ids" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <insert id="insira">
    insert into produto_turno(produto_id, hora_inicio, hora_fim)
            values (#{produto.id}, #{horaInicio},  #{horaFim})
            ;
  </insert>


  <update id="atualize" parameterType="map"  >
    update   produto_turno
     set   hora_inicio = #{horaInicio}, hora_fim = #{horaFim}
        where id = #{id}
  </update>

  <update id="remova" parameterType="map"  >
    delete from produto_turno  where id = #{id}
  </update>
</mapper>
