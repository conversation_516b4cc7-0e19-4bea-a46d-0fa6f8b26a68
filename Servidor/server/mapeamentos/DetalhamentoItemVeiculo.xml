<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="itemVeiculo" >
  <resultMap id="itemVeiculoRM" type="DetalhamentoItemVeiculo">
    <id property="id" column="item_veiculo_id"/>
    <result property="tipoOperacao" column="item_veiculo_tipo_operacao"/>
    <result property="chassi" column="item_veiculo_chassi"/>
    <result property="codigoCor" column="item_veiculo_codigo_cor"/>
    <result property="descricaoCor" column="item_veiculo_descricao_cor"/>
    <result property="potenciaMotor" column="item_veiculo_potencia_motor"/>
    <result property="cilindradas" column="item_veiculo_cilindradas"/>
    <result property="pesoLiquido" column="item_veiculo_peso_liquido"/>
    <result property="pesoBruto" column="item_veiculo_peso_bruto"/>
    <result property="serial" column="item_veiculo_serial"/>
    <result property="tipoDeCombustivel" column="item_veiculo_tipo_de_combustivel"/>
    <result property="numeroDeMotor" column="item_veiculo_numero_de_motor"/>
    <result property="cmt" column="item_veiculo_cmt"/>
    <result property="distanciaEntreEixos" column="item_veiculo_distancia_entre_eixos"/>
    <result property="renavam" column="item_veiculo_renavam"/>
    <result property="anoModelo" column="item_veiculo_ano_modelo"/>
    <result property="anoFabricacao" column="item_veiculo_ano_fabricacao"/>
    <result property="tipoDePintura" column="item_veiculo_tipo_de_pintura"/>
    <result property="tipoDeVeiculo" column="item_veiculo_tipo_de_veiculo"/>
    <result property="especieDeVeiculo" column="item_veiculo_especie_de_veiculo"/>
    <result property="vin" column="item_veiculo_vin"/>
    <result property="condicaoDoVeiculo" column="item_veiculo_condicao_do_veiculo"/>
    <result property="codigoMarcaModelo" column="item_veiculo_codigo_marca_modelo"/>
    <result property="codigoCorDENATRAN" column="item_veiculo_codigo_cor_denatran"/>
    <result property="lotacao" column="item_veiculo_lotacao"/>
    <result property="codigoRestricao" column="item_veiculo_codigo_restricao"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists item_veiculo (
      id bigint(20) primary key,
      tipo_operacao int,
      chassi varchar(100),
      codigo_cor varchar(100),
      descricao_cor varchar(100),
      potencia_motor varchar(100),
      cilindradas varchar(100),
      peso_liquido varchar(100),
      peso_bruto varchar(100),
      serial varchar(100),
      tipo_de_combustivel varchar(100),
      numero_de_motor varchar(100),
      cmt varchar(100),
      distancia_entre_eixos varchar(100),
      renavam varchar(100),
      ano_modelo int,
      ano_fabricacao int,
      tipo_de_pintura varchar(100),
      tipo_de_veiculo int,
      especie_de_veiculo int,
      vin varchar(100),
      condicao_do_veiculo int,
      codigo_marca_modelo int,
      codigo_cor_denatran varchar(100),
      lotacao varchar(100),
      codigo_restricao varchar(100)
    );
  </create>
</mapper>

