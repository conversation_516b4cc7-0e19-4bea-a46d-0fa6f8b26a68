<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="configMensagemDeBot">
  <resultMap id="configMensagemDeBotResultMap" type="ConfigMensagemDeBot">
    <id property="id" column="config_mensagem_de_bot_id"/>

    <result property="tipoDeMensagem" column="config_mensagem_de_bot_tipo_de_mensagem"/>

    <result property="mensagem" column="config_mensagem_de_bot_mensagem"/>
    <result property="encurtarLinks" column="config_mensagem_de_bot_encurtar_links"/>


    <association property="empresa"   resultMap="empresa.empresaRM"/>
  </resultMap>


  <update id="atualize" parameterType="ConfigMensagemDeBot" keyProperty="id">
    update config_mensagem_de_bot set mensagem = #{mensagem},
    encurtar_links = #{encurtarLinks}
    where    id = #{id}   and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeEncurtarLinks">
    update config_mensagem_de_bot set   encurtar_links = #{encurtarLinks}  where   empresa_id = #{idEmpresa} and tipo_de_mensagem =  #{tipo};
  </update>


  <select id="selecione" parameterType="map" resultMap="configMensagemDeBotResultMap" prefix="true">
    select    *
    from config_mensagem_de_bot
    join empresa on config_mensagem_de_bot.empresa_id = empresa.id
    where
    empresa_id = #{idEmpresa}

    <if test="tipoDeMensagem != null">
      and config_mensagem_de_bot.tipo_de_mensagem = #{tipoDeMensagem}
    </if>

    <if test="ativa != null">
      and empresa.bloqueada is not true
    </if>

  </select>
</mapper>
