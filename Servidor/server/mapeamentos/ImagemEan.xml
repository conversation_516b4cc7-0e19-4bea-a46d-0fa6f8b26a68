<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="imagemCodigoDeBarras">
  <resultMap id="imagemEanRM" type="ImagemEan">
    <id property="codigoDeBarras" column="imagem_ean_codigo_de_barras"/>
    <result property="linkImagem" column="imagem_ean_link_imagem"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="imagemEanRM" prefix="true">
    select *
    from imagem_ean
    <if test="codigoDeBarras != null">
      where codigo_de_barras = #{codigoDeBarras}
    </if>
    <if test="semImagem != null">
      where link_imagem is null
      and codigo_de_barras like '7%'
      limit 1150, 10000000;
    </if>
  </select>

  <update id="atualize">
    update imagem_ean
    set link_imagem = #{linkImagem}
      where codigo_de_barras = #{codigoDeBarras};
  </update>
</mapper>
