<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="orderEvent">
  <resultMap id="OrderEventRM" type="OrderEvent">
    <id property="id" column="order_event_id"/>

    <result property="eventId" column="order_event_event_id"/>
    <result property="eventType" column="order_event_event_type"/>
    <result property="orderId" column="order_event_order_id"/>
    <result property="createdAt" column="order_event_created_at"/>
    <result property="sourceAppId" column="order_event_source_app_id"/>
    <result property="reason" column="order_event_reason"/>
    <result property="reasonDeny" column="order_event_reason_deny"/>
    <result property="reconhecido" column="order_event_reconhecido"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="OrderEventRM" prefix="true">
    select * from order_event where  empresa_id = #{idEmpresa}
      and
    <choose>
      <when test="id">
        id = #{id}
      </when>
      <when test="eventId">
        event_id = #{eventId}
      </when>

      <when test="orderId">
        order_id = #{orderId}

        <if test="tipo">
           and event_type = #{tipo} order by id desc limit 1
        </if>

      </when>

      <when test="naoLidos">
        reconhecido is not true order by id
      </when>
    </choose>
  </select>

  <update id="atualize" parameterType="map">
      update order_event
            set reconhecido  = true
      where id = #{id} and empresa_id = #{empresa.id}
  </update>

   <update id="atualizeRetorno" parameterType="map">
      update order_event
            set reason_deny  = #{reasonDeny}
      where id = #{id} and empresa_id = #{empresa.id}
  </update>



</mapper>
