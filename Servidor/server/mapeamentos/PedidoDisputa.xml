<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="pedidoDisputa">

  <resultMap id="pedidoDisputaRM" type="PedidoDisputa">
    <id property="id" column="pedido_disputa_id"/>

    <result property="guid" column="pedido_disputa_guid" />
    <result property="justificativa" column="pedido_disputa_justificativa" />
    <result property="acao" column="pedido_disputa_acao" />
    <result property="acaoTimeout" column="pedido_disputa_acao_timeout" />
    <result property="tipo" column="pedido_disputa_tipo" />
    <result property="dataCriacao" column="pedido_disputa_data_criacao" />
    <result property="dataExpiracao" column="pedido_disputa_data_expiracao" />
    <result property="dadosDisputa" column="pedido_disputa_dados_disputa" />
    <result property="dadosAcordo" column="pedido_disputa_dados_acordo" />
    <result property="resultadoDisputa" column="pedido_disputa_resultado_disputa" />
    <result property="motivoRejeicao" column="pedido_disputa_motivo_rejeicao" />
    <result property="finalizada" column="pedido_disputa_finalizada" />
    <result property="aguardandoProposta" column="pedido_disputa_aguardando_proposta" />
    <result property="legado" column="pedido_disputa_legado" />
    <association property="pedido" resultMap="pedido.pedidoRM"/>
  </resultMap>

  <resultMap id="disputaDoPedidoRM" type="PedidoDisputa">
    <id property="id" column="pedido_disputa_id"/>

    <result property="guid" column="pedido_disputa_guid" />
    <result property="justificativa" column="pedido_disputa_justificativa" />
    <result property="acao" column="pedido_disputa_acao" />
    <result property="acaoTimeout" column="pedido_disputa_acao_timeout" />
    <result property="tipo" column="pedido_disputa_tipo" />
    <result property="dataCriacao" column="pedido_disputa_data_criacao" />
    <result property="dataExpiracao" column="pedido_disputa_data_expiracao" />
    <result property="dadosDisputa" column="pedido_disputa_dados_disputa" />
    <result property="dadosAcordo" column="pedido_disputa_dados_acordo" />
    <result property="resultadoDisputa" column="pedido_disputa_resultado_disputa" />
    <result property="motivoRejeicao" column="pedido_disputa_motivo_rejeicao" />
    <result property="finalizada" column="pedido_disputa_finalizada" />
    <result property="aguardandoProposta" column="pedido_disputa_aguardando_proposta" />
    <result property="legado" column="pedido_disputa_legado" />
  </resultMap>


  <select id="selecione" parameterType="map" resultMap="pedidoDisputaRM" prefix="true">
    select * from  pedido_disputa join pedido on pedido.id  = pedido_id
     where   pedido_disputa.empresa_id= #{idEmpresa} and finalizada is not true and data_expiracao > now()

  </select>

  <update id="atualize">
    update pedido_disputa set
     resultado_disputa = #{resultadoDisputa},
      motivo_rejeicao = #{motivoRejeicao},
      finalizada = #{finalizada}, dados_acordo = #{dadosAcordo}

      where id = #{id} and empresa_id = #{empresa.id}
  </update>
  <update id="atualizeAguardandoPorposta">
    update pedido_disputa set aguardando_proposta = #{aguardandoProposta}

    where id = #{id} and empresa_id = #{empresa.id}

  </update>
</mapper>
