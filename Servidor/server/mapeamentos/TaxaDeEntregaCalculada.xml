<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="taxaEntregaCalculada">
  <resultMap id="taxaEntregaCalculadaRM" type="TaxaDeEntregaCalculada">
    <id property="id" column="taxa_de_entrega_calculada_id"/>

    <result property="erro" column="taxa_de_entrega_calculada_erro"/>
    <result property="valor" column="taxa_de_entrega_calculada_valor"/>
    <result property="taxaRetorno" column="taxa_de_entrega_calculada_taxa_retorno"/>
    <result property="tipoDeCobranca" column="taxa_de_entrega_calculada_tipo_de_cobranca"/>
    <result property="distancia" column="taxa_de_entrega_calculada_distancia"/>
    <result property="localizacao" column="taxa_de_entrega_calculada_localizacao"/>
    <result property="endereco" column="taxa_de_entrega_calculada_endereco"/>
    <result property="sucesso" column="taxa_de_entrega_calculada_sucesso"/>
    <result property="fazEntrega" column="taxa_de_entrega_calculada_faz_entrega"/>
    <result property="horario" column="taxa_de_entrega_calculada_horario"/>
    <result property="simulacaoId" column="taxa_de_entrega_calculada_simulacao_id"/>
    <result property="calculadoPor" column="taxa_de_entrega_calculada_calculado_por"/>

    <association property="empresa"  resultMap="empresa.empresaRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="taxaEntregaCalculadaRM" prefix="true">
    select *
      from taxa_de_entrega_calculada
    where
      empresa_id = #{idEmpresa}

      <if test="id">
        and id = #{id}
      </if>
      <if test="apenasEntregas">
        and faz_entrega = true
      </if>
      <if test="apenasFora">
        and faz_entrega = false
      </if>
      <if test="busca != null">
        and endereco like CONCAT('%', #{busca}, '%')
      </if>
      <if test="dataInicial != null">
        and horario &gt;= #{dataInicial}
      </if>
      <if test="dataFinal != null">
        and horario &lt;= #{dataFinal}
      </if>
      order by horario desc
      <if test="inicio != null">
        limit #{inicio}, #{total}
      </if>
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int" prefix="true">
    select count(*)
    from taxa_de_entrega_calculada
    where
    empresa_id = #{idEmpresa}
    <if test="apenasEntregas">
      and faz_entrega = true
    </if>
    <if test="apenasFora">
      and faz_entrega = false
    </if>
    <if test="busca != null">
      and endereco like CONCAT('%', #{busca}, '%')
    </if>
    <if test="dataInicial != null">
      and horario &gt;= #{dataInicial}
    </if>
    <if test="dataFinal != null">
      and horario &lt;= #{dataFinal}
    </if>
  </select>
</mapper>
