<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="horarioDeFuncionamento">
  <resultMap id="horarioDeFuncionamentoRM" type="HorarioFuncionamento">
    <id property="id" column="horario_id"/>

    <result property="diaDaSemana" column="horario_dia_da_semana"/>
    <result property="funciona" column="horario_funciona"/>
    <result property="horarioAbertura" column="horario_horario_abertura"/>
    <result property="horarioFechamento" column="horario_horario_fechamento"/>
    <result property="servico" column="horario_servico"/>
  </resultMap>


  <select id="selecione" parameterType="map" resultMap="horarioDeFuncionamentoRM">
    select * from horario_funcionamento h  where empresa_id = #{idEmpresa};
  </select>

  <insert id="insira">
    insert into horario_funcionamento(empresa_id, dia_da_semana, horario_abertura, horario_fechamento, funciona , servico)
        values (#{empresa.id}, #{diaDaSemana},  #{horarioAbertura}, #{horarioFechamento}, #{funciona}, #{servico});
  </insert>

  <update id="atualize" parameterType="map"  >
    update horario_funcionamento
      set   funciona = #{funciona},
            horario_abertura = #{horarioAbertura},
            horario_fechamento = #{horarioFechamento}

        where id = #{id}
  </update>

  <delete id="remova" parameterType="map">
    delete from horario_funcionamento where id = #{id};
  </delete>

</mapper>
