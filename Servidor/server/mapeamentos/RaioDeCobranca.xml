
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="raioDeCobranca">
  <resultMap id="raioDeCobrancaRM" type="RaioDeCobranca">
    <id property="id" column="raio_de_cobranca_id"/>

    <result property="tipo" column="raio_de_cobranca_tipo"/>
    <result property="alcance" column="raio_de_cobranca_alcance"/>
    <result property="valorFixo" column="raio_de_cobranca_valor_fixo"/>
    <result property="valorKmTaxa" column="raio_de_cobranca_valor_km_taxa"/>
    <result property="valorMinimoTaxa" column="raio_de_cobranca_valor_minimo_taxa"/>
    <result property="valorMinimoPedido" column="raio_de_cobranca_valor_minimo_pedido"/>
    <result property="desativado" column="raio_de_cobranca_desativado"/>
    <result property="permiteFreteGratis" column="raio_de_cobranca_permite_frete_gratis"/>
  </resultMap>


  <insert id="insira" parameterType="RaioDeCobranca" keyProperty="id">
    insert into raio_de_cobranca (tipo, alcance, valor_fixo, valor_km_taxa, valor_minimo_taxa, empresa_forma_de_entrega_id, valor_minimo_pedido, permite_frete_gratis)
              values(#{tipo}, #{alcance}, #{valorFixo}, #{valorKmTaxa}, #{valorMinimoTaxa}, #{formaDeEntregaEmpresa.id}, #{valorMinimoPedido},
                       #{permiteFreteGratis});
  </insert>


  <update id="atualize">
    update raio_de_cobranca
          set tipo = #{tipo},
              alcance =  #{alcance},
              valor_fixo =  #{valorFixo},
              valor_km_taxa  = #{valorKmTaxa},
              valor_minimo_taxa = #{valorMinimoTaxa},
              valor_minimo_pedido = #{valorMinimoPedido},
              permite_frete_gratis = #{permiteFreteGratis},
              desativado = #{desativado}
          where id = #{id};
  </update>


  <update id="remova">
    delete from raio_de_cobranca where id = #{id};
  </update>

</mapper>
