<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="bearerToken">
  <resultMap id="bearerTokenResultMap" type="BearerToken">
    <id property="id" column="bearer_token_id"/>

    <result property="token" column="bearer_token_token"/>
    <result property="dataCriacao" column="bearer_token_data_criacao"/>

    <association property="usuario"   resultMap="usuario.usuarioResultMap"/>
    <association property="cliente"   resultMap="clienteApi.clienteApiResultMap"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="bearerTokenResultMap" prefix="true">
    select bearer_token.*, usuario.*, cliente_api.*
    from    bearer_token left join usuario  on (bearer_token.usuario_id = usuario.id)
                    inner join cliente_api on (bearer_token.cliente_id = cliente_api.id)
    where   bearer_token.empresa_id = #{idEmpresa}
    <choose>
      <when test="token != null">
        and bearer_token.token  = #{token}
      </when>

    </choose>
  </select>

</mapper>
