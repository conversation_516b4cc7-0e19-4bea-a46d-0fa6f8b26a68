<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="estado_chatbot">
  <resultMap id="estadoChatbotRM" type="EstadoChatbot">
    <id property="id" column="estado_chatbot_id"/>

    <result property="telefone" column="estado_chatbot_telefone"/>
    <result property="intent" column="estado_chatbot_intent"/>
    <result property="comando" column="estado_chatbot_comando"/>
    <result property="mensagem" column="estado_chatbot_mensagem"/>
    <result property="resposta" column="estado_chatbot_resposta"/>
    <result property="atendente" column="estado_chatbot_atendente"/>
    <result property="desativarParaSempre" column="estado_chatbot_desativar_para_sempre"/>

    <result property="historico" column="estado_chatbot_historico"/>
    <result property="estado" column="estado_chatbot_estado"/>

    <result property="ultimaInteracao" column="estado_chatbot_ultima_interacao"/>

    <result property="idSessaoTypebot" column="estado_chatbot_id_sessao_typebot"/>
    <result property="idFluxoTypebot" column="estado_chatbot_id_fluxo_typebot"/>
    <result property="idResultadoTypebot" column="estado_chatbot_id_resultado_typebot"/>

    <result property="endereco" column="estado_chatbot_endereco"/>

    <association property="sessaoLinkSaudacao" resultMap="sessaoLinkSaudacao.sessaoLinkSaudacaoRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="estadoChatbotRM" prefix="true">
    SELECT * FROM estado_chatbot left join sessao_link_saudacao on estado_chatbot.sessao_link_saudacao_id = sessao_link_saudacao.id
    WHERE
    estado_chatbot.empresa_id = #{idEmpresa}
    <if test="id != null">
      AND estado_chatbot.id = #{id}
    </if>
    <if test="telefone != null">
      AND estado_chatbot.telefone = #{telefone}
    </if>
    <if test="inicio != null and total != null">
      LIMIT #{inicio}, #{total}
    </if>
  </select>

  <update id="atualize">
    UPDATE estado_chatbot SET
    telefone = #{telefone},
    ultima_interacao = #{ultimaInteracao},
    mensagem = #{mensagem},
    intent = #{intent},
    atendente = #{atendente},
    historico = #{historico},
    estado = #{estado},
    desativar_para_sempre = #{desativarParaSempre},
    comando = #{comando},
    resposta = #{resposta},
    id_sessao_typebot = #{idSessaoTypebot},
    id_fluxo_typebot = #{idFluxoTypebot},
    id_resultado_typebot = #{idResultadoTypebot},
    sessao_link_saudacao_id = #{sessaoLinkSaudacao.id},
    endereco = #{endereco}
    WHERE id = #{id}
    and empresa_id = #{empresa.id};
  </update>
</mapper>
