<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mensagemBot">
  <resultMap id="mensagemBotRM" type="MensagemBot">
    <id property="id" column="mensagem_bot_id"/>

    <result property="mensagem" column="mensagem_bot_mensagem"/>
    <result property="resposta" column="mensagem_bot_resposta"/>
    <result property="telefone" column="mensagem_bot_telefone"/>
    <result property="chatId" column="mensagem_bot_chat_id"/>
    <result property="horario" column="mensagem_bot_horario"/>
    <result property="horarioModificacao" column="mensagem_bot_horario_modificacao"/>
  </resultMap>

  <resultMap id="conversaBotRM" type="ConversaBot">
    <id property="id" column="telefone"/>

    <result property="telefone" column="telefone"/>
    <result property="nome" column="nome"/>

    <result property="ultimaMensagem" column="ultima_mensagem"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="mensagemBotRM" prefix="true">
    select *
    from mensagem_bot
    where
    mensagem_bot.empresa_id = #{idEmpresa}
    <if test="id != null">
      and mensagem_bot.id = #{id}
    </if>
    <if test="telefone != null">
      and mensagem_bot.telefone = #{telefone}
    </if>

    <choose>
      <when test="inicio != null">
        order by mensagem_bot.id limit #{inicio},#{total}
      </when>
    </choose>
  </select>

  <select id="ultimasConversas" parameterType="map" resultMap="conversaBotRM">
    SELECT
    distinct mensagem_bot.nome, mensagem_bot.telefone, mensagem_bot.mensagem ultima_mensagem
    FROM
    mensagem_bot
    INNER JOIN
    (SELECT telefone, MAX(horario) AS ultimo_horario FROM mensagem_bot
      WHERE
      empresa_id = #{idEmpresa} GROUP BY telefone) m2
      ON mensagem_bot.telefone = m2.telefone AND mensagem_bot.horario = m2.ultimo_horario
    WHERE mensagem_bot.empresa_id = #{idEmpresa}
    ORDER BY
    mensagem_bot.horario DESC;
  </select>
</mapper>
