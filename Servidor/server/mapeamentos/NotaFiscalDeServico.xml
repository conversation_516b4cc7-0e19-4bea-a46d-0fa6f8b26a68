<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="notaFiscalDeServico">
  <resultMap id="notaFiscalDeServicoRM" type="NotaFiscalDeServico">
    <id property="id" column="nota_fiscal_de_servico_id"/>

    <result property="numeroRps" column="nota_fiscal_de_servico_numero_rps"/>
    <result property="numeroDaNota" column="nota_fiscal_de_servico_numero_da_nota"/>
    <result property="codigoVerificacao" column="nota_fiscal_de_servico_codigo_verificacao"/>
    <result property="aprovada" column="nota_fiscal_de_servico_aprovada"/>
    <result property="dataEmissao" column="nota_fiscal_de_servico_data_emissao"/>
    <result property="xml" column="nota_fiscal_de_servico_xml" />
    <result property="xmlResposta" column="nota_fiscal_de_servico_xml_resposta" />
    <result property="valorServicos" column="nota_fiscal_de_servico_valor_servicos" />
    <result property="discriminacao" column="nota_fiscal_de_servico_discriminacao"/>
    <result property="tipoDocumentoTomador" column="nota_fiscal_de_servico_tipo_documento_tomador"/>
    <result property="cnpjTomador" column="nota_fiscal_de_servico_cnpj_tomador"/>
    <result property="cpfTomador" column="nota_fiscal_de_servico_cpf_tomador"/>
    <result property="nomeTomador" column="nota_fiscal_de_servico_nome_tomador"/>

    <association property="fatura"   resultMap="fatura.faturaRM"/>
    <association property="empresaTomador" column="empresa_tomador_id" resultMap="empresa.empresaRM"/>
    <association property="enderecoTomador" column="endereco_tomador_id" resultMap="endereco.enderecoRM"/>

  </resultMap>

  <resultMap id="notaFiscalDeServicoDaFaturaRM" type="NotaFiscalDeServico">
    <id property="id" column="nota_fiscal_de_servico_id"/>

    <result property="numeroRps" column="nota_fiscal_de_servico_numero_rps"/>
    <result property="numeroDaNota" column="nota_fiscal_de_servico_numero_da_nota"/>
    <result property="codigoVerificacao" column="nota_fiscal_de_servico_codigo_verificacao"/>
    <result property="aprovada" column="nota_fiscal_de_servico_aprovada"/>
    <result property="dataEmissao" column="nota_fiscal_de_servico_data_emissao"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="notaFiscalDeServicoRM" prefix="true">
    select * from nota_fiscal_de_servico
             join fatura on fatura.id = nota_fiscal_de_servico.fatura_id
             join empresa on empresa.id = nota_fiscal_de_servico.empresa_tomador_id
             left join endereco on endereco.id = nota_fiscal_de_servico.endereco_tomador_id
    <choose>
      <when test="id">
        where id = #{id}
      </when>
      <when test="fatura">
        where fatura_id = #{fatura.id}
      </when>
    </choose>
  </select>

  <!--
                    this.nota.aprovada = true;
                  this.nota.numeroDaNota = resposta.resumo.numero;
                  this.nota.codigoVerificacao = resposta.resumo.codigoVerificacao;
                  this.nota.xmlResposta =  resposta.xmlNfseProcessado

  -->
 <update id="atualize" parameterType="map">
   update nota_fiscal_de_servico
   set xml = #{xml},
       data_emissao = #{dataEmissao},
       tipo_documento_tomador = #{tipoDocumentoTomador},
       cnpj_tomador = #{cnpjTomador},
       cpf_tomador = #{cpfTomador},
       nome_tomador = #{nomeTomador},
       endereco_tomador_id = #{enderecoTomador.id},
       discriminacao = #{discriminacao},
       valor_servicos = #{valorServicos}
   where id = #{id}
 </update>

  <update id="foiAprovada" parameterType="map">
    update nota_fiscal_de_servico
    set aprovada = true,
        numero_da_nota = #{numeroDaNota},
        codigo_verificacao = #{codigoVerificacao},
        xml_resposta = #{xmlResposta}
    where id = #{id}
  </update>


  <select id="selecioneUltimoRps" parameterType="map" resultType="int">
    select max(numero_rps)
    from
    nota_fiscal_de_servico;
  </select>

</mapper>
