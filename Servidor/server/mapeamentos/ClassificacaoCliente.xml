<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ClassificacaoCliente">

    <insert id="insira" parameterType="ClassificacaoCliente">
        INSERT INTO classificacao_cliente (
            id_cliente,
            tipo_classificacao,
            data_classificacao,
            periodo_inicio,
            periodo_fim,
            qtd_compras,
            ticket_medio
        ) VALUES (
            #{idCliente},
            #{tipoClassificacao},
            #{dataClassificacao},
            #{periodoInicio},
            #{periodoFim},
            #{qtdCompras},
            #{ticketMedio}
        )
    </insert>

    <select id="selecionePorTipo" resultType="ClassificacaoCliente">
        SELECT
            id,
            id_cliente as idCliente,
            tipo_classificacao as tipoClassificacao,
            data_classificacao as dataClassificacao,
            periodo_inicio as periodoInicio,
            periodo_fim as periodoFim,
            qtd_compras as qtdCompras,
            ticket_medio as ticketMedio
        FROM classificacao_cliente
        WHERE tipo_classificacao = #{tipo}
        AND data_classificacao = (
            SELECT MAX(data_classificacao)
            FROM classificacao_cliente
            WHERE tipo_classificacao = #{tipo}
        )
    </select>

    <select id="selecionePorTipos" resultType="ClassificacaoCliente">
        SELECT
            id,
            id_cliente as idCliente,
            tipo_classificacao as tipoClassificacao,
            data_classificacao as dataClassificacao,
            periodo_inicio as periodoInicio,
            periodo_fim as periodoFim,
            qtd_compras as qtdCompras,
            ticket_medio as ticketMedio
        FROM classificacao_cliente
        WHERE tipo_classificacao IN
        <foreach item="item" collection="tipos" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND data_classificacao = (
            SELECT MAX(data_classificacao)
            FROM classificacao_cliente
            WHERE tipo_classificacao = classificacao_cliente.tipo_classificacao
        )
    </select>

    <delete id="limpeClassificacoes">
        DELETE FROM classificacao_cliente
        WHERE data_classificacao &lt; #{data}
    </delete>

</mapper>
