<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="assinatura">
  <resultMap id="assinaturaRM" type="Assinatura">
    <id property="id" column="assinatura_id"/>

    <result property="codigo" column="assinatura_codigo"/>
    <result property="codigoPai" column="assinatura_codigo_pai"/>
    <result property="dataCriacao" column="assinatura_data_criacao"/>
    <result property="dataAtualizacao" column="assinatura_data_atualizacao"/>
    <result property="dataVencimento" column="assinatura_data_vencimento"/>
    <result property="suspensa" column="assinatura_suspensa"/>
    <result property="ativa" column="assinatura_ativa"/>
    <result property="formasDePagamento" column="assinatura_formas_de_pagamento"/>
    <result property="identificadorPlano" column="assinatura_identificador_plano"/>
    <result property="dados" column="assinatura_dados"/>
    <result property="dataPrimeiroPagamento" column="assinatura_data_primeiro_pagamento"/>
    <result property="dataUltimoPagamento" column="assinatura_data_ultimo_pagamento"/>
    <result property="valorDoPlano" column="assinatura_valor_do_plano"/>

    <association property="empresa"  resultMap="empresa.empresaRM"/>
    <association property="contrato"  resultMap="contrato.contratoRM"/>
    <association property="cartao"    resultMap="cartaocredito.cartaocreditoRM"/>

    <collection  property="itens"   resultMap="itemAssinatura.itemAssinaturaRM"/>
  </resultMap>

  <resultMap id="assinaturaContratoRM" type="Assinatura">
    <id property="id" column="assinatura_id"/>

    <result property="codigo" column="assinatura_codigo"/>
    <result property="codigoPai" column="assinatura_codigo_pai"/>
    <result property="dataCriacao" column="assinatura_data_criacao"/>
    <result property="dataAtualizacao" column="assinatura_data_atualizacao"/>
    <result property="dataVencimento" column="assinatura_data_vencimento"/>
    <result property="suspensa" column="assinatura_suspensa"/>
    <result property="ativa" column="assinatura_ativa"/>
    <result property="formasDePagamento" column="assinatura_formas_de_pagamento"/>
    <result property="identificadorPlano" column="assinatura_identificador_plano"/>
    <result property="dados" column="assinatura_dados"/>
    <result property="dataPrimeiroPagamento" column="assinatura_data_primeiro_pagamento"/>
    <result property="dataUltimoPagamento" column="assinatura_data_ultimo_pagamento"/>
    <result property="valorDoPlano" column="assinatura_valor_do_plano"/>
    <association property="cartao"    resultMap="cartaocredito.cartaocreditoRM"/>

    <collection  property="itens"   resultMap="itemAssinatura.itemAssinaturaRM"/>
    <collection  property="assinaturasDependentes"   resultMap="assinatura.assinaturaDependenteRM"/>

  </resultMap>

  <resultMap id="assinaturaDependenteRM"  type="Assinatura">
    <id property="id" column="assinatura_dependente_id"/>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="assinaturaRM" prefix="true">
    select  *
        from assinatura join contrato on contrato.assinatura_id = assinatura.id
                        join empresa on empresa.id = contrato.empresa_id
                        join plano_empresarial on  contrato.plano_id = plano_empresarial.id
                        left join item_assinatura on item_assinatura.assinatura_id = assinatura.id and item_assinatura.removido is not true
                        left join usuario on usuario.id = empresa.responsavel_id
                        left join cartao_credito on cartao_credito.id = assinatura.cartao_credito_id
                        left join   (select max(id) id, empresa_id from acao_contato group by empresa_id) acoes on(acoes.empresa_id = empresa.id)
                        left join acao_contato on(acao_contato.id = acoes.id)
                <choose>
                    <when test="id"> where assinatura.id = #{id}</when>
                    <when test="codigo"> where assinatura.codigo = #{codigo}</when>
                    <when test="ativas"> where assinatura.ativa is true </when>

                </choose>
                 <if test="ordemInversa">
                   order by assinatura.id desc
                 </if>

                  <if test="inicio != null">
                    limit #{inicio}, #{total}
                  </if>
  </select>

  <update id="atualize"  parameterType="map">
    update assinatura
        set formas_de_pagamento = #{formasDePagamento}, data_atualizacao = #{dataAtualizacao}, dados = #{dados},
              data_vencimento = #{dataVencimento},
              suspensa = #{suspensa}, ativa = #{ativa}
          where id = #{id}
  </update>

  <update id="atualizeCartaoCredito" parameterType="map">
    update assinatura
          set cartao_credito_id = #{cartao.id},  formas_de_pagamento = #{formasDePagamento}, data_atualizacao = now()
              where id = #{id}
  </update>

  <update id="atualizeFormasPagamento" parameterType="map">
    update assinatura
          set formas_de_pagamento = #{formasDePagamento},   data_atualizacao = now()
              where id = #{id}
  </update>

  <update id="atualizeDatasPagamento">
    update assinatura
          set  data_primeiro_pagamento = #{dataPrimeiroPagamento} , data_ultimo_pagamento = #{dataUltimoPagamento},
                  data_atualizacao = now()
          where id = #{id}
  </update>

  <update id="atualizeDataVencimento">
    update assinatura
          set  data_vencimento = #{dataVencimento},  data_atualizacao = now()
          where id = #{id}
  </update>

  <update id="atualizeValorPlano">
    update assinatura
          set  valor_do_plano = #{valorDoPlano}
          where id = #{id}
  </update>

  <update id="atualizeSuspensa">
    update assinatura
    set   suspensa = #{suspensa},  data_atualizacao = now()
    where id = #{id}
  </update>


</mapper>
