<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="modalidadeBaseDeCalculoICMSST">
    <resultMap id="modalidadeBaseDeCalculoICMSSTRM" type="ModalidadeBaseDeCalculoICMSST">
      <id property="id" column="modalidade_base_de_calculo_icms_st_id"/>

      <result property="codigo" column="modalidade_base_de_calculo_icms_st_codigo"/>
      <result property="descricao" column="modalidade_base_de_calculo_icms_st_descricao"/>
    </resultMap>

    <select id="selecione" parameterType="map" resultMap="modalidadeBaseDeCalculoICMSSTRM" prefix="true">
      select * from modalidade_base_de_calculo_icms_st
      where 1 = 1
      <if test="id">
        and id = #{id}
      </if>
      <if test="codigo">
        and codigo = #{codigo}
      </if>

    </select>
</mapper>
