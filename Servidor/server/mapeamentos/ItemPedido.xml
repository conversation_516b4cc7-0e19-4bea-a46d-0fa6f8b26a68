<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="itemPedido">

  <resultMap id="itemPedidoRM" type="ItemPedido">
    <id property="id" column="item_pedido_id"/>

    <result property="descricao" column="item_pedido_descricao"/>
    <result property="valor" column="item_pedido_valor"/>
    <result property="qtde" column="item_pedido_qtde"/>
    <result property="total" column="item_pedido_total"/>
    <result property="desconto" column="item_pedido_desconto"/>
    <result property="valorResgatado" column="item_pedido_valor_resgatado"/>
    <result property="observacao" column="item_pedido_observacao"/>

    <association property="pedido" resultMap="pedido.pedidoRM"/>

    <association property="produto" resultMap="produto.produtoResultMap"/>
    <association property="produtoTamanho" resultMap="produto.produtoTamanhoRM"/>

    <association property="adicionaisEscolhaSimples" resultMap="valorDeAdicionaisEscolhaSimples.valorDeAdicionaisEscolhaSimplesRM"/>
    <association property="adicionaisMultiplaEscolha" resultMap="valorDeAdicionaisMultiplaEscolha.valorDeAdicionaisMultiplaEscolhaRM"/>

    <collection  property="sabores"   resultMap="itemPedido.itemPedidoSaborRM"/>

  </resultMap>

  <resultMap id="itemPedidoSaborRM" type="ItemPedido">
    <id property="id" column="item_pedido_sabor_id"/>

    <result property="valor" column="item_pedido_sabor_valor"/>
    <result property="qtde" column="item_pedido_sabor_qtde"/>
    <result property="total" column="item_pedido_sabor_total"/>
    <result property="descricao" column="item_pedido_sabor_descricao"/>
    <result property="desconto" column="item_pedido_sabor_desconto"/>

    <association property="produto" columnPrefix="sabor_" resultMap="produto.produtoResultMap"/>
    <association property="produtoTamanho"  columnPrefix="sabor_"  resultMap="produto.produtoTamanhoRM"/>

  </resultMap>

  <update id="removaTodosDoPedido">
    delete valor_item
        from item_pedido item join valor_de_adicionais_escolha_simples valor_item on valor_item.item_id = item.id
           where     item.pedido_id = #{id};

    delete valor_item
      from item_pedido item join valor_de_adicionais_multipla_escolha valor_item on valor_item.item_id = item.id
        where     item.pedido_id = #{id};

    delete from item_pedido where pedido_id = #{id} and item_do_sabor_id is not null;
    delete from item_pedido where pedido_id = #{id};
  </update>

  <update id="removaItemPedido">
    delete valor_item
      from item_pedido item join valor_de_adicionais_escolha_simples valor_item on valor_item.item_id = item.id
         where     item.id = #{id};

    delete valor_item
     from item_pedido item join valor_de_adicionais_multipla_escolha valor_item on valor_item.item_id = item.id
       where     item.id = #{id};

    delete from item_pedido where pedido_id = #{idPedido} and item_do_sabor_id  = #{id};

    delete from item_pedido where id = #{id};
  </update>


</mapper>
