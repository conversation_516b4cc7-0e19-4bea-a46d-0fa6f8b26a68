<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="integracaodelivery">
  <resultMap id="integracaodeliveryResultMap" type="IntegracaoDelivery">
    <id property="id" column="integracao_delivery_id"/>

    <result property="token" column="integracao_delivery_token" />
    <result property="rede" column="integracao_delivery_rede" />
    <result property="loja" column="integracao_delivery_loja" />
    <result property="sistema" column="integracao_delivery_sistema" />
    <result property="unidadeChina" column="integracao_delivery_unidade_china" />
    <result property="unidadeGendai" column="integracao_delivery_unidade_gendai" />
    <result property="naoSincronizarGlobal" column="integracao_delivery_nao_sincronizar_global" />
    <result property="naoSincronizarDisponiveis" column="integracao_delivery_nao_sincronizar_disponiveis" />
    <result property="ultimaSincronizacaoDisponivel" column="integracao_delivery_ultima_sincronizacao_disponivel" />
    <result property="ultimaSincronizacaoProdutos" column="integracao_delivery_ultima_sincronizacao_produtos" />
    <result property="ultimaSincronizacaoPrecos" column="integracao_delivery_ultima_sincronizacao_precos" />
    <result property="ultimaSincronizacaoEstoque" column="integracao_delivery_ultima_sincronizacao_estoque" />
    <result property="validadeToken" column="integracao_delivery_validade_token" />

    <result property="ativa" column="integracao_delivery_ativa"/>
    <result property="data" column="integracao_delivery_data"/>

    <result property="configuracoesEspecificasJson" column="integracao_delivery_configuracoes_especificas" />

    <collection  property="formasDePagamento"   resultMap="formaIntegrada.formaDePagamentoIntegradaRM"/>

    <discriminator javaType="String" column="integracao_delivery_sistema" >
      <case value="ecletica" resultType="IntegracaoEcleticaERP"></case>
      <case value="foodydelivery" resultType="IntegracaoFoodyDelivery"></case>
      <case value="clickentregas" resultType="IntegracaoClickEntregas"></case>
      <case value="tiny" resultType="IntegracaoTinyERP"></case>
      <case value="RPInfo" resultType="IntegracaoRPInfo"></case>
      <case value="menew" resultType="IntegracaoMenew"></case>
      <case value="bluesoft" resultType="IntegracaoBlueSoftERP"></case>
      <case value="teknisa" resultType="IntegracaoTeknisaERP"></case>
      <case value="gcom" resultType="IntegracaoGcomERP"></case>
      <case value="saipos" resultType="IntegracaoSaiposERP"></case>
      <case value="totvs" resultType="IntegracaoTotvsFood"></case>
    </discriminator>

  </resultMap>
  <resultMap id="integracaoFoodydeliveryResultMap" type="IntegracaoFoodyDelivery">
    <id property="id" column="integracao_foodydelivery_id"/>

    <result property="token" column="integracao_foodydelivery_token" />
    <result property="configuracoesEspecificasJson" column="integracao_foodydelivery_configuracoes_especificas" />
  </resultMap>

  <insert id="insira" parameterType="map"    >
    insert into integracao_delivery (token, sistema, data, rede, loja, unidade_china,unidade_gendai, ativa, empresa_id, validade_token,
                                      configuracoes_especificas, nao_sincronizar_global, nao_sincronizar_disponiveis)
        values (#{token}, #{sistema}, #{data}, #{rede}, #{loja}, #{unidadeChina}, #{unidadeGendai}, #{ativa}, #{empresa.id},
               #{validadeToken}, #{configuracoesEspecificasJson}, #{naoSincronizarGlobal}, #{naoSincronizarDisponiveis});
  </insert>

  <update id="atualize">
    update   integracao_delivery
        set  token = #{token}, sistema = #{sistema},
             rede = #{rede}, loja =  #{loja}, unidade_china  = #{unidadeChina}, unidade_gendai = #{unidadeGendai},
    configuracoes_especificas = #{configuracoesEspecificasJson},
    nao_sincronizar_global = #{naoSincronizarGlobal}, nao_sincronizar_disponiveis = #{naoSincronizarDisponiveis}
       where integracao_delivery.id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeUltimaSincronizacao">
    update   integracao_delivery
    set ultima_sincronizacao_disponivel = #{ultimaSincronizacaoDisponivel}
    where integracao_delivery.id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeUltimaSincronizacaoProdutos">
    update   integracao_delivery
    set ultima_sincronizacao_produtos = #{ultimaSincronizacaoProdutos}
    where integracao_delivery.id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeUltimaSincronizacaoPrecos">
    update   integracao_delivery
    set ultima_sincronizacao_precos = #{ultimaSincronizacaoPrecos}
    where integracao_delivery.id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeUltimaSincronizacaoEstoque">
    update   integracao_delivery
    set ultima_sincronizacao_estoque = #{ultimaSincronizacaoEstoque}
    where integracao_delivery.id = #{id} and empresa_id = #{empresa.id}
  </update>


  <update id="atualizeTokenComValidade">
    update  integracao_delivery
    set token = #{token}, validade_token = #{validade}
    where empresa_id = #{empresa.id} and id = #{id}
  </update>

  <update id="desative">
    update   integracao_delivery
    set ativa = false
    where integracao_delivery.id = #{id} and empresa_id = #{idEmpresa}
  </update>


  <update id="remova">
    delete integracao_delivery from   integracao_delivery join empresa on empresa.id = empresa_id
        where integracao_delivery.id = #{id} ;
  </update>


</mapper>
