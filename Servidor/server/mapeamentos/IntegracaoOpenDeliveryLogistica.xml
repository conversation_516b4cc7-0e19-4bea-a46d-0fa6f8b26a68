<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="opendeliverylogistica">
  <resultMap id="opendeliverylogisticaRM" type="IntegracaoOpendeliveryLogistica">
    <id property="id" column="opend_logistica_id"/>

    <result property="authUrl" column="opend_logistica_auth_url"/>
    <result property="baseUrl" column="opend_logistica_base_url"/>
    <result property="clientId" column="opend_logistica_client_id"/>
    <result property="clientSecret" column="opend_logistica_client_secret"/>
    <result property="appid" column="opend_logistica_appid"/>
    <result property="merchantId" column="opend_logistica_merchant_id"/>
    <result property="token" column="opend_logistica_token"/>
    <result property="tokenDataExpiracao" column="opend_logistica_token_data_expiracao"/>
    <result property="retornarNaLoja" column="opend_logistica_retornar_na_loja"/>
    <result property="notificarRetirada" column="opend_logistica_notificar_retirada"/>
    <result property="notificarConclusao" column="opend_logistica_notificar_conclusao"/>
    <result property="veiculoPadrao" column="opend_logistica_veiculo_padrao"/>
    <result property="tempoLimiteRetirada" column="opend_logistica_tempo_limite_retirada"/>
    <result property="tempoLimiteEntrega" column="opend_logistica_tempo_limite_entrega"/>
    <result property="ativa" column="opend_logistica_ativa"/>
    <result property="automatico" column="opend_logistica_automatico"/>
    <result property="naoEnviarLocalizacao" column="opend_logistica_nao_enviar_localizacao"/>

  </resultMap>

  <update id="atualize">
    update integracao_opendelivery_logistica
    set auth_url = #{authUrl}, base_url = #{baseUrl},
      client_id = #{clientId}, client_secret = #{clientSecret},
      appid = #{appid}, token = #{token}, token_data_expiracao = #{tokenDataExpiracao},
      notificar_retirada = #{notificarRetirada}, notificar_conclusao = #{notificarConclusao},
      retornar_na_loja = #{retornarNaLoja}, veiculo_padrao = #{veiculoPadrao}, merchant_id = #{merchantId},
      tempo_limite_retirada = #{tempoLimiteRetirada}, tempo_limite_entrega = #{tempoLimiteEntrega},
      automatico = #{automatico}, nao_enviar_localizacao = #{naoEnviarLocalizacao}
    where id = #{id}   and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeToken">
    update integracao_opendelivery_logistica
       set token = #{token}, token_data_expiracao = #{tokenDataExpiracao}
    where id = #{id}   and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeAtiva">
    update integracao_opendelivery_logistica
    set  ativa = #{ativa}
    where id = #{id}   and empresa_id = #{empresa.id};
  </update>

</mapper>
