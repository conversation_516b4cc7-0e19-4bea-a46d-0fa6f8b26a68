<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="transacao">
  <resultMap id="transacaoRM" type = "Transacao">
    <id property="id" column="transacao_id"/>


    <result property="descricao" column="transacao_descricao"/>
    <result property="horario" column="transacao_horario"/>
    <result property="tipo" column="transacao_tipo"/>
    <result property="valorEmCentavos" column="transacao_valor_em_centavos"/>
    <result property="estornavel" column="transacao_estornavel"/>

    <association property="formaDePagamento" columnPrefix="transacao_" column="forma_de_pagamento_id" resultMap="formaDePagamento.formaDePagamentoRM"/>
    <association property="caixa" column="caixa_id" resultMap="caixa.caixaRM"/>
    <association property="operador" column="operador_id" resultMap="usuario.operadorResultMap"/>
    <association property="pedido" column="pedido_id" resultMap="pedido.pedidoRM"/>
    <association property="comanda" columnPrefix="trs_" column="comanda_id" resultMap="comanda.comandaRM"/>

  </resultMap>

  <insert id="insira" parameterType="map">
    INSERT INTO transacao (descricao, caixa_id, operador_id, horario, valor_em_centavos, tipo, pedido_id, estornavel, forma_de_pagamento_id, comanda_id)
    VALUES (#{descricao}, #{caixa.id}, #{operador.id}, #{horario}, #{valorEmCentavos}, #{tipo}, #{pedido.id}, #{estornavel}, #{formaDePagamento.id}, #{comanda.id})
  </insert>

  <insert id="insiraLista" parameterType="map">
    insert into transacao (descricao, horario, valor_em_centavos, tipo, pedido_id, estornavel, forma_de_pagamento_id, caixa_id, operador_id, comanda_id)
    values
    <foreach item="transacao" collection="dados" open="" separator="," close="">
      (#{transacao.descricao}, #{transacao.horario}, #{transacao.valorEmCentavos}, #{transacao.tipo}, #{transacao.pedido.id}, #{transacao.estornavel},
      #{transacao.formaDePagamento.id}, #{transacao.caixa.id}, #{transacao.operador.id}, #{transacao.comanda.id})
    </foreach>
  </insert>
</mapper>
