<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="itemCombustivel" >
  <resultMap id="itemCombustivelRM" type="DetalhamentoItemCombustivel">
    <id property="id" column="item_combustivel_id"/>
    <result property="codigoANP" column="item_combustivel_codigo_anp"/>
    <result property="codif" column="item_combustivel_codif"/>
    <result property="qTemp" column="item_combustivel_q_temp"/>
    <result property="ufConsumo" column="item_combustivel_uf_consumo"/>
    <result property="descricaoANP" column="item_combustivel_descricao_anp"/>
    <result property="percentualGLP" column="item_combustivel_percentual_glp"/>
    <result property="percentualGNnacional" column="item_combustivel_percentual_gn_nacional"/>
    <result property="percentualGNimportado" column="item_combustivel_percentual_gn_importado"/>
    <result property="valorDePartida" column="item_combustivel_valor_de_partida"/>
    <result property="percentualMisturaBio" column="item_combustivel_percentual_mistura_bio"/>

    <association property="cide" resultMap="cide.cideRM"/>
  </resultMap>

  <create id="crieTabela" parameterType="map">
    create table if not exists item_combustivel (
      id bigint(20) primary key,
      codigo_anp varchar(20),
      codif varchar(20),
      q_temp decimal(10,2),
      uf_consumo varchar(2),
      descricao_anp varchar(100),
      percentual_glp decimal(10,2),
      percentual_gn_nacional decimal(10,2),
      percentual_gn_importado decimal(10,2),
      valor_de_partida decimal(10,2),
      percentual_mistura_bio decimal(10,2),
      cide_id bigint(20),

      foreign key (cide_id) references cide(id)

    );
  </create>
</mapper>
