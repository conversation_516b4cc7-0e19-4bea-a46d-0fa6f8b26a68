<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="icmsSimples" >
  <resultMap id="icmsSimplesRM" type="ICMSimples">
    <id property="id" column="icms_simples_id"/>
    <result property="origem" column="icms_simples_origem"/>
    <result property="codigoSitOp" column="icms_simples_codigo_sit_op"/>
    <result property="percentualCredito" column="icms_simples_percentual_credito"/>
    <result property="valorCredito" column="icms_simples_valor_credito"/>
    <result property="modBaseDeCalculoICMSSt" column="icms_simples_mod_base_de_calculo_icms_st"/>
    <result property="percentualMargemValorICMSSt" column="icms_simples_percentual_margem_valor_icms_st"/>
    <result property="percentualReducaoBaseDeCalculoICMSSt" column="icms_simples_percentual_reducao_base_de_calculo_icms_st"/>
    <result property="valorBaseDeCalculoICMSSt" column="icms_simples_valor_base_de_calculo_icms_st"/>
    <result property="aliquotaICMSSt" column="icms_simples_aliquota_icms_st"/>
    <result property="valorICMSSt" column="icms_simples_valor_icms_st"/>
    <result property="modbasecalcIcms" column="icms_simples_modbasecalc_icms"/>
    <result property="valorBaseDeCalculoICMS" column="icms_simples_valor_base_de_calculo_icms"/>
    <result property="aliquotaICMS" column="icms_simples_aliquota_icms"/>
    <result property="aliqutoAplicavelCalcCred" column="icms_simples_aliquto_aplicavel_calc_cred"/>
    <result property="percentualReducaoBaseDeCalculoICMS" column="icms_simples_percentual_reducao_base_de_calculo_icms"/>
    <result property="valorICMS" column="icms_simples_valor_icms"/>
    <result property="valorBCICMSRet" column="icms_simples_valor_bc_icms_ret"/>
    <result property="valorICMSRet" column="icms_simles_valor_icms_ret"/>
    <result property="valorBaseDeCalculoFCPST" column="icms_simples_valor_base_de_calculo_fcpst"/>
    <result property="percentualFCPST" column="icms_simples_percentual_fcpst"/>
    <result property="valorFCPST" column="icms_simples_valor_fcpst"/>
    <result property="percentualConsumidorST" column="icms_simples_percentual_consumidor_st"/>
    <result property="valorBCFCPSTRet" column="icms_simples_valor_bc_fcpst_ret"/>
    <result property="percentualFCPSTRet" column="icms_simples_percentual_fcpst_ret"/>
    <result property="valorFCPSTRet" column="icms_simples_valor_fcpst_ret"/>
    <result property="percentualReducaoBCEfetiva" column="icms_simples_percentual_reducao_bc_efetiva"/>
    <result property="valorBCEfetiva" column="icms_simples_valor_bc_efetiva"/>
    <result property="percentualICMSEfetivo" column="icms_simples_percentual_icms_efetivo"/>
    <result property="valorICMSEfetivo" column="icms_simples_valor_icms_efetivo"/>
    <result property="valorICMSSubstituto" column="icms_simples_valor_icms_substituto"/>
  </resultMap>
  <create id="crieTabela" parameterType="map">
    create table if not exists icms_simples (
      id bigint(20) primary key,
      origem int,
      codigo_sit_op int,
      percentual_credito decimal(10,2),
      valor_credito decimal(10,2),
      mod_base_de_calculo_icms_st int,
      percentual_margem_valor_icms_st decimal(10,2),
      percentual_reducao_base_de_calculo_icms_st decimal(10,2),
      valor_base_de_calculo_icms_st decimal(10,2),
      aliquota_icms_st decimal(10,2),
      valor_icms_st decimal(10,2),
      modbasecalc_icms int,
      valor_base_de_calculo_icms decimal(10,2),
      aliquota_icms decimal(10,2),
      aliquto_aplicavel_calc_cred decimal(10,2),
      percentual_reducao_base_de_calculo_icms decimal(10,2),
      valor_icms decimal(10,2),
      valor_bc_icms_ret decimal(10,2),
      valor_icms_ret decimal(10,2),
      valor_base_de_calculo_fcpst decimal(10,2),
      percentual_fcpst decimal(10,2),
      valor_fcpst decimal(10,2),
      percentual_consumidor_st decimal(10,2),
      valor_bc_fcpst_ret decimal(10,2),
      percentual_fcpst_ret decimal(10,2),
      valor_fcpst_ret decimal(10,2),
      percentual_reducao_bc_efetiva decimal(10,2),
      valor_bc_efetiva decimal(10,2),
      percentual_icms_efetivo decimal(10,2),
      valor_icms_efetivo decimal(10,2),
      valor_icms_substituto decimal(10,2)
    );
  </create>
</mapper>
