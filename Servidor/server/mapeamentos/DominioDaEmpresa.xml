<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="dominioDaEmpresa">

  <resultMap id="dominioDaEmpresaRM" type="DominioDaEmpresa">
    <id property="id" column="dominio_da_empresa_id" />

    <result property="hostname" column="dominio_da_empresa_hostname" />
    <result property="urlCardapio" column="dominio_da_empresa_url_cardapio" />
    <result property="urlCatalogo" column="dominio_da_empresa_url_catalogo"/>
  </resultMap>


  <select id="selecione" parameterType="map" resultMap="dominioDaEmpresa.dominioDaEmpresaRM" prefix="true">
    select * from dominio_da_empresa
    where empresa_id = #{idEmpresa}
  </select>

  <delete id="remova" parameterType="map">
    delete from dominio_da_empresa where empresa_id = #{id};
  </delete>
</mapper>
