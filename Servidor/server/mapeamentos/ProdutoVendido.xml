<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="produtoVendido">
  <resultMap id="produtoVendidoResultMap" type="ProdutoVendido">
    <id property="id" column="produto_vendido_id"/>

  </resultMap>

  <select id="listeResumo" resultMap="pedido.dtoResumoVendaAdicionaisRM">
     select pv.*, pv.quantidade qtde, produto_id produto
        from produto_vendido pv join pedido on pedido.id  = pv.pedido_id
                                   join empresa e on e.id = pv.empresa_id and e.id = #{idEmpresa}
    where pv.empresa_id = #{idEmpresa} and pv.tipo  = #{tipo}

    <choose>
          <when test="pagos != null"> and pago is true </when>
          <when test="naoPagos != null"> and (pago is not true and 4 >= pedido.status)</when>
          <when test="pagosENaoPagos != null"> and  4 >= pedido.status </when>
          <otherwise>  and false</otherwise>
        </choose>
        <choose>
          <when test="soMesas"> and  pedido.mesa_id is not null </when>
          <when test="comMesas">  </when>
          <when test="delivery">  and pedido.mesa_id is null</when>
        </choose>
        <if test="idProduto">   and  pv.produto_id = #{idProduto}  </if>
        <if test="darkPrincipal">  and pedido.multipedido_id is null   </if>
        <if test="dataInicio">  and if(horario_entrega_agendada is null, horario, horario_entrega_agendada) >= #{dataInicio}</if>
        <if test="dataFim">   and if(horario_entrega_agendada is null, horario, horario_entrega_agendada) &lt;=  #{dataFim}</if>

  </select>

  <update id="remova" parameterType="map">
     delete from produto_vendido
        where pedido_id = #{idPedido} and empresa_id = #{idEmpresa}
              <if test="idItemPedido">
                   and item_pedido_id = #{idItemPedido}
              </if>
  </update>
</mapper>
