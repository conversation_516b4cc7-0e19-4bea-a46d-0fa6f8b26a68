<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="grupoDeLojas">
  <resultMap id="grupoDeLojasRM" type="GrupoDeLojas">
    <id property="id" column="grupo_de_lojas_id" />

    <result property="hostname" column="grupo_de_lojas_hostname" />
    <result property="nome" column="grupo_de_lojas_nome" />
    <result property="descricao" column="grupo_de_lojas_descricao" />

    <result property="capa" column="grupo_de_lojas_capa" />
    <result property="logo" column="grupo_de_lojas_logo" />
    <result property="fundo" column="grupo_de_lojas_fundo" />
    <result property="pixelFacebook" column="grupo_de_lojas_pixel_facebook" />

    <result property="favicon" column="grupo_de_lojas_favicon" />
    <result property="gtag" column="grupo_de_lojas_gtag" />

    <result property="msgConversaWhatsapp" column="grupo_de_lojas_msg_conversa_whatsapp" />

    <result property="codigoGtm" column="grupo_de_lojas_codigo_gtm" />
    <result property="analytics" column="grupo_de_lojas_analytics" />
    <result property="multipedido" column="grupo_de_lojas_multipedido" />

    <result property="telaMultiLoja" column="grupo_de_lojas_tela_multi_loja" />

    <result property="paginaDirecionarPara" column="grupo_de_lojas_pagina_direcionar_para" />
    <result property="encontrarLojaMaisProxima" column="grupo_de_lojas_encontrar_loja_mais_proxima" />

    <result property="naoExibirBuscaDeLojasPorCep" column="grupo_de_lojas_nao_exibir_busca_de_lojas_por_cep" />
    <result property="naoListarLojas" column="grupo_de_lojas_nao_listar_lojas" />


    <association property="empresaPrincipal" resultMap="empresaPrincipalRM"/>

    <collection  property="empresas" resultMap="empresaDoGrupo.empresaDoGrupoRM"/>
    <collection  property="deParas" resultMap="formaPagamentoGrupoDePara.formaPagamentoDeParaDoGrupoRM"/>
  </resultMap>

  <resultMap id="grupoDaEmpresaRM" type="GrupoDeLojas">
    <id property="id" column="grupo_de_lojas_id" />

    <result property="nome" column="grupo_de_lojas_nome" />

  </resultMap>
  <resultMap id="empresaPrincipalRM" type="DTOObjetoComNome">
    <id property="id" column="grupo_de_lojas_empresa_principal_id"/>
  </resultMap>


  <select id="selecione" parameterType="map" resultMap="grupoDeLojasRM" prefix="true">
    select * from grupo_de_lojas
              left join grupo_de_lojas_empresa on(grupo_de_lojas.id = grupo_de_lojas_empresa.grupo_de_lojas_id)
              left join empresa on(grupo_de_lojas_empresa.empresa_id = empresa.id)
              left join dominio_da_empresa on dominio_da_empresa.empresa_id = empresa.id
              left join forma_pagamento_grupo_de_para on forma_pagamento_grupo_de_para.grupo_de_lojas_id = grupo_de_lojas.id
              left join forma_de_pagamento  forma_de_pagamento_de_para on forma_de_pagamento_de_para.id = forma_pagamento_grupo_de_para.para_id
              left join catalogo on empresa.catalogo_id = catalogo.id
    <choose>
      <when test="hostname != null">
        where grupo_de_lojas.hostname = #{hostname} or grupo_de_lojas.hostname = #{hostname2}
      </when>

      <when test="nome != null">
        where grupo_de_lojas.nome = #{nome}
      </when>

      <when test="idEmpresa != null">
        where empresa.id = #{idEmpresa}
      </when>

      <when test="id != null">
        where grupo_de_lojas.id = #{id}
      </when>
    </choose>
  </select>

  <select id="selecioneEmpresas" parameterType="map" resultMap="empresaDoGrupo.empresaDoGrupoRM" prefix="true">
    select *
        from grupo_de_lojas
      inner join grupo_de_lojas_empresa on(grupo_de_lojas.id = grupo_de_lojas_empresa.grupo_de_lojas_id)
      inner join empresa on(grupo_de_lojas_empresa.empresa_id = empresa.id)
      left join forma_de_pagamento on forma_de_pagamento.empresa_id = empresa.id and forma_de_pagamento.removida is not true
    <choose>
      <when test="id != null">
        where grupo_de_lojas.id = #{id}
      </when>
    </choose>
  </select>

  <update id="atualize">
    update grupo_de_lojas
    set nome = #{nome}, hostname = #{hostname}, descricao = #{descricao},
    capa= #{capa}, logo = #{logo}, fundo = #{fundo},
    nao_exibir_busca_de_lojas_por_cep = #{naoExibirBuscaDeLojasPorCep},
    favicon = #{favicon},
    gtag = #{gtag},
    pixel_facebook = #{pixelFacebook},
    codigo_gtm = #{codigoGtm},
    encontrar_loja_mais_proxima = #{encontrarLojaMaisProxima},
    pagina_direcionar_para = #{paginaDirecionarPara},
    msg_conversa_whatsapp = #{msgConversaWhatsapp},
    nao_listar_lojas = #{naoListarLojas},
    analytics = #{analytics},
    tela_multi_loja = #{telaMultiLoja}, multipedido = #{multipedido}
    where id = #{id}
  </update>

  <delete id="remova" parameterType="map">
    delete from grupo_de_lojas where empresa_id = #{id};
  </delete>

  <insert id="insiraEmpresasGrupo">
    INSERT INTO grupo_de_lojas_empresa
    (grupo_de_lojas_id, empresa_id)
    VALUES( #{idGrupo}, #{idEmpresa})
  </insert>

  <insert id="removaEmpresaGrupo">
    DELETE FROM grupo_de_lojas_empresa
    where
      grupo_de_lojas_id = #{idGrupo}
      and empresa_id = #{idEmpresa};
  </insert>

  <update id="atualizeEmpresasEmpPrincipal" parameterType="map">
    update empresa set  empresa_principal_id = null where id = #{empresaPrincipal.id};
    update empresa set
    empresa_principal_id = #{empresaPrincipal.id}
    where id in(select empresa_id from grupo_de_lojas_empresa where grupo_de_lojas_id = #{grupo.id})
    and id != #{empresaPrincipal.id};
  </update>

  <update id="atualizeEmpresaPrincipalGrupo" parameterType="map">
    update grupo_de_lojas set empresa_principal_id = #{empresaPrincipal.id}
    where id = #{grupo.id};
  </update>
</mapper>
