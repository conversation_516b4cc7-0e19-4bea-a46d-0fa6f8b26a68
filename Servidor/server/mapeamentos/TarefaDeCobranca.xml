<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tarefa_de_cobranca">

    <resultMap id="tarefaDeCobrancaMap" type="TarefaDeCobranca">
        <id property="id" column="tarefa_de_cobranca_id"/>
        <result property="dataCobranca" column="tarefa_de_cobranca_data_cobranca"/>
        <result property="status" column="tarefa_de_cobranca_status"/>
        <result property="tentativa" column="tarefa_de_cobranca_tentativa"/>
        <result property="dataCriacao" column="tarefa_de_cobranca_data_criacao"/>
        <result property="dataAtualizacao" column="tarefa_de_cobranca_data_atualizacao"/>
        <result property="mensagem" column="tarefa_de_cobranca_mensagem"/>

        <association property="fatura"   resultMap="fatura.faturaRM"/>

    </resultMap>

    <select id="selecione" parameterType="map" resultMap="tarefaDeCobrancaMap" prefix="true">
      SELECT * FROM tarefa_de_cobranca   INNER JOIN
                    fatura   ON fatura.id = tarefa_de_cobranca.fatura_id
      <choose>
        <when test="id != null">
          WHERE tc.id = #{id}
        </when>

        <when test="pendentes">
          WHERE  CURDATE() >= tarefa_de_cobranca.data_cobranca AND tarefa_de_cobranca.status = 1 and  2 > fatura.status
        </when>


        <when test="fatura_id != null">
          WHERE fatura_id = #{fatura_id}

          <if test="status != null">
            and tarefa_de_cobranca.status = #{status}
          </if>
        </when>

        <when test="status != null">
          WHERE tarefa_de_cobranca.status = #{status}
        </when>
      </choose>

      ORDER BY tarefa_de_cobranca.data_cobranca ASC, tarefa_de_cobranca.tentativa ASC

      <if test="total">
        limit #{inicio}, #{total}
      </if>

    </select>

    <update id="atualize" parameterType="map">
        UPDATE tarefa_de_cobranca SET
            status = #{status} ,
            data_atualizacao = #{dataAtualizacao},
            mensagem = #{mensagem}
        WHERE id = #{id}
    </update>

    <update id="atualizeCanceladasDaFatura" parameterType="map">
        UPDATE tarefa_de_cobranca SET
            status = 5,
            data_atualizacao = NOW(), mensagem = #{mensagem}
        WHERE    fatura_id = #{faturaId}     AND status IN (1, 2)
    </update>

    <update id="removaDaFatura" parameterType="map">
        delete from tarefa_de_cobranca where  fatura_id = #{faturaId} and data_cobranca > curdate()
    </update>
</mapper>
