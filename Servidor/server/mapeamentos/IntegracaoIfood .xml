<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="integracaoIfood">
  <resultMap id="integracaoIfoodRM" type="IntegracaoIfood">
    <id property="id" column="integracao_ifood_id"/>

    <result property="idLoja" column="integracao_ifood_id_loja"/>
    <result property="nomeLoja" column="integracao_ifood_nome_loja"/>
    <result property="token" column="integracao_ifood_token"/>
    <result property="refreshToken" column="integracao_ifood_refresh_token"/>
    <result property="codigoAutorizacao" column="integracao_ifood_codigo_autorizacao"/>
    <result property="verificadorCodigoAutorizacao" column="integracao_ifood_verificador_codigo_autorizacao"/>
    <result property="validadeToken" column="integracao_ifood_validade_token"/>
    <result property="desativado" column="integracao_ifood_desativado"/>
    <result property="orderApi" column="integracao_ifood_order_api"/>
    <result property="shippingApi" column="integracao_ifood_shipping_api"/>
    <result property="confirmarCotacao" column="integracao_ifood_confirmar_cotacao"/>
    <result property="solicitarAutomatico" column="integracao_ifood_solicitar_automatico"/>
    <result property="dataAtivacao" column="integracao_ifood_data_ativacao"/>

  </resultMap>


  <select id="existe" parameterType="map" resultType="int">
    select count(*) total
      from integracao_ifood where id_loja = #{idLoja} and empresa_id != #{idEmpresa}
  </select>

  <update id="atualizeToken">
    update  integracao_ifood
    set token = #{token}, validade_token = #{validadeToken}, refresh_token = #{refreshToken}
    where empresa_id = #{empresa.id} and id = #{id}
  </update>

  <update id="atualizeAtiva">
    update  integracao_ifood
    set desativado = #{desativado}
        where empresa_id = #{empresa.id} and id = #{id}
  </update>


  <update id="atualizeApi">
    update  integracao_ifood
    set order_api = #{orderApi},
        shipping_api = #{shippingApi},
        confirmar_cotacao =  #{confirmarCotacao},
        solicitar_automatico =  #{solicitarAutomatico}
    where empresa_id = #{empresa.id} and id = #{id}
  </update>


  <update id="remova">
      delete from integracao_ifood   where empresa_id = #{empresa.id} and id = #{id}
  </update>

</mapper>
