<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="motivocancelamento">
  <resultMap id="motivocancelamentoRM" type="MotivoCancelamentoEmpresa">
    <id property="id" column="motivo_cancelamento_empresa_id"/>

    <result property="codigo" column="motivo_cancelamento_empresa_codigo"/>
    <result property="descricao" column="motivo_cancelamento_empresa_descricao"/>
    <result property="classificacao" column="motivo_cancelamento_empresa_classificacao"/>

  </resultMap>


  <select id="selecione" parameterType="map" resultMap="motivocancelamentoRM" prefix="true">
    select * from motivo_cancelamento_empresa order by codigo
  </select>

  <update id="removaTagsProduto">
    delete from cancelamento_empresa_motivo where  cancelamento_empresa_id = #{idCancelamento}
  </update>

  <insert id="insiraMotivos">
    INSERT INTO cancelamento_empresa_motivo
    (cancelamento_empresa_id, motivo_id)
    VALUES
    <foreach item="item" collection="dados" open="" separator="," close="">
      ( #{item.idCancelamento} ,   #{item.idMotivo}  )
    </foreach>
  </insert>


</mapper>

