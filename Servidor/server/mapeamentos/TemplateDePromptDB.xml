<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="templateDePromptDB">
  <resultMap id="templateDePromptDBRM" type="TemplateDePromptDB">
    <id property="id" column="template_de_prompt_db_id"/>

    <result property="nome" column="template_de_prompt_db_nome"/>
    <result property="descricao" column="template_de_prompt_db_descricao"/>
    <result property="template" column="template_de_prompt_db_template"/>
    <result property="tipo" column="template_de_prompt_db_tipo"/>

    <collection property="trechosDePrompt" resultMap="trechoDePrompt.trechoDePromptRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="templateDePromptDBRM" prefix="true">
    SELECT
    *, CASE
      WHEN trecho_de_prompt_empresa.id is NULL THEN trecho_de_prompt.ativo
      ELSE trecho_de_prompt_empresa.ativo
    end as ativo
    , CASE
      WHEN trecho_de_prompt_empresa.texto IS NULL THEN trecho_de_prompt.texto
      ELSE trecho_de_prompt_empresa.texto
    end as texto
    , CASE
      WHEN trecho_de_prompt_empresa.tipo IS NULL THEN trecho_de_prompt.tipo
      ELSE trecho_de_prompt_empresa.tipo
    end as tipo
    , CASE
      WHEN trecho_de_prompt_empresa.intent IS NULL THEN trecho_de_prompt.intent
      ELSE trecho_de_prompt_empresa.intent
    end as intent
    , CASE
      WHEN trecho_de_prompt_empresa.exemplos_json IS NULL THEN trecho_de_prompt.exemplos_json
      ELSE trecho_de_prompt_empresa.exemplos_json
    end as exemplos_json
    , CASE
    WHEN trecho_de_prompt_empresa.posicao IS NULL THEN trecho_de_prompt.posicao
    ELSE trecho_de_prompt_empresa.posicao
    end as posicao
    , CASE
      WHEN trecho_de_prompt_empresa.texto IS NULL THEN false
    ELSE (trecho_de_prompt_empresa.texto &lt;&gt; trecho_de_prompt.texto or trecho_de_prompt_empresa.exemplos_json &lt;&gt; trecho_de_prompt.exemplos_json)
    end as modificado
    FROM template_de_prompt_db
    join trecho_de_prompt on trecho_de_prompt.template_id = template_de_prompt_db.id
    left join (SELECT *
        FROM trecho_de_prompt_empresa
        WHERE empresa_id = #{idEmpresa}) as trecho_de_prompt_empresa ON
      (trecho_de_prompt.id = trecho_de_prompt_empresa.trecho_de_prompt_id)
    LEFT JOIN exemplo_prompt ON trecho_de_prompt.id = exemplo_prompt.trecho_de_prompt_id
    <if test="todos != null">
      WHERE (trecho_de_prompt.empresa_id = #{idEmpresa} or trecho_de_prompt.escopo = 'global')
    </if>
    <if test="id != null">
      WHERE template_de_prompt_db.id = #{id}
    </if>
    <if test="nome != null">
      WHERE template_de_prompt_db.nome = #{nome}
    </if>
    order by template_de_prompt_db.id, COALESCE(trecho_de_prompt_empresa.posicao, trecho_de_prompt.posicao) ASC;
  </select>

  <update id="atualize" parameterType="map">
    UPDATE template_de_prompt_db
    SET nome  = #{nome},
    descricao = #{descricao}
    WHERE id = #{id}
  </update>

  <update id="incrementeOrdem" parameterType="map">
    UPDATE trecho_de_prompt
    SET ordem = ordem + 1
    WHERE

      ordem &gt;= #{posicaoAnterior} AND ordem &lt; #{novaPosicao};
  </update>

  <update id="decrementeOrdem">
    UPDATE trecho_de_prompt
    SET ordem = ordem - 1
    WHERE ordem > #{posicaoAnterior} AND ordem &lt;= #{novaPosicao}
  </update>

  <update id="atualizeOrdem">
    UPDATE trecho_de_prompt
    SET ordem = #{novaPosicao}
    WHERE ordem = #{posicaoAnterior}
  </update>
</mapper>
