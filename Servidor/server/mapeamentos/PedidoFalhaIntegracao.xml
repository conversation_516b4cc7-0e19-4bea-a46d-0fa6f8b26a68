<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="pedidoFalhaIntegracao">



  <resultMap id="pedidoFalhaIntegracaoRM" type="PedidoFalhaIntegracao">
    <id property="id" column="pedido_falha_integracao_id" />
    <result property="sistema" column="pedido_falha_integracao_sistema" />
    <result property="payload" column="pedido_falha_integracao_payload" />
    <result property="erro" column="pedido_falha_integracao_erro" />
    <result property="horario" column="pedido_falha_integracao_horario" />
    <result property="lido" column="pedido_falha_integracao_lido" />

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="pedidoFalhaIntegracaoRM" prefix="true">
    select * from  pedido_falha_integracao
      where empresa_id  = #{idEmpresa}
        <if test="naoLidos">
          and  lido is not true and 10 > TIMESTAMPDIFF(MINUTE, horario, NOW()) ;
        </if>

        <if test="total">
           order by id desc limit #{inicio}, #{total}
        </if>

  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int" prefix="true">
      select count(*) from pedido_falha_integracao     where empresa_id  = #{idEmpresa};
  </select>

  <update id="atualizeLido">
    update pedido_falha_integracao set lido = true where id = #{id} and  empresa_id  = #{empresa.id};
  </update>
</mapper>
