<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mensagemPadraoBot">
  <resultMap id="mensagemPadraoBotRM" type="MensagemPadraoBot">
    <id property="id" column="mensagem_padrao_bot_id"/>

    <result property="nome" column="mensagem_padrao_bot_nome"/>

    <result property="descricao" column="mensagem_padrao_bot_descricao"/>

    <result property="template" column="mensagem_padrao_bot_template"/>
    <result property="mensagem" column="mensagem_padrao_bot_mensagem"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="mensagemPadraoBotRM" prefix="true">
    select * from mensagem_padrao_bot
    where
    1 = 1
    <if test="id != null">
      and mensagem_padrao_bot.id = #{id}
    </if>
    <if test="template != null">
      and mensagem_padrao_bot.telefone = #{template}
    </if>

    <choose>
      <when test="inicio != null">
        order by mensagem_padrao_bot.id limit #{inicio},#{total}
      </when>
    </choose>
  </select>
</mapper>
