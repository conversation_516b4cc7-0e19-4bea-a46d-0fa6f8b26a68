<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="produtoTemplate">
  <resultMap id="produtoTemplateRM" type="ProdutoTemplate">
      <id property="id" column="produto_template_id"/>

      <result property="nome" column="produto_template_nome"/>
      <result property="identificador" column="produto_template_identificador"/>
      <result property="tipoDeCobranca" column="produto_template_tipo_de_cobranca"/>
      <result property="tipo" column="produto_template_tipo" />
      <result property="montarPizza" column="produto_template_montar_pizza"/>
      <result property="vendaPorTamanho" column="produto_template_venda_por_tamanho"/>
      <result property="exibirPrecosTamanhos" column="produto_template_exibir_precos_tamanhos"/>
      <result property="taxaExtra" column="produto_template_taxa_extra"/>
      <result property="campoOrdenar" column="produto_template_campo_ordenar"/>
      <result property="nomeCategoriaMontar" column="produto_template_nome_categoria_montar"/>
      <result property="ocultarProdutos" column="produto_template_ocultar_produtos"/>

      <collection  property="tamanhos" resultMap="produtoTemplate.produtoTemplateTamanhoRM"/>
      <collection  property="adicionais" resultMap="produtoTemplate.produtoTemplateAdicionalRM"/>


  </resultMap>

  <resultMap id="produtoTemplateTamanhoRM" type="ProdutoTemplateTamanho">
    <id property="id" column="produto_template_tamanho_id"/>

    <result property="descricao" column="produto_template_tamanho_descricao"/>
    <result property="qtdePedacos" column="produto_template_tamanho_qtde_pedacos"/>
    <result property="qtdeSabores" column="produto_template_tamanho_qtde_sabores"/>
    <result property="disponivel" column="produto_template_tamanho_disponivel"/>
    <result property="pontosGanhos" column="produto_template_tamanho_pontos_ganhos"/>
    <result property="cashback" column="produto_template_tamanho_cashback"/>

    <result property="dadosJson" column="produto_template_dados_json"/>

    <collection  property="deParaTamanhoSabores" resultMap="pizzaTamanhoSaboresDePara.pizzaTamanhoSaboresDeParaRM"/>

  </resultMap>

  <resultMap id="produtoTemplateTamanhoComprarRM" type = "ProdutoTemplateTamanho">
    <id property="id" column="produto_template_tamanho_comprar_id"/>

    <result property="descricao" column="produto_template_tamanho_comprar_descricao"/>
  </resultMap>

  <resultMap id="produtoTemplateTamanhoGanharRM" type = "ProdutoTemplateTamanho">
    <id property="id" column="produto_template_tamanho_ganhar_id"/>

    <result property="descricao" column="produto_template_tamanho_ganhar_descricao"/>
  </resultMap>

  <resultMap id="produtoTemplateTamanhoDaOpcaoRM" type="ProdutoTemplateTamanho">
    <id property="id" column="produto_template_tamanho_opcao_id"/>

    <result property="descricao" column="produto_template_tamanho_opcao_descricao"/>
  </resultMap>

  <resultMap id="produtoTemplateAdicionalRM" type="ProdutoTemplateAdicional">
      <id property="id" column="produto_template_adicional_id"/>

      <result property="descricao" column="produto_template_adicional_descricao"/>
      <result property="tipo" column="produto_template_adicional_tipo"/>
      <result property="obrigatorio" column="produto_template_adicional_obrigatorio"/>
      <result property="disponivel" column="produto_template_adicional_disponivel"/>

      <result property="dadosJson" column="produto_template_adicional_dados_json"/>

      <collection  property="opcoes" resultMap="produtoTemplate.produtoTemplateOpcaoRM"/>

  </resultMap>

  <resultMap id="produtoTemplateOpcaoRM" type="ProdutoTemplateOpcao">
      <id property="id" column="produto_template_opcao_id"/>

      <result property="nome" column="produto_template_opcao_nome"/>
      <result property="valor" column="produto_template_opcao_valor"/>
      <result property="codigoPdv" column="produto_template_opcao_codigo_pdv"/>

      <result property="descricao" column="produto_template_opcao_descricao"/>
      <result property="disponivel" column="produto_template_opcao_disponivel"/>
      <result property="semborda" column="produto_template_opcao_semborda"/>

      <association property="tamanho" resultMap="produtoTemplate.produtoTemplateTamanhoDaOpcaoRM"/>
  </resultMap>

  <resultMap id="opcaoProdutoTamanhoRM" type="ProdutoTemplateTamanho">
    <id property="id" column="opcao_produto_tamanho_id"/>

    <result property ="idTamanho" column="opcao_produto_tamanho_produto_template_tamanho_id"/>

  </resultMap>



  <select id="selecione" parameterType="map" resultMap="produtoTemplateRM" prefix="true">
     select *
      from  produto_template left join produto_template_tamanho on produto_template.id = produto_template_tamanho.produto_template_id
                             left join produto_template_adicional on produto_template.id = produto_template_adicional.produto_template_id
                             left join produto_template_opcao on  produto_template_adicional.id = produto_template_opcao.produto_template_adicional_id and produto_template_opcao.removido is not true
                             left join produto_template_tamanho produto_template_tamanho_opcao on produto_template_tamanho_opcao.id = produto_template_opcao.tamanho_id
                             left join pizza_tamanho_sabores_ecletica on produto_template_tamanho_id = produto_template_tamanho.id
        where
          <choose>
            <when test="id != null">
              produto_template.id = #{id}
            </when>
            <otherwise>
              produto_template.catalogo_id = #{idCatalogo}
            </otherwise>
          </choose>
            and produto_template.removido is not true

            <if test="tipo">
              and produto_template.tipo = #{tipo}
            </if>

            <if test="ativo">
              and produto_template.ativo is true
            </if>

            <if test="usados">
              and exists (select 1 from produto where removido is not true  and template_id = produto_template.id)
            </if>

  </select>


  <select id="existe" parameterType="map" resultType="int">
    select count(*) total
        from produto where removido is not true and catalogo_id = #{catalogo.id} and template_id = #{id};
  </select>

  <update id="atualize">
    update produto_template set ativo = #{ativo}, nome = #{nome}, identificador = #{identificador},
           montar_pizza =  #{montarPizza}, venda_por_tamanho =  #{vendaPorTamanho},
            nome_categoria_montar = #{nomeCategoriaMontar},    ocultar_produtos = #{ocultarProdutos},
             exibir_precos_tamanhos = #{exibirPrecosTamanhos}
      where id = #{id}
  </update>


  <update id="remova">
    update produto_template join catalogo on catalogo.id = catalogo_id
        set removido = true   where produto_template.id = #{id}
  </update>

  <update id="atualizeTipoDeCobranca">
    update produto_template set
        tipo_de_cobranca = #{tipoDeCobranca}, taxa_extra = #{taxaExtra}, campo_ordenar = #{campoOrdenar}
        where id = #{id}
  </update>

  <update id="atualizePontosFidelidade">
    update produto_template_tamanho set
    pontos_ganhos = #{pontosGanhos}, cashback = #{cashback}
    where id = #{id}
  </update>


  <update id="atualizeIdentificador">
    update produto_template set  identificador = #{identificador}  where id = #{id}
  </update>

  <update id="atualizeTamanho">
    update produto_template_tamanho
            set descricao =  #{descricao}, disponivel  = #{disponivel},
                qtde_pedacos =  #{qtdePedacos},  qtde_sabores = #{qtdeSabores}
             where id = #{id}
  </update>

  <update id="atualizeOpcao">
     update  produto_template_opcao
          set nome = #{nome}, descricao = #{descricao}, disponivel = #{disponivel}, valor = #{valor},
                tamanho_id = #{tamanho.id}, codigo_pdv = #{codigoPdv}, semborda = #{semborda}
              where id = #{id};

    update opcao_adicional_produto set nome = #{nome}, valor = #{valor} , codigo_pdv = #{codigoPdv}
        where produto_template_opcao_id = #{id};
  </update>

  <update id="removaOpcaoTemplate">
    update  produto_template_opcao left join opcao_adicional_produto on produto_template_opcao_id =  produto_template_opcao.id
      set produto_template_opcao.removido = true, opcao_adicional_produto.excluido = true  where produto_template_opcao.id = #{id};
  </update>

  <update id="atualizeDisponibilidadeOpcao">
    update  produto_template_opcao
      set  disponivel = #{disponivel} where id = #{id};
  </update>

  <update id="atualizeDisponibilidadeOpcoes">
    update  produto_template_opcao
    set  disponivel = #{disponivel}
    where id in

    <foreach item="id" collection="ids" open="( " separator="," close=")">
      #{id}
    </foreach>

  </update>



  <insert id="insiraTamanho">

    insert into produto_template_tamanho(descricao, qtde_pedacos, qtde_sabores, produto_template_id,disponivel)
          values (#{descricao}, #{qtdePedacos},#{qtdeSabores},#{template.id},#{disponivel})

  </insert>

  <insert id="insiraAdicional">
    insert into    produto_template_adicional(descricao, tipo, produto_template_id,obrigatorio,disponivel )
        values (#{descricao},#{tipo}, #{template.id}, #{obrigatorio}, #{disponivel})

  </insert>

  <insert id="insiraOpcao">

    insert into produto_template_opcao(nome,descricao, valor, produto_template_adicional_id, disponivel , tamanho_id, codigo_pdv, semborda)
        values  (#{nome},#{descricao}, #{valor},  #{template.id}, #{disponivel} , #{tamanho.id}, #{codigoPdv}, #{semborda})


  </insert>

</mapper>
