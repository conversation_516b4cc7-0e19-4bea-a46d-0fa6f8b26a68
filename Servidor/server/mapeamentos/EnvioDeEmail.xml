<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="envioDeEmail">
  <resultMap id="envioEmailResultMap" type="email.EnvioDeEmail">
    <id property="id" column="envio_id" />

    <result property="dados" column="envio_dados" />
    <result property="guid" column="envio_guid" />
    <result property="destinatarioNome" column="envio_destinatario_nome" />
    <result property="destinatarioEmail" column="envio_destinatario_email" />

    <result property="tipo" column="envio_tipo" />
    <result property="enviado" column="envio_enviado" />
    <result property="horario" column="envio_horario" />
    <result property="horarioEnvio" column="envio_horario_envio" />
    <result property="idEnvio" column="envio_id_envio" />
    <result property="status" column="envio_status" />
    <result property="mensagemFalha" column="envio_mensagem_falha" />

    <association property="empresa"   resultMap="empresa.empresaRM"/>

    <discriminator javaType="String" column="envio_tipo" >

      <case value="RecuperacaoSenha" resultType="emails.EnvioRecuperacaoSenha"></case>
      <case value="PedidoEntregueTrendsfood" resultType="emails.EnvioPedidoEntregueTrendsFoood"></case>
      <case value="AutenticacaoLogin" resultType="emails.EnvioDeEmailAutenticacaoLogin"></case>


    </discriminator>

  </resultMap>


  <select id="selecione" parameterType="map" resultMap="envioEmailResultMap" prefix="true">
    select envio.*, empresa.*
      from envio_de_email envio join empresa on empresa.id = envio.empresa_id
        where empresa.id = #{idEmpresa}
          <choose>
            <when test="status != null">
              and envio.status = #{status}
            </when>
            <when test="guid != null">
              and envio.guid = #{guid}
            </when>

            <when test="naoEnviado != null">
              and envio.status != 'Enviado' and envio.status != 'Cancelado' order by envio.horario desc
            </when>
          </choose>

    <if test="tipo != null">
      and envio.tipo = #{tipo}
    </if>

    <if test="idContato != null">
      and envio.contato_id = #{idContato}
    </if>

    <if test="ultimo">
      order by envio.id  desc limit 1
    </if>

    <if test="inicio != null">
      order by envio.id desc limit #{inicio},#{total}
    </if>

  </select>


  <insert id="insira" parameterType="map" useGeneratedKeys="true" keyProperty="id">
    insert into envio_de_email(empresa_id,guid,usuario_id, contato_id, dados,tipo,enviado,horario,status,destinatario_email,destinatario_nome)
    values(#{empresa.id}, #{guid}, #{usuario.id}, #{contato.id},#{dados},#{tipo},#{enviado},#{horario},#{status},#{destinatarioEmail},#{destinatarioNome})
  </insert>

  <update id="atualize" parameterType="map" >
    update envio_de_email
    set    enviado = #{enviado},
          id_envio = #{idEnvio},
          status = #{status} ,
          horario_envio = #{horarioEnvio},
          mensagem_falha = #{mensagemFalha}
    where id = #{id} and empresa_id = #{empresa.id}
  </update>

  <update id="atualizeStatus">
    update envio_de_email set status = #{status} where id = #{id}
  </update>


</mapper>
