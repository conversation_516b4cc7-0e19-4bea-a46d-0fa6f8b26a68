<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="projetoTomtom">
  <resultMap id="projetoTomtomRM" type="tomtom.ProjetoTomtom">
    <id property="id" column="projeto_tomtom_id"/>

    <result property="data" column="projeto_tomtom_data"/>
    <result property="idTomtom" column="projeto_tomtom_id_tomtom"/>
    <result property="qtdeRegioes" column="projeto_tomtom_qtde_regioes"/>
    <result property="completou" column="projeto_tomtom_completou"/>

    <association property="empresa"  resultMap="empresa.empresaRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="projetoTomtomRM" prefix="true">
    select *
    from
    projeto_tomtom
    join empresa on(empresa.id = projeto_tomtom.empresa_id)
    where empresa_id = #{idEmpresa}
    <if test="id != null">
      and id = #{id}
    </if>
  </select>

  <update id="atualize"  parameterType="map">
    update projeto_tomtom set id_tomtom = #{idTomtom},
                              completou = #{completou},
                              qtde_regioes = #{qtdeRegioes}
    where id = #{id}
      and empresa_id = #{empresa.id};
  </update>

  <update id="remova"  parameterType="map">
    delete from projeto_tomtom
    where id = #{id}
    and empresa_id = #{empresa.id};
  </update>
</mapper>
