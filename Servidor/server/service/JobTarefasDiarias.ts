import {<PERSON><PERSON><PERSON><PERSON>} from "cron";
import * as async from "async";
import * as _ from "underscore";
import {MapeadorDeNotificacao} from "../mapeadores/MapeadorDeNotificacao";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
// @ts-ignore
import * as mybatis from 'mybatisnodejs';
import {Ambiente} from "./Ambiente";
import {NotificacaoService} from "./NotificacaoService";
import {MapeadorDePontuacaoRegistrada} from "../mapeadores/MapeadorDePontuacaoRegistrada";
import {CartaoService} from "./CartaoService";
import {Notificacao} from "../domain/Notificacao";
import {Cartao} from "../domain/Cartao";
import {NotificacaoMeioPagamentoService} from "./NotificacaoMeioPagamentoService";
import {NotificacaoPedidoService} from "./NotificacaoPedidoService";
import {ContratoService} from "./ContratoService";
import {EcleticaService} from "./EcleticaService";
import {TrendFoodsTarefa} from "../lib/integracao/TrendFoodsTarefa";
import {BlueSoftTarefa} from "../lib/integracao/BlueSoftTarefa";
import {EmpresaService} from "./EmpresaService";
import {IntegradorUtils} from "../utils/IntegradorUtils";
import {CriadorDeMensagemAvaliarPedidoService} from "./CriadorDeMensagemAvaliarPedidoService";
import {IFoodUtils} from "./integracoes/IFoodUtils";
import {IFoodService} from "./integracoes/IFoodService";
import {CriadorMensagemPagamentoPendenteService} from "./CriadorMensagemPagamentoPendenteService";
import {ProcessadorTarefasCobranca} from "./ProcessadorTarefasCobranca";

const Contexto = mybatis.Contexto;

export class JobTarefasDiarias{
  private static _instance: JobTarefasDiarias;

  constructor() {  }

  static get Instance() {
    return this._instance || this.crieInstancia();
  }


  static crieInstancia() {
    this._instance = new JobTarefasDiarias();

    return this._instance;
  }

  inicieTeste() {
    this.executeNoDomain('Job criar mensagens avaliar pedido', async ( contexto: any ) => {
      require('domain').active.contexto.idEmpresa = -1;
      await CriadorDeMensagemAvaliarPedidoService.executePendentes();

      contexto.release();
    })
  }
  inicieTarefas(){
    // tslint:disable-next-line:no-unused-expression
    new CronJob('00 30 16 * * *', () => {
      this.executeNoDomain('Job atualizar contatos em perigo', ( contexto: any ) => {
        this.atualizeContatosEmPerigo(contexto);
      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('15 */10 * * * *', () => {
      this.executeNoDomain('Job executar notificaçoes pagamentos', async ( contexto: any ) => {
        if(new Date().getHours() > 7){
          await NotificacaoMeioPagamentoService.executePendentes( )
        }

        contexto.release();
      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('*/15 * * * *', () => {
      this.executeNoDomain('Job criar mensagens avaliar pedido', async ( contexto: any ) => {
        require('domain').active.contexto.idEmpresa = -1;
        await CriadorDeMensagemAvaliarPedidoService.executePendentes();

        contexto.release();
      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('*/6 * * * *', () => {
      this.executeNoDomain('Job criar mensagens notificar pagamento pendente', async ( contexto: any ) => {
        require('domain').active.contexto.idEmpresa = -1;
        await CriadorMensagemPagamentoPendenteService.criePendentes();
        contexto.release();
      })
    }, null, true);


    // tslint:disable-next-line:no-unused-expression
    new CronJob(  '0 */5 * * * *', () => {
      this.executeNoDomain('Job executar notificaçoes pedidos', async ( contexto: any ) => {
        if(new Date().getHours() > 7){
          await NotificacaoPedidoService.executePendentes( )
        }

        contexto.release();
      })
    }, null, true);


    // tslint:disable-next-line:no-unused-expression
    new CronJob('30 */15 * * * *', () => {
      this.executeNoDomain('Job sincronizar produtos ecletica ', async ( contexto: any ) => {
        if(new Date().getHours() > 7){
          this.setEmpresaContexto({id: -1});
          await EcleticaService.monitoreProdutosIndisponiveis(contexto)
        }

      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('30 30 09 * * *', () => {
      this.executeNoDomain('Job atualizar catalogos site china', async ( contexto: any ) => {
        //await new TrendFoodsTarefa().sincronizeTodasChinas();
        //await new TrendFoodsTarefa().sincronizeTodasChinasGcom();
        await new TrendFoodsTarefa().sincronizeTodasGendai();
        await new TrendFoodsTarefa().sincronizeTodasGendaiGcom();

        console.log('fim job sincronizar catalogo')
      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('30 10 08 * * *', () => {
      this.executeNoDomain('Job sincronizar todos preços bluesoft ', async ( contexto: any ) => {
        this.setEmpresaContexto({id: -1});
        await new BlueSoftTarefa().sincronizePrecosTodasLojas(true);
      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('00 15 08 * * *', () => {
      this.executeNoDomain('Job sincronizar todos estoque bluesoft ', async ( contexto: any ) => {
        this.setEmpresaContexto({id: -1});
        await new BlueSoftTarefa().sincronizeEstoqueTodasLojas(true);
      })
    }, null, true);


    // tslint:disable-next-line:no-unused-expression
    new CronJob('00 */40 * * * *', () => {
      this.executeNoDomain('Job sincronizar preços bluesoft ', async ( contexto: any ) => {
        this.setEmpresaContexto({id: -1});
        await new BlueSoftTarefa().sincronizePrecosTodasLojas(false);
        await new BlueSoftTarefa().sincronizeEstoqueTodasLojas(false);
      })
    }, null, true);



    // tslint:disable-next-line:no-unused-expression
    new CronJob('30 45 07 * * *', () => {
      this.executeNoDomain('Job atualizar contatos em perigo', async ( contexto: any ) => {
        await new EmpresaService().recalculeResumoPedidos();
      })
    }, null, true);


    // tslint:disable-next-line:no-unused-expression
    new CronJob('30 00 15 * * *', () => {
      this.executeNoDomain('Job atualizar contatos perdidos', ( contexto: any ) => {
        this.atualizeContatosPerdidos(contexto);
      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('30 15 17 * * *', () => {
      this.executeNoDomain('Job pontos expirar', ( contexto: any ) => {
        this.envieNotificacaoPontosExpirar(contexto);
      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('30 30 05 * * *', () => {
      this.executeNoDomain('Job pontos expirados (novo)', ( contexto: any ) => {
        this.atualizePontosVencidos(contexto);
      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('0 05 10 * * *', () => {
      this.executeNoDomain('Job gerar faturas periodo', async (contexto: any) => {
        let resposta: any =  await new ContratoService().gereFaturasDoPeriodo();
        console.log('fim job faturas periodo')
        console.log(resposta)
        contexto.release();
      });
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('30 10 15 * * *', () => {
      this.executeNoDomain('Job executar cobrança cartão', ( contexto: any ) => {
        this.executeCobrancasCartao(contexto);
      })
    }, null, true);


    // tslint:disable-next-line:no-unused-expression
    new CronJob('45 */2 * * * *', () => {
       this.executeNoDomain('Job sincronnizar pedidos pooling sistemas integrados ', async ( contexto: any ) => {
         this.setEmpresaContexto({id: -1});
         await IntegradorUtils.monitorePedidosPendentes(contexto)
       })
     }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('*/30 * * * * *', () => {
      this.executeNoDomain('Job pooling lojas ifood', async ( contexto: any ) => {
        this.setEmpresaContexto({id: -1});
        await IFoodUtils.executePollingLojas();
      })
    }, null, true);

    // tslint:disable-next-line:no-unused-expression
    new CronJob('00 30 06 * * *', () => {
      this.executeNoDomain('Job remover empresas bloqueadas', async ( contexto: any ) => {
        await new EmpresaService().removaEmpresasBloquedasPorTempo();
      })
    }, null, true);


    // tslint:disable-next-line:no-unused-expression
    new CronJob('00 45 06,09,12,15,18 * * *', () => {
      this.executeNoDomain('Job fechar pedidos ifood', async ( contexto: any ) => {
        this.setEmpresaContexto({id: -1});
        await IFoodService.fechePedidosEmAberto();
      })
    }, null, true);
  }


  private async envieNotificacaoPontosExpirar(contexto: any) {
    this.setEmpresaContexto({id: -1});

    let mapeador = new MapeadorDeNotificacao();

    mapeador.desativeMultiCliente();

    let notificacoes = await  mapeador.listeAsync( { todas: true, tipoDeNotificacao: TipoDeNotificacaoEnum.PontosExpirar, ativa: true });

    async.forEachSeries(notificacoes,    (notificacao: Notificacao, cb) => {

      console.log('empresa ' + notificacao.empresa.nome  + ', dias a expirar:  ' + notificacao.qtdeDiasAtiva)

      contexto.idEmpresa = notificacao.empresa.id;
      contexto.empresa  = notificacao.empresa;


      const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(notificacao.empresa));

      new MapeadorDePontuacaoRegistrada().listePontuacoesAhVencerAhDias(notificacao.qtdeDiasAtiva).then( (pontuacoesVencidas: any) => {
        if(!pontuacoesVencidas.length) return cb();

        console.log('Total de pontuações a vencer: ' + pontuacoesVencidas.length);

        let mapPontuacoes = _.groupBy(pontuacoesVencidas, (pontuacao: any) =>  pontuacao.cartao.id);

        async.forEachSeries(Object.keys(mapPontuacoes), (idCartao: any, cb2: Function) => {
          let pontuacoes: any = mapPontuacoes[idCartao];
          let cartao: Cartao = pontuacoes[0].cartao;

          notificacaoService.envieNotificacaoPontosExpirar( cartao, pontuacoes, notificacao.qtdeDiasAtiva).then( () => {
            cb2();
          })
        }, () => {
          cb();
        })
      })

    } , (erro) => {
      console.log('fim job pontos expirar.')
      contexto.release()
    })

  }

  private async executeCobrancasCartao( contexto: any){
    await new ContratoService().executeCobrancasCartao();
    await   ProcessadorTarefasCobranca.processeTarefasPendentes();

    console.log('fim job cobrancas cartao')
    contexto.release()
  }

  private async atualizePontosVencidos( contexto: any) {
    contexto.idEmpresa =    { id: -1}
    let mapeador = new MapeadorDePontuacaoRegistrada();

    mapeador.desativeMultiCliente();
    console.log('carregar pontuaçoes vencidas...')
    let pontuacoesVencidas: any = await mapeador.listePontuacoesVencidas();
    console.log('total de pontuaçoes vencidas: ' + pontuacoesVencidas.length )
    let cartaoService = new CartaoService();

    for(let i = 0; i < pontuacoesVencidas.length; i++){
      let pontuacoes: any = pontuacoesVencidas[i].pontuacoes;
      let cartao =  pontuacoesVencidas[i].cartao;

      contexto.idEmpresa = cartao.empresa.id;
      contexto.empresa  = { id: cartao.empresa.id };

      await cartaoService.registrePontuacaoVencida(cartao, pontuacoes);
    }

    console.log('fim job pontos vencidos')
    contexto.release()
  }

  private async atualizeContatosPerdidos(contexto: any) {
    try {
      this.setEmpresaContexto({id: -1});

      console.log('[task_contatos_perdidos] iniciando');
      let mapeador = new MapeadorDeNotificacao();

      mapeador.desativeMultiCliente();

      console.log('[task_contatos_perdidos] buscando notificações ativas');
      let notificacoes = await mapeador.listeAsync({
        todas: true,
        tipoDeNotificacao: TipoDeNotificacaoEnum.ClientePerdido,
        ativa: true
      });

      console.log(`[task_contatos_perdidos] encontradas ${notificacoes.length} notificações para processar`);

      for(const notificacao of notificacoes) {
        try {
          console.log(`[task_contatos_perdidos] processando empresa ${notificacao.empresa.nome} (${notificacao.empresa.id})`);

          contexto.idEmpresa = notificacao.empresa.id;
          contexto.empresa = notificacao.empresa;

          const notificacaoService = new NotificacaoService(
            Ambiente.Instance.novoEnviadorDeMensagens(notificacao.empresa)
          );

          await notificacaoService.envieNotificacaoClientesPerdidos(notificacao);

          console.log(`[task_contatos_perdidos] empresa ${notificacao.empresa.nome} processada com sucesso`);
        } catch(erro) {
          console.error(`[task_contatos_perdidos] erro ao processar empresa ${notificacao.empresa.nome}:`, erro);
        }
      }

      console.log('[task_contatos_perdidos] finalizado com sucesso');
    } catch(erro) {
      console.error('[task_contatos_perdidos] erro fatal:', erro);
    } finally {
      if(contexto && typeof contexto.release === 'function') {
        await contexto.release();
      }
    }
  }

  private async atualizeContatosEmPerigo(contexto: any) {
    try {
      this.setEmpresaContexto({id: -1});

      console.log('[task_contatos_em_perigo] iniciando');
      let mapeador = new MapeadorDeNotificacao();

      mapeador.desativeMultiCliente();

      console.log('[task_contatos_em_perigo] buscando notificações ativas');
      let notificacoes = await mapeador.listeAsync({
        todas: true,
        tipoDeNotificacao: TipoDeNotificacaoEnum.SentimosSuaFalta,
        ativa: true
      });

      console.log(`[task_contatos_em_perigo] encontradas ${notificacoes.length} notificações para processar`);

      for(const notificacao of notificacoes) {
        try {
          console.log(`[task_contatos_em_perigo] processando empresa ${notificacao.empresa.nome} (${notificacao.empresa.id}) - ${notificacao.qtdeDiasAtiva} dias`);

          contexto.idEmpresa = notificacao.empresa.id;
          contexto.empresa = notificacao.empresa;

          const notificacaoService = new NotificacaoService(
            Ambiente.Instance.novoEnviadorDeMensagens(notificacao.empresa)
          );

          await notificacaoService.envieNotificacaoClientesEmPerigo(notificacao);

          console.log(`[task_contatos_em_perigo] empresa ${notificacao.empresa.nome} processada com sucesso`);
        } catch(erro) {
          console.error(`[task_contatos_em_perigo] erro ao processar empresa ${notificacao.empresa.nome}:`, erro);
        }
      }

      console.log('[task_contatos_em_perigo] finalizado com sucesso');
    } catch(erro) {
      console.error('[task_contatos_em_perigo] erro fatal:', erro);
    } finally {
      if(contexto && typeof contexto.release === 'function') {
        await contexto.release();
      }
    }
  }

  private setEmpresaContexto(empresa: any){
    require('domain').active.contexto.idEmpresa = empresa.id;
  }

  private executeNoDomain(nome: string, cb: Function){
    console.log('executar job: ' + nome)
    console.log(new Date())

    let reqDomain = require('domain').create();

    reqDomain.contexto = new Contexto();

    reqDomain.on('error',   async (er: any) => {
      console.error('==============Erro no JOB ==================>'  + nome);
      console.error('Detalhes do erro:', er);
      console.error('Stack:', er.stack);

      try {
        if(reqDomain.contexto && typeof reqDomain.contexto.release === 'function') {
          await reqDomain.contexto.release();
          console.log('Contexto liberado com sucesso após erro');
        }
      } catch(e) {
        console.error('Erro ao liberar contexto:', e);
      }
    });

    reqDomain.run(async function(){
      try {
        await cb(reqDomain.contexto);
      } catch(e) {
        console.error('Erro durante execução do job ' + nome + ':', e);
        if(reqDomain.contexto && typeof reqDomain.contexto.release === 'function') {
          await reqDomain.contexto.release();
        }
      }
    });
  }

}
