import {Campan<PERSON>} from "../domain/Campanha";
import {NotificacaoApp} from "../domain/app/NotificacaoApp";
import {MapeadorDeNotificacaoApp} from "../mapeadores/MapeadorDeNotificacaoApp";
import {MapeadorDeMensagemEnviada} from "../mapeadores/MapeadorDeMensagemEnviada";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {MapeadorDeNotificacaoPedido} from "../mapeadores/MapeadorDeNotificacaoPedido";
import {MapeadorDeTarefaMensagemAvaliarPedido} from "../mapeadores/MapeadorDeTarefaMensagemAvaliarPedido";
import {TarefaMensagemAvaliarPedido} from "../domain/delivery/TarefaMensagemAvaliarPedido";
import {MapeadorDeNotificacao} from "../mapeadores/MapeadorDeNotificacao";
import {Notificacao} from "../domain/Notificacao";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {NotificacaoPedidoService} from "./NotificacaoPedidoService";
import {NotificacaoService} from "./NotificacaoService";
import {Ambiente} from "./Ambiente";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";

export class CriadorDeMensagemAvaliarPedidoService {
  static async executePendentes( ) {
    return new Promise<void>( async(resolve, reject) => {
      const mapeadorDeTarefaMensagemAvaliarPedido = new MapeadorDeTarefaMensagemAvaliarPedido();
      mapeadorDeTarefaMensagemAvaliarPedido.desativeMultiCliente();

      const tarefas: Array<TarefaMensagemAvaliarPedido> = await mapeadorDeTarefaMensagemAvaliarPedido.
        listeAsync({tarefasPendentes: true});

      console.log('tarefas: ' + tarefas.length);

      for( let tarefa of tarefas ) {
        require('domain').active.contexto.idEmpresa = tarefa.empresa.id;
        require('domain').active.contexto.empresa = tarefa.empresa;

        const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(tarefa.empresa));

        const resposta = await notificacaoService.envieNotificacaoAvaliarPedido(tarefa.pedido, tarefa.pedido.contato);

        if( !resposta ) {
          continue;
        }
        if( resposta.sucesso ) {
          const mensagemCriada: MensagemEnviada = resposta.data;

          tarefa.mensagemAvaliarPedido = mensagemCriada;

          await mapeadorDeTarefaMensagemAvaliarPedido.atualizeSync(tarefa);
        }
        console.log(1);
      }

      resolve();
    })
  }
}
