import { Server } from 'socket.io';
import {EmpresaService} from "./EmpresaService";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
let redis = require("redis");
import { createAdapter } from "@socket.io/redis-adapter";
const { createServer } = require("http");

interface SocketClientInfo {
  socketId: string;
  room: string;
  empresaId: number;
  empresaNome?: string;
  origem: string;
  navegador: string;
  ip: string;
  conectadoEm: Date;
  ultimaAtividade: Date;
  comandosExecutados: number;
  status: 'connected' | 'idle' | 'reconnecting';
  desconectadoEm?: Date;
}

interface ConnectionHistoryEvent {
  socketId: string;
  evento: 'connect' | 'disconnect' | 'reconnect';
  empresaId: number;
  empresaNome?: string;
  origem: string;
  navegador: string;
  ip: string;
  timestamp: Date;
  room: string;
}

interface EventoWhatsapp {
  tipo: 'NOVAS_MENSAGENS' | 'NOVA_MENSAGEM' | 'whatsapp_new_message' | 'whatsapp_new_messages' |
    'ENVIOU_MENSAGEM' | 'RESP_STATUS_WHATSAPP' | 'SELECIONOU_CONTATO' | 'DIGITOU_MENSAGEM' |
    'RESP_CARREGOU_WHATSAPP';
  dados: any;
  timestamp: Date;
}

class SocketServer {
  private static instance: SocketServer;

  private io: Server;
  private redisClient: any;

  public static getInstance(port: number): SocketServer {
    console.log(`getInstance() called with port: ${port}`);
    if (!SocketServer.instance) {
      console.log('Creating new SocketServer instance');
      SocketServer.instance = new SocketServer(port);

      console.log('\n\n\n-------------criando instancia novamente----------\n\n\n');
      // Configura os listeners de eventos
      SocketServer.instance.setupListeners();
    } else {
      console.log('Using existing SocketServer instance');
    }

    return SocketServer.instance;
  }

  private constructor(port: number) {
    console.log(`SocketServer constructor called with port: ${port}`);
  }

  private async setupListeners() {
    console.log('setupListeners() called - starting socket server setup');
    let client = redis.createClient();
    const subClient = client.duplicate();
    this.redisClient = redis.createClient();

    this.io = new Server(3000, {
      adapter: createAdapter(client, subClient),
      transports: ['websocket'],
      path: '/testesocketio/',
      cors: {
        origin: '*',
      }
    });

    console.log('Socket.IO server created on port 3000');

    this.io.on('connection', async (socket) => {
      console.log('\n\n=== NOVA CONEXÃO ===');
      console.log('Socket ID:', socket.id);
      console.log('Query params:', socket.handshake.query.emp);
      ExecutorAsync.execute( async (cb: Function) => {
        console.log('origem: ' + socket.handshake.headers.origin);
        const origem = socket.handshake.headers.origin;

        let empresaDaRequest = '';

        if( origem.includes('chrome-extension') ) {
          empresaDaRequest = socket.handshake.query['emp'] +  '';
          empresaDaRequest = await new EmpresaService().obtenhaEmpresaPeloLink(empresaDaRequest)
        }
        else {
          const clientUrl = (origem + '').replace('https://', '').replace('http://', '');

          empresaDaRequest = await new EmpresaService().obtenhaEmpresaPeloLink(clientUrl)
        }

        console.log('empresa da request: ' + empresaDaRequest);

        new MapeadorDeEmpresa().selecioneCachePorDominio(empresaDaRequest).then( (empresa: any) => {
          console.log('conectando por: ' + empresaDaRequest);

          if( !empresa ) {
            console.log('não achou a empresa: ' + empresaDaRequest);
            console.log('AVISO: Socket conectando sem empresa definida!');

            // Configurar socket.data mesmo sem empresa
            socket.data = {
              room: '',
              empresaId: 0,
              empresaNome: 'Sem empresa',
              origem: this.detectOrigem(socket.handshake.headers.origin, socket.handshake.headers['user-agent']),
              navegador: this.detectBrowser(socket.handshake.headers['user-agent'] || ''),
              conectadoEm: new Date(socket.handshake.time),
              ultimaAtividade: new Date(),
              comandosExecutados: 0
            };

            cb();
            return;
          }

          console.log('achou a empresa: ' + empresa.id);

          const roomName = 'empresa_' + empresa.id;
          socket.join(roomName);
          console.log(`Socket ${socket.id} entrando na room: ${roomName}`);
          socket.emit('message', 'Hello World');

          // Configurar dados do socket
          socket.data = {
            room: roomName,
            empresaId: empresa.id,
            empresaNome: empresa.nome,
            origem: this.detectOrigem(socket.handshake.headers.origin, socket.handshake.headers['user-agent']),
            navegador: this.detectBrowser(socket.handshake.headers['user-agent'] || ''),
            conectadoEm: new Date(socket.handshake.time),
            ultimaAtividade: new Date(),
            comandosExecutados: 0
          };

          console.log('Socket.data configurado:', socket.data);

          // Log conexão no histórico
          this.logConnectionEvent(empresa.id, {
            socketId: socket.id,
            evento: 'connect',
            empresaId: empresa.id,
            empresaNome: empresa.nome,
            origem: socket.data.origem,
            navegador: socket.data.navegador,
            ip: socket.handshake.address,
            timestamp: new Date(),
            room: roomName
          });


          socket.on('disconnect', () => {
            console.log(`disconnect ${socket.id} `);

            // Log desconexão no histórico
            if (socket.data) {
              this.logConnectionEvent(socket.data.empresaId, {
                socketId: socket.id,
                evento: 'disconnect',
                empresaId: socket.data.empresaId,
                empresaNome: socket.data.empresaNome,
                origem: socket.data.origem,
                navegador: socket.data.navegador,
                ip: socket.handshake.address,
                timestamp: new Date(),
                room: socket.data.room
              });
            }
          });

          // Listener para eventos do WhatsApp
          socket.on('whatsapp-evento', (evento: EventoWhatsapp) => {
            console.log(`[WhatsApp Event] ${socket.id} - Tipo: ${evento.tipo}`);
            console.log('Dados:', evento.dados);

            if (!socket.data || !socket.data.empresaId) {
              console.warn('[WhatsApp Event] Socket sem empresa definida, ignorando evento');
              return;
            }

            this.processarEventoWhatsapp(socket, evento);
          });

          cb();
        });
      }, (error: any) => {
        console.log('deu erro', error);
      }, 100);
    });
  }

  private processarEventoWhatsapp(socket: any, evento: EventoWhatsapp) {
    const empresaId = socket.data.empresaId;
    const socketId = socket.id;
    const agora = new Date();

    // Adicionar timestamp se não existir
    if (!evento.timestamp) {
      evento.timestamp = agora;
    }

    // Salvar evento no Redis para auditoria
    const eventoCompleto = {
      ...evento,
      socketId: socketId,
      empresaId: empresaId,
      processadoEm: agora
    };

    // Armazenar eventos por empresa
    const chaveEventos = `whatsapp:eventos:${empresaId}`;
    this.redisClient.lpush(chaveEventos, JSON.stringify(eventoCompleto));
    this.redisClient.ltrim(chaveEventos, 0, 999); // Manter últimos 1000 eventos
    this.redisClient.expire(chaveEventos, 86400); // TTL 24 horas

    // Armazenar eventos por socket individual
    const chaveEventosSocket = `whatsapp:eventos:socket:${socketId}`;
    this.redisClient.lpush(chaveEventosSocket, JSON.stringify(eventoCompleto));
    this.redisClient.ltrim(chaveEventosSocket, 0, 99); // Manter últimos 100 eventos por socket
    this.redisClient.expire(chaveEventosSocket, 3600); // TTL 1 hora

    console.log(`[WhatsApp Event] Evento ${evento.tipo} salvo para empresa ${empresaId}, socket ${socketId}`);

    // Normaliza tipo para lidar com variações (caixa, separadores)
    const tipoRaw = (evento.tipo || '').toString();
    const tipoNorm = tipoRaw.toLowerCase().replace(/[-:]/g, '_');

    // Processamento específico por tipo de evento
    switch (tipoNorm) {
      case 'novas_mensagens':
        this.processarNovasMensagens(socket, evento.dados);
        break;

      case 'nova_mensagem':
        // Normaliza evento singular para o mesmo fluxo das novas mensagens
        // Aceita tanto objeto único quanto array em evento.dados
        const mensagens = Array.isArray(evento.dados) ? evento.dados : [evento.dados];
        this.processarNovasMensagens(socket, mensagens);
        break;

      case 'whatsapp_new_message':
        // Compatibilidade com evento vindo da extensão em inglês
        // Extraímos e logamos o body da(s) mensagem(ns) recebida(s)
        const msgs = Array.isArray(evento.dados) ? evento.dados : [evento.dados];
        msgs.forEach((m, idx) => {
          const body = m?.body ?? m?.message?.body ?? m?.text ?? m?.conversation ?? m?.content?.body ?? JSON.stringify(m);
          console.log(`[WhatsApp] ${socket.id} - whatsapp_new_message body${msgs.length > 1 ? ` [${idx}]` : ''}:`, body);
        });
        this.processarNovasMensagens(socket, msgs);
        break;

      case 'whatsapp_new_messages':
        // Variação plural
        {
          const msgsPlural = Array.isArray(evento.dados) ? evento.dados : [evento.dados];
          msgsPlural.forEach((m, idx) => {
            const body = m?.body ?? m?.message?.body ?? m?.text ?? m?.conversation ?? m?.content?.body ?? JSON.stringify(m);
            console.log(`[WhatsApp] ${socket.id} - whatsapp_new_messages body${msgsPlural.length > 1 ? ` [${idx}]` : ''}:`, body);
          });
          this.processarNovasMensagens(socket, msgsPlural);
        }
        break;

      case 'enviou_mensagem':
        this.processarEnviouMensagem(socket, evento.dados);
        break;

      case 'resp_status_whatsapp':
        this.processarStatusWhatsapp(socket, evento.dados);
        break;

      case 'selecionou_contato':
        this.processarSelecionouContato(socket, evento.dados);
        break;

      case 'resp_carregou_whatsapp':
        this.processarCarregouWhatsapp(socket, evento.dados);
        break;

      default:
        // Fallback: tenta identificar padrões conhecidos
        if (/whatsapp.*new.*message/i.test(tipoRaw)) {
          const msgs2 = Array.isArray(evento.dados) ? evento.dados : [evento.dados];
          msgs2.forEach((m, idx) => {
            const body = m?.body ?? m?.message?.body ?? m?.text ?? m?.conversation ?? m?.content?.body ?? JSON.stringify(m);
            console.log(`[WhatsApp] ${socket.id} - whatsapp_new_message (fallback) body${msgs2.length > 1 ? ` [${idx}]` : ''}:`, body);
          });
          this.processarNovasMensagens(socket, msgs2);
        } else if (tipoNorm.includes('whatsapp') && tipoNorm.includes('message')) {
          // Heurística ampla para tipos desconhecidos que contenham whatsapp e message
          const msgsHeur = Array.isArray(evento.dados) ? evento.dados : [evento.dados];
          msgsHeur.forEach((m, idx) => {
            const body = m?.body ?? m?.message?.body ?? m?.text ?? m?.conversation ?? m?.content?.body ?? JSON.stringify(m);
            console.log(`[WhatsApp] ${socket.id} - whatsapp message (heuristic) body${msgsHeur.length > 1 ? ` [${idx}]` : ''}:`, body);
          });
          this.processarNovasMensagens(socket, msgsHeur);
        } else {
          // Log de diagnóstico enxuto do payload para facilitar debug sem poluir o console
          const preview = (() => {
            try {
              const base = Array.isArray(evento.dados) ? evento.dados[0] : evento.dados;
              if (!base) return '[sem dados]';
              const body = base?.body ?? base?.message?.body ?? base?.text ?? base?.conversation ?? base?.content?.body;
              return body ? `${(body+ '').slice(0, 120)}` : JSON.stringify(base).slice(0, 200);
            } catch { return '[erro ao gerar preview]'; }
          })();
          console.log(`[WhatsApp Event] Tipo de evento não processado: ${evento.tipo} | preview:`, preview);
        }
    }
  }

  public getAllRooms(): Promise<any> {
    return new Promise((resolve, reject) => {
      const rooms = this.io.sockets.adapter.rooms;
      resolve(Array.from(rooms));
    });
  }

  public getConnectedClients(): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.io.of('/').allSockets().then((sockets) => {
        resolve(Array.from(sockets));
      }).catch((err) => {
        reject(err);
      });
    });
  }

  // Métodos específicos para processar cada tipo de evento WhatsApp
  private processarNovasMensagens(socket: any, mensagens: any[]) {
    console.log(`[WhatsApp] ${socket.id} - Processando ${mensagens.length} novas mensagens`);

    // Loga o body de cada mensagem para facilitar depuração
    try {
      mensagens.forEach((m, idx) => {
        const body = m?.body ?? m?.message?.body ?? m?.text ?? m?.conversation ?? m?.content?.body ?? m?.message?.conversation;
        if (body !== undefined) {
          console.log(`[WhatsApp] ${socket.id} - mensagem body${mensagens.length > 1 ? ` [${idx}]` : ''}:`, body);
        } else {
          const preview = (() => { try { return JSON.stringify(m).slice(0, 200); } catch { return '[preview indisponível]'; } })();
          console.log(`[WhatsApp] ${socket.id} - mensagem sem body, preview:`, preview);
        }
      });
    } catch (e) {
      console.log('[WhatsApp] Erro ao logar bodies das mensagens:', e);
    }

    // Verificar se alguma mensagem é do número específico para auto-reply
    mensagens.forEach(mensagem => {
      const from = mensagem?.from ?? mensagem?.message?.from ?? mensagem?.author ?? mensagem?.sender;

      if (from === '<EMAIL>') {
        console.log(`[WhatsApp Auto-Reply] ${socket.id} - Mensagem detectada do número ${from}, enviando auto-reply`);

        // Enviar comando de auto-reply para o cliente
        socket.emit('NOVA_MENSAGEM', {
          to: '<EMAIL>',
          body: 'reply',
          tipo: 'chat'
        });

        console.log(`[WhatsApp Auto-Reply] ${socket.id} - Comando NOVA_MENSAGEM enviado para ${from}`);
      }
    });

    // Salvar mensagens no Redis para processamento posterior
    const empresaId = socket.data.empresaId;
    const chaveMensagens = `whatsapp:mensagens:${empresaId}`;

    mensagens.forEach(mensagem => {
      const mensagemCompleta = {
        ...mensagem,
        socketId: socket.id,
        empresaId: empresaId,
        processadaEm: new Date()
      };

      this.redisClient.lpush(chaveMensagens, JSON.stringify(mensagemCompleta));
    });

    this.redisClient.ltrim(chaveMensagens, 0, 9999); // Manter últimas 10000 mensagens
    this.redisClient.expire(chaveMensagens, 604800); // TTL 7 dias

    // Broadcast para outros sockets da mesma empresa (opcional)
    socket.to(socket.data.room).emit('novas-mensagens-whatsapp', {
      quantidade: mensagens.length,
      socketOrigem: socket.id
    });
  }

  private processarEnviouMensagem(socket: any, respostaEnvio: any) {
    console.log(`[WhatsApp] ${socket.id} - Mensagem enviada:`, respostaEnvio);

    // Salvar confirmação de envio no Redis
    const empresaId = socket.data.empresaId;
    const chaveEnvios = `whatsapp:envios:${empresaId}`;

    const envioCompleto = {
      ...respostaEnvio,
      socketId: socket.id,
      empresaId: empresaId,
      confirmedEm: new Date()
    };

    this.redisClient.lpush(chaveEnvios, JSON.stringify(envioCompleto));
    this.redisClient.ltrim(chaveEnvios, 0, 999); // Manter últimos 1000 envios
    this.redisClient.expire(chaveEnvios, 86400); // TTL 24 horas
  }

  private processarStatusWhatsapp(socket: any, status: any) {
    console.log(`[WhatsApp] ${socket.id} - Status atualizado:`, status);

    // Salvar status atual no Redis
    const empresaId = socket.data.empresaId;
    const chaveStatus = `whatsapp:status:${empresaId}`;

    const statusCompleto = {
      ...status,
      socketId: socket.id,
      empresaId: empresaId,
      atualizadoEm: new Date()
    };

    this.redisClient.setex(chaveStatus, 3600, JSON.stringify(statusCompleto)); // TTL 1 hora

    // Broadcast status para outros sockets da mesma empresa
    socket.to(socket.data.room).emit('status-whatsapp-atualizado', statusCompleto);
  }

  private processarSelecionouContato(socket: any, dadosContato: any) {
    console.log(`[WhatsApp] ${socket.id} - Contato selecionado:`, dadosContato.chat || dadosContato);

    // Salvar contato atual no Redis
    const empresaId = socket.data.empresaId;
    const chaveContatoAtual = `whatsapp:contato-atual:${empresaId}:${socket.id}`;

    this.redisClient.setex(chaveContatoAtual, 1800, JSON.stringify({
      ...dadosContato,
      socketId: socket.id,
      selecionadoEm: new Date()
    })); // TTL 30 minutos
  }

  private processarCarregouWhatsapp(socket: any, dadosCarregamento: any) {
    console.log(`[WhatsApp] ${socket.id} - WhatsApp carregado:`, dadosCarregamento);

    // Salvar informações de carregamento
    const empresaId = socket.data.empresaId;
    const chaveCarregamento = `whatsapp:carregamento:${empresaId}:${socket.id}`;

    this.redisClient.setex(chaveCarregamento, 3600, JSON.stringify({
      ...dadosCarregamento,
      socketId: socket.id,
      empresaId: empresaId,
      carregadoEm: new Date()
    })); // TTL 1 hora
  }

  dispare(canal: string, evento: string) {
    console.log(`[SocketServer] Emitindo evento "${evento}" para o canal "${canal}"`);
    // Incrementar métricas quando servidor envia evento
    const hoje = new Date().toDateString();

    // Incrementar comandos por room
    this.redisClient.incr(`metrics:room:${canal}:comandos:${hoje}`, (err: any, result: any) => {
      if (err) {
        console.error(`Erro ao incrementar room commands:`, err);
      }
    });

    // Incrementar comandos globais
    this.redisClient.incr(`metrics:global:comandos:${hoje}`, (err: any, result: any) => {
      if (err) {
        console.error(`Erro ao incrementar global commands:`, err);
      }
    });

    // Salvar no histórico por room/canal (mantido para compatibilidade)
    this.redisClient.lpush(`comandos:${canal}`, JSON.stringify({
      timestamp: new Date(),
      comando: evento,
      tipo: 'server-to-client'
    }));
    this.redisClient.ltrim(`comandos:${canal}`, 0, 99); // Manter últimos 100
    this.redisClient.expire(`comandos:${canal}`, 3600); // TTL 1 hora

    // Registrar comando para cada socket individual da room
    this.io.in(canal).fetchSockets().then((sockets) => {
      sockets.forEach((socket) => {
        const comandoData = JSON.stringify({
          timestamp: new Date(),
          comando: evento,
          tipo: 'server-to-client',
          canal: canal
        });

        // Salvar no histórico individual por socket
        this.redisClient.lpush(`comandos:socket:${socket.id}`, comandoData);
        this.redisClient.ltrim(`comandos:socket:${socket.id}`, 0, 99); // Manter últimos 100
        this.redisClient.expire(`comandos:socket:${socket.id}`, 3600); // TTL 1 hora
      });
    }).catch((err) => {
      console.error('Erro ao buscar sockets para registrar comandos individuais:', err);
    });

    this.io.to(canal).emit(evento);
  }

  // Novos métodos para o dashboard
  async getDetailedConnectedClients(room?: string): Promise<SocketClientInfo[]> {
    const sockets = await this.io.fetchSockets();
    const hoje = new Date().toDateString();

    console.log(`=== DEBUG getDetailedConnectedClients ===`);
    console.log(`Total de sockets: ${sockets.length}`);

    // Buscar comandos por room
    const roomCommands = new Map<string, number>();

    let clientesDetalhados = await Promise.all(sockets.map(async (socket) => {
      const socketRoom = socket.data?.room || '';

      console.log(`Socket ${socket.id}:`);
      console.log(`  - Room: "${socketRoom}"`);
      console.log(`  - socket.data:`, socket.data);

      // Buscar comandos da room se ainda não tiver no cache
      if (!roomCommands.has(socketRoom)) {
        if (socketRoom) {
          const comandos = await new Promise<number>((resolve) => {
            this.redisClient.get(`metrics:room:${socketRoom}:comandos:${hoje}`, (err: any, value: string) => {
              console.log(`  - Redis key: metrics:room:${socketRoom}:comandos:${hoje}`);
              console.log(`  - Redis value: ${value}`);
              resolve(err ? 0 : parseInt(value || '0'));
            });
          });
          roomCommands.set(socketRoom, comandos);
        } else {
          console.log(`  - Socket sem room definida!`);
          roomCommands.set(socketRoom, 0);
        }
      }

      const comandosExecutados = roomCommands.get(socketRoom) || 0;
      console.log(`  - Comandos executados: ${comandosExecutados}`);

      // Fallback inteligente para origem e navegador se socket.data não estiver disponível
      const origem = socket.data?.origem || this.detectOrigem(
        socket.handshake.headers.origin,
        socket.handshake.headers['user-agent']
      );
      const navegador = socket.data?.navegador || this.detectBrowser(
        socket.handshake.headers['user-agent'] || ''
      );

      return {
        socketId: socket.id,
        room: socketRoom,
        empresaId: socket.data?.empresaId || 0,
        empresaNome: socket.data?.empresaNome,
        origem: origem,
        navegador: navegador,
        ip: socket.handshake.address,
        conectadoEm: socket.data?.conectadoEm || new Date(socket.handshake.time),
        ultimaAtividade: socket.data?.ultimaAtividade || new Date(),
        comandosExecutados: comandosExecutados,
        status: 'connected' as const
      };
    }));

    // Filtrar por room se especificado
    if (room) {
      clientesDetalhados = clientesDetalhados.filter(c => c.room === room);
    }

    console.log(`=== FIM DEBUG ===`);
    console.log(`Room commands cache:`, Array.from(roomCommands.entries()));

    return clientesDetalhados;
  }

  async getMetricsByRoom(room: string): Promise<any> {
    const hoje = new Date().toDateString();

    // Sockets conectados desta room
    const socketsDaRoom = await this.io.in(room).fetchSockets();

    // Comandos hoje
    const comandosHoje = await new Promise<number>((resolve) => {
      const chave = `metrics:room:${room}:comandos:${hoje}`;

      this.redisClient.get(chave, (err: any, value: string) => {
        if (err) {
          console.error(`Erro ao ler comandos room:`, err);
          resolve(0);
        } else {
          const resultado = parseInt(value || '0');
          resolve(resultado);
        }
      });
    });

    // Origens únicas
    const origensUnicas = await new Promise<number>((resolve) => {
      this.redisClient.scard(`metrics:room:${room}:origens:${hoje}`, (err: any, count: number) => {
        resolve(count || 0);
      });
    });

    return {
      room,
      conexoesAtivas: socketsDaRoom.length,
      comandosHoje,
      origensUnicas
    };
  }

  async getGlobalMetrics(): Promise<any> {
    const hoje = new Date().toDateString();
    const sockets = await this.io.fetchSockets();

    // Agrupar por room
    const roomsAtivas = new Set<string>();
    sockets.forEach(socket => {
      if (socket.data?.room) {
        roomsAtivas.add(socket.data.room);
      }
    });

    // Buscar métricas de cada room
    const metricasPorRoom = await Promise.all(
      Array.from(roomsAtivas).map(room => this.getMetricsByRoom(room))
    );

    // Total global
    const comandosHojeGlobal = await new Promise<number>((resolve) => {
      const chave = `metrics:global:comandos:${hoje}`;

      this.redisClient.get(chave, (err: any, value: string) => {
        if (err) {
          console.error(`Erro ao ler comandos global:`, err);
          resolve(0);
        } else {
          const resultado = parseInt(value || '0');
          resolve(resultado);
        }
      });
    });

    // Calcular origens únicas globais
    const origensGlobais = new Set<string>();
    sockets.forEach(s => {
      if (s.data?.origem) origensGlobais.add(s.data.origem);
    });

    return {
      conexoesAtivas: sockets.length,
      totalSockets: await this.io.engine.clientsCount,
      comandosHoje: comandosHojeGlobal,
      origensUnicas: origensGlobais.size,
      metricasPorRoom
    };
  }

  async getSocketCommands(socketId: string): Promise<any[]> {
    return new Promise((resolve) => {
      this.redisClient.lrange(`comandos:socket:${socketId}`, 0, -1, (err: any, values: string[]) => {
        if (err || !values) {
          resolve([]);
          return;
        }

        const comandos = values.map(v => JSON.parse(v));
        resolve(comandos);
      });
    });
  }

  async disconnectClient(socketId: string): Promise<void> {
    const sockets = await this.io.fetchSockets();
    const socket = sockets.find(s => s.id === socketId);
    if (socket) {
      socket.disconnect(true);
    }
  }

  // Métodos de histórico de conexões
  private logConnectionEvent(empresaId: number, event: ConnectionHistoryEvent): void {
    if (!this.redisClient || empresaId === 0) return;

    const historyKey = `connection_history:${empresaId}`;
    const timestamp = event.timestamp.getTime();
    const eventData = JSON.stringify(event);

    this.redisClient.zadd(historyKey, timestamp, eventData, (err: any) => {
      if (err) {
        console.error(`Erro ao salvar evento de conexão:`, err);
      } else {
        console.log(`Evento ${event.evento} salvo para empresa ${empresaId}`);
      }
    });

    // Limpeza automática a cada log (remove dados > 30 dias)
    this.cleanupOldConnections(empresaId);
  }

  private cleanupOldConnections(empresaId: number): void {
    if (!this.redisClient) return;

    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const historyKey = `connection_history:${empresaId}`;

    this.redisClient.zremrangebyscore(historyKey, '-inf', thirtyDaysAgo, (err: any, removed: number) => {
      if (err) {
        console.error(`Erro na limpeza do histórico:`, err);
      } else if (removed > 0) {
        console.log(`Removidos ${removed} eventos antigos da empresa ${empresaId}`);
      }
    });
  }

  async getConnectionHistory(empresaId: number, startDate?: Date, endDate?: Date, limit: number = 1000): Promise<ConnectionHistoryEvent[]> {
    if (!this.redisClient) return [];

    const historyKey = `connection_history:${empresaId}`;
    const start = startDate ? startDate.getTime() : '-inf';
    const end = endDate ? endDate.getTime() : '+inf';

    return new Promise((resolve) => {
      this.redisClient.zrangebyscore(historyKey, start, end, 'LIMIT', 0, limit, (err: any, results: string[]) => {
        if (err || !results) {
          console.error(`Erro ao buscar histórico:`, err);
          resolve([]);
          return;
        }

        try {
          const events = results.map(eventStr => JSON.parse(eventStr) as ConnectionHistoryEvent);
          resolve(events);
        } catch (parseErr) {
          console.error(`Erro ao parsear eventos:`, parseErr);
          resolve([]);
        }
      });
    });
  }

  async getRecentConnections(empresaId: number, count: number = 100): Promise<ConnectionHistoryEvent[]> {
    if (!this.redisClient) return [];

    const historyKey = `connection_history:${empresaId}`;

    return new Promise((resolve) => {
      this.redisClient.zrevrange(historyKey, 0, count - 1, (err: any, results: string[]) => {
        if (err || !results) {
          console.error(`Erro ao buscar conexões recentes:`, err);
          resolve([]);
          return;
        }

        try {
          const events = results.map(eventStr => JSON.parse(eventStr) as ConnectionHistoryEvent);
          resolve(events);
        } catch (parseErr) {
          console.error(`Erro ao parsear eventos recentes:`, parseErr);
          resolve([]);
        }
      });
    });
  }

  async getConnectionStats(empresaId: number, days: number = 7): Promise<any> {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));

    const events = await this.getConnectionHistory(empresaId, startDate, endDate);

    const connects = events.filter(e => e.evento === 'connect').length;
    const disconnects = events.filter(e => e.evento === 'disconnect').length;
    const uniqueIps = new Set(events.map(e => e.ip)).size;
    const origens = new Set(events.map(e => e.origem));

    return {
      periodo: `${days} dias`,
      totalConexoes: connects,
      totalDesconexoes: disconnects,
      ipsUnicos: uniqueIps,
      origensUnicas: Array.from(origens),
      eventos: events.length
    };
  }

  // Helpers
  private detectOrigem(origin: string | undefined, userAgent?: string): string {
    if (!origin || origin.includes('chrome-extension')) {
      return 'Chrome Extension';
    }
    if (userAgent?.includes('Mobile')) {
      return 'Mobile App';
    }
    if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
      return 'Development';
    }
    return 'Website';
  }

  private detectBrowser(userAgent: string): string {
    if (!userAgent) return 'Unknown';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Other';
  }
}

export default SocketServer;
