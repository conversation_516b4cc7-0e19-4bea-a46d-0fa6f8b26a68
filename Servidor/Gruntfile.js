module.exports = function(grunt) {
  grunt.loadNpmTasks('grunt-contrib-watch');

  grunt.initConfig({
    //use the copy with source and destination
    copy: {
      html: {
        files: [
          {src: 'views/**', cwd: 'server', dest: 'distServer', expand: true},
          {src: '*.json', cwd: 'server', dest: 'distServer', expand: true},
          {src: '**', cwd: 'src/extensao', dest: 'dist/assets', expand: true},
          {src: '**', cwd: 'src/extensao', dest: 'distServer/public/assets', expand: true},
          {src: 'public/**', cwd: 'server', dest: 'distServer', expand: true},
          {src: 'certificados/**', cwd: '', dest: 'distServer', expand: true},
          {src: 'assets/*.png', cwd: 'src', dest: 'distServer/public', expand: true},
          {src: 'assets/*.jpg', cwd: 'src', dest: 'distServer/public', expand: true},
          {src: 'images/*.*', cwd: 'projects/loja/src/assets', dest: 'distServer/public', expand: true},
          {src: 'mapeamentos/**', cwd: 'server', dest: 'distServer', expand: true}
        ]
      },
      views: {
        files: [
          {src: 'views/**', cwd: 'server', dest: 'distServer', expand: true}
        ]
      },
      json: {
        files: [
          {src: '*.json', cwd: 'server', dest: 'distServer', expand: true},
        ],
        options: {
          timestamp: true,
        }
      },
      mapeamentos: {
        files: [
          {src: 'mapeamentos/**', cwd: 'server', dest: 'distServer', expand: true}
        ]
      },
      i18n: {
        files: [
          {src: 'locales/**', cwd: 'server', dest: 'distServer', expand: true}
        ]
      },
      typebot: {
        files: [
          {src: 'templates/**', cwd: 'server/service/ia', dest: 'distServer', expand: true}
        ]
      },
      paginas: {
        files: [
          {src: 'public/**', cwd: 'server', dest: 'distServer', expand: true}
        ]
      }
    },
    watch: {
      views: {
        files: ['server/views/**'],
        tasks: ['copy:views']
      },
      mapeamentos: {
        files: ['server/mapeamentos/*.xml'],
        tasks: ['copy:mapeamentos']
      },
      json: {
        files: ['server/*.json'],
        tasks: ['copy:json']
      },
      html: {
        files: ['server/public/**'],
        tasks: ['copy:paginas']
      },
      i18n: {
        files: ['server/locales/**'],
        tasks: ['copy:i18n']
      },
      typebot: {
        files: ['server/service/ia/templates/**'],
        tasks: ['copy:typebot']
      }
    }
  });

//load the copy module
  grunt.loadNpmTasks('grunt-contrib-copy');
  grunt.loadNpmTasks('grunt-contrib-watch');

//register the build task
  grunt.registerTask('build', ['copy:html']);
};
