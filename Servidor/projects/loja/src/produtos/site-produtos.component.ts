import {AfterViewInit, Component, ElementRef, HostListener, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";

import {CarrinhoService} from "../services/carrinho.service";
import {PedidoLoja} from "../objeto/PedidoLoja";
import {ProdutoService} from "../services/produto.service";
import {ITela} from "../objeto/ITela";
import {ConstantsService} from "../services/ConstantsService";
import {ClienteService} from "../services/cliente.service";
import {DominiosService} from "../services/dominios.service";
import {MyDetectorDevice} from "../app/shared/MyDetectorDevice";
import {
  DialogCloseResult,
  DialogRef,
  DialogService,
  WindowCloseResult,
  WindowRef,
  WindowService
} from "@progress/kendo-angular-dialog";
import {Location} from "@angular/common";
import {SiteProdutoComponent} from "../site-produto/site-produto.component";
import {SiteMontarpizzaComponent} from "../site-montarpizza/site-montarpizza.component";
import {AutorizacaoLojaService} from "../services/autorizacao-loja.service";
import {BannerService} from "../services/banner.service";
import {PopupUtils} from "../objeto/PopupUtils";
import {PopupEnderecoChinaInboxComponent} from "../app/popup-endereco-china-inbox/popup-endereco-china-inbox.component";
import {HttpClient} from "@angular/common/http";
import {catchError, map} from "rxjs/operators";
import {Observable, of, Subscription} from "rxjs";
import {SharedDataService} from "../services/shared.data.service";
import {FormaDeEntrega} from "../objeto/FormaDeEntrega";
import {Endereco} from "../objeto/Endereco";
import {SiteWizardProdutosIaComponent} from "../app/produtos/site-wizard-produtos-ia/site-wizard-produtos-ia.component";
import {ProdutosTelaUtils} from "./ProdutosTelaUtils";
import {PainelLoginComponent} from "../app/painel-login/painel-login.component";
import {ValidarContatoZapComponent} from "../app/cad-contato/validar-contato-zap/validar-contato-zap.component";

declare var _;
declare var $;

@Component({
  selector: 'app-produtos',
  templateUrl: './site-produtos.component.html',
  styleUrls: ['./site-produtos.component.scss' , './produtos-categorias.scss']
})
export class SiteProdutosComponent implements OnInit, ITela, AfterViewInit, OnDestroy {
  @ViewChild('divProdutos', {static: false}) divProdutos: any;
  @ViewChild('siteCategorias', {static: true}) siteCategorias: any;

  @ViewChild('megamenu', {static: false}) megamenu: ElementRef;
  @ViewChild('wraper', {static: false})   wraper: ElementRef;

  produtos = [];
  vitrines = [];
  ultimosPratos = [];
  carregou: boolean;
  mensagemErro = '';
  pedido: PedidoLoja;
  carregando: boolean;
  categorias: any  = [];
  produtosPorCategoria: any = {}
  nomePagina: string;
  isMobile: any;
  observer: IntersectionObserver;
  estaFazendoScroll: boolean;
  window: DialogRef;
  codigoProduto: any;
  idTamanho: any;
  indiceProduto: any;
  codigopromo: any;
  hashMesa: string;
  mesaExpirada: boolean;
  layoutHorizontal: boolean;
  exibirMegaMenu: boolean
  larguraProdutos: number;
  empresa: any
  categoriasMegamenu: any;
  exibirVerMaisMegamenu = false;
  deslocamento = 0
  exibirMenuMobile = false;
  qtdeMaxima: number;
  movendo: boolean;
  categoriaMonteSuaPizza = {
    id: 1,  nome: ''
  };
  destaques =  [];
  nomeCategoriaDestaque = 'DESTAQUES';
  categoriaDestaque = {
    id: 2,
    nome: this.nomeCategoriaDestaque,
    imagem: null
  };
  alturaBanner = 230;

  banners = [];
  exibirPrecos = true;
  apiLoaded: Observable<boolean>;

  assinatura: Subscription;
  colunasMenu: any = {};
  evtEmpresa: Subscription;
  evtEmpresa2: Subscription;

  empresasDoGrupo = [];
  multiPedido = false;
  solicitandoGarcom: boolean;
  solicitandoFechar: boolean;
  mensagemSucessoGarcom: string;
  timerSolicitarGarcomNovamente: number;
   timerSolicitarFecharNovamente: number;

   ids = '';
  exibirMenuCategorias = false;
  evtCatalogo: any;
  categoriaSelecionada: any;
  exibirProdutosValorZerado = false;
  fidelidade: any;
  usuario: any;
  brindes: Array<any> = [];
  constructor(private router: Router, private sanitizer: DomSanitizer, private clienteService: ClienteService,
              private produtoService: ProdutoService, private carrinhoService: CarrinhoService,
              private autorizacaoLojaService: AutorizacaoLojaService, public sharedDataService: SharedDataService,
              private activatedRoute: ActivatedRoute, private constantsService: ConstantsService,
              private dominiosService: DominiosService, private detectorDevice: MyDetectorDevice,
              private dialogService: DialogService, private location: Location, private bannerService: BannerService,
              private httpClient: HttpClient, private autorizacao: AutorizacaoLojaService) {
    this.nomePagina = dominiosService.obtenhaRaizCardapio();
    this.isMobile = this.detectorDevice.isMobile();


    this.hashMesa = this.activatedRoute.snapshot.params.hm;
    this.codigopromo = this.activatedRoute.snapshot.params.codigopromo;
    this.dominiosService.hashMesa = this.hashMesa

    document.body.classList.add("ativar_fundo_tema");

    if( this.isMobile ) {
      this.alturaBanner = 100;
    }

    if(window['multipedido']) {
      this.multiPedido = true;
      const  tipoCardapio = (this.hashMesa || (this.pedido && this.pedido.mesa )) ? 'MESA' : 'DELIVERY';

      this.produtoService.listeAhVendaGrupo(tipoCardapio, false).subscribe( (empresas: any) => {
        this.empresasDoGrupo = empresas;
      });
    }

    this.apiLoaded = this.httpClient.jsonp('https://maps.googleapis.com/maps/api/js?' +
      'key=AIzaSyBlPtg98e0hBo0JJSYMB3R7iakhFe_iT4o&callback=initMap&libraries=&v=weekly', 'callback').pipe(
      map(() => {
        console.log('chamando');
        return true
      }),
      catchError((error) => {
        console.log(error);
        return of(false);
      }),
    );



    this.inicializePedido().then(() => {
      this.activatedRoute.queryParams.subscribe(params => {

        if( params['ids'] ) {
          this.ids = params['ids'];
        }
        if( params['nl']  )
          this.autorizacaoLojaService.atualizeUsuarioLogado().then(  () => {
            if(params['target'])
              this.router.navigateByUrl(params['target']);
          });

        if( params['c'] ) {
          this.codigoProduto = parseInt(params['c'], 10);
          if( isNaN(this.codigoProduto) )
            this.codigoProduto = null;
        }

        if( params['t'] ) {
          this.idTamanho = parseInt(params['t'], 10);
          if( isNaN(this.idTamanho) )
            this.idTamanho = null;
        }
        if( params['e'] ) {

          this.indiceProduto = parseInt(params['e'], 10);

          if( isNaN(this.indiceProduto) ) {
            this.indiceProduto = null;
          } else {
            this.indiceProduto = this.indiceProduto + '';
          }
        }
      });

      if (this.codigopromo)
        this.carrinhoService.atualizeValorDesconto(this.codigopromo, this.empresa, true).then( () => {});
    });

    this.sharedDataService.exibirMenu.subscribe(exibir => {
      this.exibirMenuMobile = exibir;
    });
  }

  public static abraPedindoEndereco(siteProdutos: SiteProdutosComponent, router: Router, location: Location, activatedRoute: ActivatedRoute,
                                    dialogService: DialogService, isMobile: boolean) {
    let dimensao = PopupUtils.calculeAlturaLarguraMenor(isMobile)

    const windowRef: DialogRef = dialogService.open({
      title: null,
      content: PopupEnderecoChinaInboxComponent,
      minWidth: 250,
      width: dimensao.largura,
      maxHeight: dimensao.altura,
      cssClass: 'bsModal'
    });

    let telaProduto: PopupEnderecoChinaInboxComponent = windowRef.content.instance;

    let params: any = {
    };

    telaProduto.window = windowRef;

    PopupUtils.abraJanela(router,  location, activatedRoute, windowRef, params);

    windowRef.result.subscribe((result: any) => {
      if( siteProdutos ) siteProdutos.exibirPrecos = true;
    });

    return windowRef;
  }

  ngAfterViewInit(): void {
    this.evtEmpresa2 = this.constantsService.empresa$.subscribe((empresa) => {
      if (!empresa)    return

      let params = this.activatedRoute.snapshot.params;

      this.empresa = empresa;
      this.exibirMenuCategorias = empresa.cardapio && empresa.cardapio.exibirSelecaoCategorias;

      const pedido: PedidoLoja = this.carrinhoService.obtenhaPedido();

      if( params.end ) {
        const endereco = Endereco.novo();
        Object.assign(endereco, JSON.parse(decodeURI(params.end)))

        this.pedido.entrega.formaDeEntrega = FormaDeEntrega.RECEBER_EM_CASA;
        this.pedido.entrega.setTaxaEntrega(endereco, null);
        this.carrinhoService.salvePedido(this.pedido);
      }

      const formaReceberEmCasa = this.obtenhaFormaDeEntregaReceberEmCasa(empresa);

      if (formaReceberEmCasa && formaReceberEmCasa.perguntarEnderecoInicio ) {
        if (!empresa.integracaoDelivery) {
          this.mensagemErro = 'Integração com Eclética tem que estar configurada';
        }

        if ((!pedido.entrega || !pedido.entrega.formaDeEntrega)) {
          this.exibirPrecos = false;
          SiteProdutosComponent.abraPedindoEndereco(this, this.router,
            this.location, this.activatedRoute, this.dialogService, this.isMobile);
        }
      }

      if (empresa.nomeCategoriaDestaques) {
        let nomeAnterior = this.categoriaDestaque.nome
        this.categoriaDestaque.nome = empresa.nomeCategoriaDestaques;
        this.categoriaDestaque.imagem = empresa.imagemCategoriaDestaque;
        this.produtosPorCategoria[this.categoriaDestaque.nome] = this.produtosPorCategoria[nomeAnterior]
      }

      this.bannerService.obtenhaBanners().then((banners) => {
        for (let i = 0; i < banners.length; i++) {
          const banner = banners[i];

          banner.thumbImage = banner.image = 'https://promokit.com.br/images/empresa/' + banner.linkImagem;
        }

        this.banners = banners;
      })

      this.layoutHorizontal = empresa.tipoDeLoja === 'MERCADO' || empresa.tipoDeLoja === 'ECOMMERCE';

      if (this.layoutHorizontal) {
        this.qtdeMaxima = 25;
      }

      if (this.layoutHorizontal) {
        if (this.isMobile) {
          this.larguraProdutos = ((this.divProdutos.nativeElement.clientWidth - 20) / 2);
        } else {
          this.larguraProdutos = ((this.divProdutos.nativeElement.clientWidth - 40) / 4);
        }
      }


      if( empresa.cardapio ) {
        const tipoDeCardapio = this.obtenhaTipoCardapio();

        this.exibirProdutosValorZerado = empresa.cardapio?.exibirProdutosValorZeradoMesa && tipoDeCardapio === 'MESA';
      }
    });
  }

  obtenhaFormaDeEntregaReceberEmCasa(empresa: any) {
    return empresa.formasDeEntrega.find( (forma) => {
      return forma.formaDeEntrega.nome === 'Receber em casa'
    });
  }

  ngOnDestroy(): void {
    if(this.evtEmpresa)
      this.evtEmpresa.unsubscribe();

    if(this.evtEmpresa2)
      this.evtEmpresa2.unsubscribe();

    if(this.evtCatalogo)
      this.evtCatalogo.unsubscribe();

    document.body.classList.remove("ativar_fundo_tema");
  }

  @HostListener('window:popstate', ['$event'])
  onPopState(event) {
    if( this.window ) {
      this.window.close({
        back: true
      });
    }
  }

  abraDetalhesProduto(produto: any, indiceProduto: any = null) {
    if(produto.naodisponivel) return;

    if(!produto.montar){
      this.window = SiteProdutoComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, this.isMobile, produto, indiceProduto , null , () => {
          this.setSaldoDisponivel();
        });
    } else {
      this.window =  SiteMontarpizzaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, this.isMobile, produto, indiceProduto);
    }
  }

  abraDetalhesPrato(itemPedido: any){
    this.window = SiteProdutoComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService, this.isMobile, itemPedido.produto, null, itemPedido);
  }

  obtenhaTipoCardapio(){
    return (this.hashMesa || (this.pedido && this.pedido.mesa )) ? 'MESA' : 'DELIVERY' ;
  }

  carregueCategoriasDepoisProdutos(){

   this.evtCatalogo =
     this.constantsService.obtenhaCarpadioCompleto( this.obtenhaTipoCardapio(), this.empresa).subscribe((cardapio: any) => {
      if(!cardapio) return;

      if(cardapio.categorias){
        this.categorias = cardapio.categorias;
        this.carregando = false;
      }

      if(cardapio.produtos){
        this.produtos = cardapio.produtos;
        this.agrupePorCategorias();
        this.setBrindesDisponiveis(cardapio.brindes);
      }

    })
  }


  agrupePorCategorias(){
    this.produtosPorCategoria = _.groupBy(this.produtos, produto => produto.categoria ? produto.categoria?.nome?.trim() : 'Outros');

    ProdutosTelaUtils.definaCategoriasFixas(this);
  }

  carregouProdutos(){
    this.produtos.forEach((produto: any) => {
      if( produto.tags.length &&  !produto.tags[0].nome){
        for(let i = 0 ; i <  produto.tags.length ; i++)
          produto.tags[i] =  this.empresa.tagsProdutos.find((item: any) => item.id ===  produto.tags[i].id)

      }

    })
    if(this.ids) {
      let dimensao = PopupUtils.calculeAlturaLargura(this.isMobile);

      const windowRef: DialogRef = this.dialogService.open({
        title: null,
        content: SiteWizardProdutosIaComponent,
        minWidth: 250,
        width: dimensao.largura,
        height: dimensao.altura
      });

      let telaProduto: SiteWizardProdutosIaComponent = windowRef.content.instance;
      telaProduto.ids = this.ids;
      telaProduto.categorias = this.categorias;
      telaProduto.produtos = this.produtos;
      telaProduto.window = windowRef;

      telaProduto.processe();

      PopupUtils.abraJanela(this.router, this.location, this.activatedRoute,
        windowRef, {}, null, 'dialog-produto-ia');

    }

    if(!this.constantsService.produtosLoja.length)
        this.constantsService.produtosLoja = this.produtos;

    if( this.layoutHorizontal ) {
      for (let i = 0; i < this.produtos.length; i++) {
        const produto = this.produtos[i];
        produto.destacado = true;
      }
    }

    this.agrupePorCategorias();

    this.categoriaSelecionada =  this.categorias.length ? this.categorias[0].nome : '';

    this.carregando = false;

    if( this.codigoProduto ) {
      const produto = this.obtenhaProduto(this.codigoProduto);
      if(produto)
        this.abraDetalhesProduto(produto, this.indiceProduto);
    } else if (this.idTamanho){
      let produtoCategoria =
        this.produtosPorCategoria[this.categoriaMonteSuaPizza.nome].find( produtoCategogia => produtoCategogia.id === this.idTamanho);

      this.abraDetalhesProduto(produtoCategoria, this.indiceProduto);
    }

    this.assinatura = this.activatedRoute.params.subscribe( (params2) => {
      const idCategoria = params2.idc;

      if(idCategoria){
        const categoria = this.categorias.find((cat: any) => cat.id === Number(idCategoria))

        if( categoria ) {
          setTimeout( () => {
            this.selecionou(categoria);
            this.assinatura.unsubscribe();
          }, 1000);
        }
      }
    });

  }

  ngOnInit() {
    this.carregando = true;
    this.autorizacao.usuario$.subscribe( (usuario) => {
      this.usuario = null
      if(usuario && usuario.id)
        this.usuario = usuario;

      this.setSaldoDisponivel();

    });

    this.evtEmpresa = this.constantsService.empresa$.subscribe( (empresa) => {
      if( !empresa ) { return }

      this.exibirMegaMenu = empresa.tipoDeLoja === 'ECOMMERCE';
      this.layoutHorizontal = empresa.tipoDeLoja === 'MERCADO' || empresa.tipoDeLoja === 'ECOMMERCE';

      if(  this.exibirMegaMenu ){
        this.produtoService.listeCategoriasMegamenu().then( (categoriasMegamenu) => {
          this.categoriasMegamenu = categoriasMegamenu;
          this.setColunasMegaMenu();
        }).catch(erro => {
          console.log(erro)
        })
      }

      this.produtoService.listeProdutosVitrine().then( (vitrines: any) => {
        this.vitrines = vitrines || [];

        if(this.vitrines.length &&  this.exibirMegaMenu ) {
          this.carregando = false;
          ProdutosTelaUtils.definaCategoriasFixas(this);
          return;
        }

        const  tipoCardapio =  this.obtenhaTipoCardapio();

        if(window.history.state.fezLogin)
          this.produtoService.removaDaCache(empresa, tipoCardapio);

        this.ultimosPratos = [];

        if(this.exibirMenuCategorias)  {
          this.carregueCategoriasDepoisProdutos();
          return;
        }

        this.produtoService.listeAhVenda(empresa, tipoCardapio, this.layoutHorizontal).subscribe( (resposta: any) => {
          this.produtos = resposta.produtos;
          this.categorias = resposta.categorias;
          if(resposta.ultimosPratos){
            resposta.ultimosPratos.forEach((itemPedido: any) => {
              //  produtoPedirNovamente.destacado = true;
              this.ultimosPratos.push(itemPedido);
            })
          }
          this.carregouProdutos();
          this.setBrindesDisponiveis(resposta.brindes );
        });
      });
    });
  }

 async setBrindesDisponiveis(brindes: any){
    if(this.isMobile || !brindes) return;
   if(this.empresa.integracaoPedidoFidelidade && this.empresa.integracaoPedidoFidelidade.resgatarBrinde){
     this.brindes = brindes.sort((a: any, b: any) =>  a.valorResgate - b.valorResgate);

     await this.setSaldoDisponivel();
   }
  }

  private async setSaldoDisponivel() {
    if(this.isMobile || !this.empresa || !this.empresa.exibirFidelidade || this.pedido.mesa  ) return;

    if(this.usuario)
      await this.autorizacao.atualizeSaldoFidelidade(this.usuario);

    let tipoAcumulo: string = this.empresa.integracaoPedidoFidelidade ?
      this.empresa.integracaoPedidoFidelidade.plano.tipoDeAcumulo : '';

    this.fidelidade  = { descricao: this.empresa.integracaoPedidoFidelidade.plano.nome,
      saldoDisponivel:   this.usuario ? this.usuario.saldoFidelidade : 0,
      cashback: tipoAcumulo === 'Reais' ? this.empresa.integracaoPedidoFidelidade.atividade.cashback * 100 : null,
      acumulo: tipoAcumulo === 'Selos' ? 'selos' : 'pts'
    }

    if(!this.usuario || this.usuario.fezLoginGuest) this.fidelidade.fazerLogin = true;

    if(this.fidelidade){
      this.fidelidade.saldoDisponivel = (this.usuario ? this.usuario.saldoFidelidade : 0) - this.pedido.totalResgatado;
      if(this.fidelidade.saldoDisponivel < 0)
        this.fidelidade.saldoDisponivel = 0

      this.brindes.forEach((item: any) => {
        item.naodisponivel =    item.valorResgate > this.fidelidade.saldoDisponivel;
        item.acumulo = this.fidelidade.acumulo
      })
    }

  }

  obtenhaProduto(id: number) {
   return this.produtos.find( produto => produto.id === id) || this.brindes.find( brinde => brinde.id === id)
  }

  getPosicao(nomeCategoria){
    let _produto = this.produtos.find( produto => produto.categoria && produto.categoria.nome.trim() === nomeCategoria.trim())

    let posicao = _produto ? _produto.categoria.posicao : this.produtos.length;

    return posicao;
  }

  deveExibirTopo() {
    return true;
  }

  deveExibirMenu(){
    return true;
  }

  abraTelaDeBusca() {
    const params: any = {state: {ehMesa: (this.hashMesa || (this.pedido && this.pedido.mesa ))}};

    let link = String(`/${this.nomePagina}/busca`);

    if(this.empresa.layoutEcommerce){
      link += '/ecommerce/todos'
      params.state.categorias = this.categoriasMegamenu;
    }

    this.router.navigateByUrl(link, {}).then( () => { });
  }

  abraTelaBuscaCategoria(categoria: any) {
    this.sharedDataService.exibirMenuMobile = false;

    this.router.navigateByUrl('/busca/ecommerce/' + categoria.id,
      {state: { categoria: categoria, categorias: this.categoriasMegamenu }  });

    return false;
  }

  abraTelaBuscaPorCategoria(categoria) {
    if(this.empresa && this.empresa.layoutEcommerce){
      this.abraTelaBuscaCategoria(categoria)
    } else {
      const produtos = this.produtosPorCategoria[categoria.nome];
      const mapProdutosPorCategoria = {};

      mapProdutosPorCategoria[categoria.nome] = produtos;

      const state = {state: {produtos: produtos,
          produtosPorCategoria: mapProdutosPorCategoria, categorias: [categoria],
          ehMesa: (this.hashMesa || (this.pedido && this.pedido.mesa ))}};

      this.router.navigateByUrl('/busca?cid=' + categoria.id, state).then( () => {});
    }

    return false;
  }

  voltar() {

  }


  getOffset(el) {
    const rect = el.getBoundingClientRect();
    return {
      left: rect.left + window.scrollX,
      top: rect.top + window.scrollY
    };
  }


  exibaProdutosCategoria(categoria: any){

  }

  selecionou(categoria) {
    this.categoriaSelecionada = categoria.nome;

    const idTab = 'tab_' + categoria.nome;
    const nomeCategoria = categoria.nome;

    this.posicioneTab(idTab);

    this.posicioneConteudo(nomeCategoria);

    let novaUrl = '/categoria/' +
      categoria.nome.toLowerCase().replace(/\s+/g, "-") + '/' + categoria.id + window.location.search;

    // this.location.replaceState(novaUrl);
    window.history.pushState({path: novaUrl}, '', novaUrl);

    return false;
  }

  posicioneTab(categoria) {
    categoria = categoria.trim();
    const tab = document.getElementById(categoria);

    if(tab){
      const $tabs = $("#tabs.nav-tabs");
      const posicao = tab.offsetLeft - ($tabs.width() / 2) + (tab.getBoundingClientRect().width / 2);

      $tabs.animate({scrollLeft: posicao}, 100);
    }

  }

  @HostListener('window:scroll', ['$event']) onScrollEvent($event){
    if( this.estaFazendoScroll ) {
      return;
    }

    const tabs = document.querySelectorAll('.categoria');

    for( let i = tabs.length - 1; i >= 0; i-- ) {
      const tab: any = tabs[i];

      const topElemento = this.getOffset(tab).top;
      const posicaoY = topElemento - document.querySelector('.header').clientHeight - 50;

      if( window.scrollY >= posicaoY ) {
        const idTab = 'tab_' + tab.innerText;

       // console.log(tab.innerText + ' <-> ' + this.categoriaSelecionada);
        if( this.categoriaSelecionada !== tab.innerText ) {
         // console.log(tab.innerText);
          this.categoriaSelecionada = tab.innerText;

          this.posicioneTab('tab_' + tab.innerText);
        }
        break;
      }
    }
  }

  posicioneConteudo(categoria) {
    categoria = categoria.trim();
    const topElemento = this.getOffset(document.getElementById(categoria)).top;
    const y = topElemento - document.querySelector('.header').clientHeight - 20;

    this.estaFazendoScroll = true;

    let posicao = -1;

    const chequeScroll = () => {
      if( posicao !== window.scrollY ) {
        posicao = window.scrollY;

        setTimeout(chequeScroll, 100);
        return;
      }

      this.estaFazendoScroll = false;
    }

    setTimeout(chequeScroll, 100);

    window.scroll({
      top: y,
      behavior: 'smooth'
    });
  }

  mudeAba() {
  }

  exibirUnidade(produto: any) {
    return produto.tipoDeVenda && produto.tipoDeVenda === 'Peso'
  }

  private inicializePedido() {
    let pedido =  this.carrinhoService.obtenhaPedido();

    if(pedido.mesa && pedido.mesa.expirada){
      this.mesaExpirada = true;
    } else {
      this.pedido = pedido;
    }

    return Promise.resolve();
  }

  moverScrollDireita(scroll: HTMLDivElement, categoria: HTMLDivElement) {
    if( this.movendo ) return;

    const posicao = scroll.scrollLeft;

    let largura = categoria.clientWidth;

    if( this.isMobile ) {
      largura = this.larguraProdutos + 10;
    }

    const novaPosicao = posicao + largura;

    this.movendo = true;
    scroll.scroll({
      left: novaPosicao,
      behavior: 'smooth'
    });

    categoria.classList.add('moveu');

    setTimeout( () => {
      this.movendo = false;

      const scrollWidth = scroll.scrollWidth;
      if (scrollWidth - largura === novaPosicao) {
        categoria.classList.add('scroll_fim');
      }
    }, 500);
  }

  moverScrollEsquerda(scroll: HTMLDivElement, categoria: HTMLDivElement) {
    if( this.movendo ) return;

    categoria.classList.remove('scroll_fim');

    const posicao = scroll.scrollLeft;

    let largura = categoria.clientWidth;

    if( this.isMobile ) {
      largura = this.larguraProdutos + 10;
    }


    let novaPosicao = posicao - largura;
    if( novaPosicao < 0 ) {
      novaPosicao = 0;
    }

    this.movendo = true;
    scroll.scroll({
      left: novaPosicao,
      behavior: 'smooth'
    });

    setTimeout( () => {
      if( novaPosicao === 0 ) {
        categoria.classList.remove('moveu');
      }
      this.movendo = false;
    }, 500);
  }



  clicouBanner(indice: any) {
    const banner = this.banners[indice];

    if( banner.comando === 'LINK' ) {
      window.open(banner.extra);
    } else if( banner.comando === 'BUSCA' ) {
      const state = {state: {produtos: this.produtos,
          produtosPorCategoria: this.produtosPorCategoria, categorias: this.categorias}};

      this.router.navigateByUrl('/' + this.nomePagina + '/busca?q=' + banner.extra, {}).then( () => {

      });
    }
  }

  deveTerBordas() {
    return true;
  }

  fecheMenuCategorias() {
    this.sharedDataService.exibirMenuMobile = false;
    return false;
  }



  private setColunasMegaMenu() {

    for(let categoria of this.categoriasMegamenu){
       this.colunasMenu[categoria.id] = {
          coluna1: { total: 0, subcategorias: []},
          coluna2: { total: 0, subcategorias: []},
          coluna3: { total: 0, subcategorias: []}
      }

      for(let i = 0 ; i <  categoria.subcategorias.length; i++){
        let item: any = this.colunasMenu[categoria.id];

        let coluna: any = this.obtenhaMaisVazia(item.coluna1, item.coluna2, item.coluna3)
        let subcategoria = categoria.subcategorias[i];
        coluna.subcategorias.push(subcategoria)

        let total = 1 + subcategoria.subcategorias.length;

        coluna.total += total; //so exibi 8 itens da subcategoria na tela
      }
    }
  }

  private obtenhaMaisVazia(categoriasColuna1: any, categoriasColuna2: any, categoriasColuna3: any){
    if(categoriasColuna1.total <= categoriasColuna2.total &&  categoriasColuna1.total <= categoriasColuna3.total )
      return categoriasColuna1;

    if(categoriasColuna2.total <= categoriasColuna3.total)
      return categoriasColuna2;


    return categoriasColuna3;
  }


  getColunas(categoria: any): any{
    return this.colunasMenu[categoria.id];
  }

  deveExibirBannerTema() {
    return true;
  }

  scrollParaDireita() {

    let tamanhoTotal = 0;

    for(let i = 0; i <  this.wraper.nativeElement.firstChild.children.length; i++)
      tamanhoTotal += this.wraper.nativeElement.firstChild.children[i].offsetWidth;

    let maximoScroll = tamanhoTotal - this.wraper.nativeElement.offsetWidth

    if(this.deslocamento <= maximoScroll  ) {
      this.deslocamento += 200;
    }
  }

  scrollParaEsquerda(){
    this.deslocamento -= 200;
    if(this.deslocamento  < 0 )
      this.deslocamento  = 0;

  }

  marginLeft(){
    return String(`-${this.deslocamento}px`);
  }

  paddingLeft(){ //padding-left
    return String(`${this.deslocamento}px`);
  }

  solicitouFecharMesa() {
    this.solicitandoFechar = true;

    this.carrinhoService.soliciteFecharMesa().then((resultado: any) => {
      this.timerSolicitarFecharNovamente = 180;

      this.inicieTimerSolicitarFecharNovamente()


      this.mensagemSucessoGarcom = 'Foi solicitado fechar a conta.'

    })

  }


  solicitouGarcom() {
    this.solicitandoGarcom = true;

    this.carrinhoService.soliciteGarcom().then((resultado: any) => {
      this.timerSolicitarGarcomNovamente = 180;

      this.inicieTimerSolicitarGarcomNovamente()

      this.mensagemSucessoGarcom = 'Garçom solicitado com sucesso.'

    })

  }

  fecheMensagemSucessoGarcom() {
    this.mensagemSucessoGarcom = null;
  }

  private inicieTimerSolicitarGarcomNovamente() {
    setTimeout(() => {
        this.timerSolicitarGarcomNovamente--;

        if (this.timerSolicitarGarcomNovamente > 0)
          this.inicieTimerSolicitarGarcomNovamente();
        else
          this.solicitandoGarcom = false;
          },
      1000)
  }

  private inicieTimerSolicitarFecharNovamente() {
    setTimeout(() => {
        this.timerSolicitarFecharNovamente--;

        if (this.timerSolicitarFecharNovamente > 0)
          this.inicieTimerSolicitarFecharNovamente();
        else
          this.solicitandoFechar = false;
      },
      1000)
  }


  irParaLogin(){
    if(this.isMobile){
      const telafone: string  =   this.pedido && this.pedido.contato ?  this.pedido.contato.telefone : null;
      this.dominiosService.vaParaLogin(window.location.pathname, telafone)
    } else {
      PainelLoginComponent.abraComoPopup(this.router, this.location, this.activatedRoute,  this.dialogService, null, () => {
        this.setSaldoDisponivel();
      } );
    }

    return false;
  }

  irParaVerSaldo(){
    if(this.isMobile){
      this.dominiosService.vaParaValidarLogin(window.location.pathname, this.pedido.contato)
    } else {
      ValidarContatoZapComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService,  this.pedido.contato , (result) => {
          if(result && result.login) {
            this.setSaldoDisponivel();
          }
        });
    }

    return false;
  }
}
