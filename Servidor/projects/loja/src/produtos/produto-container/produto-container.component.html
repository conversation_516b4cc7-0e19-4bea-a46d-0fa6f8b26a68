<div #divProduto *ngIf="exibirProdutoValorZerado || (produto.preco > 0 || produto.valorMinimo > 0)" (click)="abrirDetalhes(produto)"
     class="produto pt-2"  [style.width.px]="larguraProdutos"
     [class.destacado]="produto.destacado && !semdestaque"   [class.destaque2]="destaque2"  >
  <div class="container_foto justify-content-center align-items-center wrapper-img" *ngIf="((produto.imagemCodigoDeBarras)|| (produto.imagens && produto.imagens.length > 0))">
    <img *ngIf="!produto.imagemCodigoDeBarras" class="img img-fluid mb-1" [src]="'https://fibo.promokit.com.br/images/empresa/' + produto.imagens[0].linkImagem" alt="Imagem"/>
    <img *ngIf="produto.imagemCodigoDeBarras" class="img img-fluid mb-1" [src]="'https://fibo.promokit.com.br/images/produtos/' + (produto.imagemCodigoDeBarras.linkImagem ? produto.imagemCodigoDeBarras.linkImagem : 'carrinho-produtos.svg' )" alt="Imagem"/>
  </div>

  <div class="media">
    <div class="media-body pt-0">
      <ng-container *ngIf="!itemPedido">
        <h5 class="mt-0 mb-1 font-14 nome-produto">{{produto.nome}}  </h5>

        <span class="text-muted descricao" [hidden]="itemPedido?.adicionais?.length" [ngClass]="{'descricao_menor': botaoEscolher}">
        {{produto.descricao}}</span>


        <app-tags-alimentar [tags]="produto.tags"  *ngIf="produto.tags?.length"></app-tags-alimentar>


      </ng-container>

      <ng-container *ngIf="itemPedido">
        <h5 class="mt-0 mb-1 font-14 nome-produto">
          {{itemPedido.qtde}}{{itemPedido.obtenhaUnidade()}}
          {{itemPedido.obtenhaDescricao()}}

        </h5>

        <div class="text-muted  descricao" >
           <span  class="d-block" *ngFor="let adicional of adicionaisImprirmir">
             {{adicional.qtde}}x {{adicional.nome}}
           </span>
        </div>

        <h5 class="preco font-14" *ngIf="!produto.indisponivel" style="color: var(--cor-preco); font-weight: 700;">
          {{ itemPedido.total | currency: 'BRL'}}
        </h5>

      </ng-container>

      <ng-container *ngIf="!produto.indisponivel && produto.exibirPrecoNoCardapio">
        <div>
          <ng-container *ngIf="!produto.template?.exibirPrecosTamanhos && !itemPedido">
            <span class=" text-muted font-12 mr-1"  *ngIf="produto.valorMinimo"><i>A partir de</i></span>
            <h5 class="preco font-14" *ngIf="exibirPrecos" style="color: var(--cor-preco); font-weight: 700;">

              <ng-container *ngIf="!produto.precoInical">
                {{ (produto.valorMinimo ? produto.valorMinimo : produto.preco) | currency: 'BRL'}}

                <span class=" text-muted font-11"  *ngIf="exibirUnidade(produto)" > /  Kg </span>
              </ng-container>

              <ng-container *ngIf="produto.precoInical">
                {{ produto.precoInical | currency: 'BRL'}} / {{produto.valorInicial}} {{produto.unidadeMedida.sigla}}
              </ng-container>

            </h5>

            <h5 class="preco font-14 antigo" *ngIf="produto.precoAntigo && exibirPrecos" style="color: var(--cor-preco); font-weight: 700;">
              {{(produto.precoAntigo) | currency: 'BRL'}}
            </h5>

            <div *ngIf="produto.cashbackValor" class="cashback-container">
              <i class="fe-star-on"></i>
              Ganhe {{produto.cashbackValor | currency: 'BRL'}} em cashback
            </div>
          </ng-container>

          <ng-container *ngIf="produto.template?.exibirPrecosTamanhos && !itemPedido ">
            <div *ngFor="let tamanho of produto.tamanhos" class="mt-2">
              {{tamanho.descricao}}

              <span class=" text-muted font-12 mr-1"   *ngIf="tamanho.valorMinimo"><i>A partir de</i></span>

              <h5 class="preco font-14 mt-0 mb-0" *ngIf="exibirPrecos" style="color: var(--cor-preco); font-weight: 700;">
                {{ (tamanho.novoPreco ? tamanho.novoPreco : tamanho.preco) | currency: 'BRL'}}

              </h5>
              <h5 class="preco font-14 antigo" *ngIf="tamanho.precoAntigo && exibirPrecos" style="color: var(--cor-preco); font-weight: 700;">
                {{(tamanho.precoAntigo) | currency: 'BRL'}}
              </h5>
            </div>

          </ng-container>

          <i *ngIf="produto.qtdMaxima"><label class="text-muted" style="font-size: 10px">*Até {{produto.qtdMaxima}} por pedido</label></i>

          <div *ngIf="botaoEscolher">
            <button class="btn btn-sm btn-primary">Escolher</button>
          </div>
        </div>
      </ng-container>

      <h5 *ngIf="produto.indisponivel" class="text-danger">Indisponível!</h5>

    </div>
  </div>
</div>
