import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {ClienteService} from "../services/cliente.service";
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";
import {TopoComponent} from "../topo/topo.component";
import {ConstantsService} from "../services/ConstantsService";
import {MyDetectorDevice} from "../app/shared/MyDetectorDevice";
import {ActivatedRoute, NavigationEnd, Router} from "@angular/router";
import {PedidoLoja} from "../objeto/PedidoLoja";
import {CarrinhoService} from "../services/carrinho.service";
import {LojaMenuComponent} from "../app/loja-menu/loja-menu.component";
import {DominiosService} from "../services/dominios.service";

let sticky ;

@Component({
  selector: 'app-pagina',
  templateUrl: './pagina.component.html',
  styleUrls: ['./pagina.component.scss']
})
export class PaginaComponent implements OnInit {
  @ViewChild('carrinho') carrinho: any;
  @ViewChild('lojaMenu') lojaMenu: LojaMenuComponent;
  @ViewChild('topo', {static: false}) topo: TopoComponent;
  @ViewChild('topoDesktop', {static: false}) topoDesktop: any;
  @ViewChild('stickyCarrinho') stickyCarrinho: ElementRef;
  empresa: any = {};
  carregou = false;
  private urlMaps: SafeResourceUrl;
  mensagemErro: string;
  exibirTopo = true;
  telaMultiLoja = false;
  exibirMenu = true;
  finalizarPedido = false;
  isDesktop: boolean;
  hashMesa: string;
  pedido: PedidoLoja;
  mesaExpirada: boolean;
  tipoCardapio = '';
  aceitouCookies = false;
  temBordas = true;
  exibirBannerTema = false;
  topoReduzido = false;
  exibirTitulo = true;
  nomePagina: string;
  estaNaTelaTabletPedidos = false;
  constructor(private clienteService: ClienteService, private activatedRoute: ActivatedRoute,
              private deviceService: MyDetectorDevice, private dominiosService: DominiosService,
              private sanitizer: DomSanitizer, private constantsService: ConstantsService,
              private carrinhoService: CarrinhoService, private router: Router) {
    this.nomePagina = dominiosService.obtenhaRaizCardapio();
    this.exibirTitulo = !window['telaMultiLoja'] || this.router.url.indexOf('/loja') === -1;
    this.telaMultiLoja = window['telaMultiLoja'];

    if(this.telaMultiLoja)
      this.constantsService.recarregueEmpresa();

    this.carrinhoService.setContexto(window['multipedido']);

    this.activatedRoute.queryParams.subscribe( (params) => {
      if( params.op ) {
        localStorage.setItem('operador', params.op)
      }
    });

    this.aceitouCookies = localStorage.aceitouCookies === 'true';
    if( window['Flutter'] || window['flutter_inappwebview'] || this.router.url.includes('pedido-tablet') ) {
      this.aceitouCookies = true;
    }
  }

  setDesktop(){
    const fullUrl = this.router.url;
    if(fullUrl.indexOf('3ds/retorno') >= 0 || fullUrl.indexOf('pedido-tablet') >= 0) {
      this.isDesktop = false;
      return;
    }

    if( this.empresa.tema !== 'quiosque' && (this.deviceService.isMobile() || this.deviceService.isTablet()) ) {
      this.isDesktop = false;
      document.body.classList.add("mobile");
    } else {
      document.body.classList.add("desktop");
      this.isDesktop = true;
    }
  }

  ngOnInit() {
    // Verificar se está na tela de tablet-pedidos
    this.estaNaTelaTabletPedidos = this.verificarTelaTabletPedidos();

    this.constantsService.empresa$.subscribe( (empresa) => {
      if(empresa){
        this.empresa  = empresa;
        this.setDesktop();
        this.adicioneTemaNoBody(empresa);
        this.lojaMenu.exibirMenuCategorias =  this.empresa && this.empresa.layoutEcommerce;

        this.hashMesa = this.activatedRoute.firstChild.snapshot.params.hm;

        this.inicializePedido().then( () => {
          this.tipoCardapio = (this.hashMesa || (this.pedido && this.pedido.mesa )) ? 'MESA' : 'DELIVERY';
        });

        if(this.isDesktop) {
          window.onscroll =  () => {
            sticky = this.topoDesktop.obtenhaAltura();

            this.setDesktop();
            if(!this.stickyCarrinho) return;

            if (window.pageYOffset > (sticky + 150) ) {
              this.stickyCarrinho.nativeElement.classList.add("is-sticky");
            } else {
              this.stickyCarrinho.nativeElement.classList.remove("is-sticky");
            }
          };

        } else {
          setTimeout( () => {this.topo.carregueDados() }, 0)
        }
        this.urlMaps = this.sanitizer.bypassSecurityTrustResourceUrl(this.empresa.linkMaps);
      }

    });
  }

  private inicializePedido() {
    let pedido =  this.carrinhoService.obtenhaPedido();

    if(pedido.mesa && pedido.mesa.expirada){
      this.mesaExpirada = true;
    } else {
      this.pedido = pedido;
    }

    return Promise.resolve();
  }

  onActivate(componente: any) {
    this.exibirTopo = componente.deveExibirTopo();
    this.exibirMenu =  componente.deveExibirMenu();
    this.temBordas = componente.deveTerBordas();
    this.finalizarPedido = this.exibirCarrinhoDesktop();
    this.exibirBannerTema = componente.deveExibirBannerTema();
    if( componente.topoDeveSerReduzido ) {
      this.topoReduzido = componente.topoDeveSerReduzido();
    }
    window.scrollTo(0, 0);

    setTimeout( () => {
      if( this.topoDesktop)
        this.topoDesktop.carregaDados();

      if(this.lojaMenu)
        this.lojaMenu.veriqueMenuAtivo()

      if(this.carrinho)
        this.carrinho.verifiqueDeveExibir();
    }, 0);
  }

  exibirCarrinhoDesktop(): boolean{
    return location.pathname.endsWith('/pedido') ||
      location.pathname.indexOf('/pedido/acompanhar') >= 0
    || location.pathname.indexOf('pedido-totem') >= 0;
  }

  concordoCookies() {
    localStorage.aceitouCookies = true;
    this.aceitouCookies = true;
  }

  // Verifica se está na tela de tablet-pedidos
  verificarTelaTabletPedidos() {
    return this.router.url.includes('pedido-tablet') || document.body.classList.contains('tablet-pedidos-ativo');
  }

  private adicioneTemaNoBody(empresa: any) {
    if( empresa.tema ) {
      document.body.classList.add(window['tema']);
    }
  }

  abraTelaDeBrindes(){
    let link = String(`/${this.nomePagina}/brindes`);
    const params: any = {state: {   ehMesa: (this.hashMesa )}};

    this.router.navigateByUrl(link, params).then( () => { });
  }



  abraTelaDeBusca($event: any) {
    const params: any = {state: {   ehMesa: (this.hashMesa )}};

    let link = String(`/${this.nomePagina}/busca`);

    if(this.empresa.layoutEcommerce){
      link += '/ecommerce/todos'
      //params.state.categorias = this.categoriasMegamenu;
    }

    this.router.navigateByUrl(link, params).then( () => { });
  }
}
