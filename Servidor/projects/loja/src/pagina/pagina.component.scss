.barra-cookies {
  position: fixed;
  bottom: 0px;
  background: #333;
  z-index: 9999;
  color: #fff;
  width: 100%;
}

.desktop {
  .barra-cookies {
    font-size: 16px;
  }
}

.container{
  position: relative;
  max-width: 100%;
  display: flex;

  &.remover-padding {
    padding: 0px;
  }

  .principal {
    width: 100%;
    background: #fff;
  }

  &.mobile {
    display: block;
    max-width: none;
    position: relative;
    width: 100%;

    .aviso-abertura {
      margin-top: 10px;
      max-width: 500px;
    }
  }

  .carrinho_desktop {
    display: none;
    height: 100%;
    right: 0px;
    flex-basis: 452px;
    z-index: 1000;
  }
}

.black_friday_2022 {
  background: #0e0e0e !important;
  .principal {
    background: #0e0e0e !important;
  }
}

.cacau_show {
  background: var(--cor-fundo-site) !important;

  .principal {
    background: var(--cor-fundo-site) !important;
  }

  .aviso-abertura {
    background-color: var(--cor-fundo-elementos) !important;
    color: var(--cor-texto-primaria) !important;
    border: none !important;

    .destaque {
      color: var(--cor-destaque) !important;
    }
  }

  .rodape {
    background-color: var(--cor-fundo-elementos) !important;
    border-top: 1px solid var(--cor-borda) !important;

    .texto-rodape {
      color: var(--cor-texto-secundaria) !important;
    }

    .links {
      a {
        color: var(--cor-texto-primaria) !important;

        &:hover {
          color: var(--cor-destaque) !important;
        }
      }
    }
  }
}

.carnaval{
  .aviso-abertura{
    background: #4ab8b8 !important;
    color: #fce335 !important;
  }
}

.footer {
  border-top: solid 1px #eeeeee;
  padding: 0px;
  position: fixed;
  z-index: 99999;
  background: #f1f5f7;
  z-index: 10;
  left: 0px !important;

  &.carnaval{
    ::ng-deep .carrinho{
      background: #4bb8b8 !important;
    }
  }

  &.chinainbox{
    ::ng-deep .carrinho{
      background: #e52f26 !important;
    }
  }

  &.quiosque{
    display: block;

    ::ng-deep .carrinho{
      background: #3E9920 !important;
    }
  }
}

.aviso-abertura{
  position: relative;
  width: 100%;
  border-radius: 0;
  margin-top: 10px;
  max-width: 250px;
  padding: 0px 30px !important;
}

@media (min-width: 1024px) {
  .container{
    .principal {
      max-width: calc(100% - 442px);
      flex-basis: 100%;
      width: 100%;

      .conteudo{
        max-width: 1080px;
        width: 100%;
        float: right;
        min-height: calc(100vh - 240px);
        padding-right: 15px;
        padding-left: 15px;

        &.overflow-x-hidden{
          overflow-x: hidden;
          padding-left: 0px;
        }
      }
    }

    &.finalizar{
      padding-left: 0px !important;
      .principal{
        max-width: 1100px;
        margin: 0 auto;
      }
    }

    .carrinho_desktop {
      display: flex !important;
      background-color: #f7f8f8;
      z-index: 9999;

      .sticky{
        right: 0px;
        background-color: #f7f8f8;
        border-right: solid 1px #efefef;
        border-left: solid 1px #e2e2e2;
        position: absolute;

        &.is-sticky{
          position: fixed;
          top: 0px;
          background-color: #f7f8f8;
          border-left: solid 1px #e2e2e2;
        }
      }

      .carrinho{
        height: 100vh;
        width: 452px;
        padding: 0 20px;
      }
    }

    &.quiosque {
      .conteudo {
        max-width: 100% !important;
      }
      .principal {
        max-width: 100% !important;
      }

      .carrinho_desktop {
        display: none !important;
      }
    }
  }

  .footer {
    position: initial !important;
    margin-top: 30px;
    display: none;

    &.quiosque{
      position: fixed !important;
    }
  }
}

a {
  color: #fff;
}

::ng-deep body {
  &.black_friday_2022 {
    background-color: #0e0e0e !important;

    .container {
      .carrinho_desktop {
        background-color: #0e0e0e !important;

        .sticky, .is-sticky {
          background-color: #0e0e0e !important;
          border-right: solid 1px #1a1a1a !important;
          border-left: solid 1px #1a1a1a !important;
        }
      }
    }
  }
}

::ng-deep .dialog-produto {
  overflow-x: hidden;
  padding-bottom: 80px !important;
  padding-top: 0px !important;
  position: initial;
}

::ng-deep .mobile .dialog-produto {
  padding: 12px;
}

::ng-deep .k-dialog-wrapper {
  z-index: 100001;
}

/* Apenas para telas até 768px (tablets e móveis) */
@media (max-width: 768px) {
  ::ng-deep .k-dialog-wrapper {
    z-index: 100001;
    display: block;
  }
}

::ng-deep .ng-image-fullscreen-view {
  z-index: 99999 !important;
}

::ng-deep .black_friday_2022{
  .dialog-produto {
    background-color: #0e0e0e !important;
  }

  &.desktop .dialog-produto {
    box-shadow: 0 10px 20px 0 rgb(0 0 0 / 25%);
    border: solid 1px hsla(0,0%,100%,.12)!important;
  }
}

::ng-deep .cacau_show {
  .dialog-produto {
    background-color: var(--cor-fundo-site) !important;
  }

  &.desktop .dialog-produto {
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.25);
    border: solid 1px color-mix(in srgb, var(--cor-fundo-site) 80%, white 20%) !important;
  }

  .container {
    .carrinho_desktop {
      background-color: var(--cor-fundo-site) !important;

      .sticky, .is-sticky {
        background-color: var(--cor-fundo-site) !important;
        border-right: solid 1px var(--cor-fundo-elementos) !important;
        border-left: solid 1px var(--cor-fundo-elementos) !important;
      }
    }
  }

  header {
    box-shadow: 0 1px 0 var(--cor-fundo-elementos) !important;
  }

  a {
    color: var(--cor-texto) !important;
  }
}

::ng-deep .quiosque {
  @media (min-width: 1024px) {
    .container {
      .principal {
        max-width: calc(100% - 342px) !important;
      }

      .carrinho {
        width: 352px !important;
        padding: 0px 10px !important;
      }

      .carrinho_desktop {
        flex-basis: 352px !important;
      }
    }
  }
}

::ng-deep .tema-personalizado {
  background: var(--cor-fundo-site, #fff) !important;

  .principal {
    background: var(--cor-fundo-site, #fff) !important;
  }

  header {
    box-shadow: none !important;
    border-bottom: solid 1px var(--cor-borda) !important;
  }

  .container {
    .carrinho_desktop {
      background-color: var(--cor-fundo-site, #fff) !important;

      .sticky, .is-sticky {
        background-color: var(--cor-fundo-site, #fff) !important;
        border-right: solid 1px var(--cor-borda) !important;
        border-left: solid 1px var(--cor-borda) !important;
      }
    }
  }

  .dialog-produto {
    background-color: var(--cor-fundo-site) !important;
  }

  &.desktop .dialog-produto {
    box-shadow: 0 10px 20px 0 var(--cor-fundo-site, #fff) !important;
    border: solid 1px color-mix(in srgb, var(--cor-fundo-site) 80%, white 20%) !important;
  }
}

