import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ConstantsService } from '../../services/ConstantsService';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-powered-by',
  templateUrl: './powered-by.component.html',
  styleUrls: ['./powered-by.component.scss']
})
export class PoweredByComponent implements OnInit, OnDestroy {
  exibirPoweredBy = true;
  private empresaSubscription: Subscription;

  constructor(private constantsService: ConstantsService) { }

  ngOnInit(): void {
    this.empresaSubscription = this.constantsService.empresa$.subscribe((empresa) => {
      if (empresa) {
        this.exibirPoweredBy = empresa.exibirPoweredBy !== false;
      }
    });
  }

  ngOnDestroy(): void {
    if (this.empresaSubscription) {
      this.empresaSubscription.unsubscribe();
    }
  }

}
