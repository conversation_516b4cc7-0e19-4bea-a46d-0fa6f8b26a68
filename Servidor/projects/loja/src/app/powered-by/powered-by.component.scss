.powered-by {
  height: calc(16px + env(safe-area-inset-bottom) + 10px);
  display: flex;
  align-items: baseline;
  justify-content: center;
  background: transparent;
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: -1;
  color: var(--cor-texto, #6c757d) !important;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  letter-spacing: 0.5px;
  text-shadow: none;
  z-index: 999999999 !important;
}


.powered-by {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  font-size: 12px;

  a {
    display: flex;
    align-items: center;
    color: #6c757d;
    text-decoration: none;

    &:hover {
      color: #495057;
    }

    img {
      vertical-align: middle;
    }
  }

  i {
    display: inline-flex;
    align-items: center;
  }
}
