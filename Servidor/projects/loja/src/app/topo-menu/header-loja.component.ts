import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Router} from "@angular/router";
import {MyDetectorDevice} from "../shared/MyDetectorDevice";
import {DominiosService} from "../../services/dominios.service";
import {Subject, Subscription} from "rxjs";
import { debounceTime } from 'rxjs/operators';

@Component({
  selector: 'app-header-loja',
  templateUrl: './header-loja.component.html',
  styleUrls: ['./header-loja.component.scss']
})
export class HeaderLojaComponent implements OnInit {
  @Input() titulo;
  @Input() tema;
  @Input() busca;
  @Input() focarBusca = true;
  @Input() public retorno = 'index';
  @Output() evtVoltar = new EventEmitter();

  @Output()
  public mudouValorBusca: EventEmitter<any> = new EventEmitter<any>();

  @Input()
  public valueChangeDelay = 300;

  private stream: Subject<any> = new Subject<any>();
  private subscription: Subscription;

  query: any = '';
  isMobile  = false;
  nomePagina: string;
  constructor(private router: Router,  private deviceService: MyDetectorDevice,
              private dominiosService: DominiosService) {
    this.isMobile = this.deviceService.isMobile() || this.deviceService.isTablet();

    this.nomePagina = dominiosService.obtenhaRaizCardapio()
    this.subscription = this.stream
      .pipe(debounceTime(this.valueChangeDelay))
      .subscribe((value: any) => {
          this.mudouValorBusca.next(value)
      }
      );
  }

  ngOnInit(): void {
    if( window['tema'])
      this.tema = window['tema'];
  }

  voltar() {
    if( this.evtVoltar.observers.length > 0 ) {
      this.evtVoltar.emit();
      return;
    }

    this.dominiosService.navegueParaUrl(this.retorno)
  }

  limpe() {
    this.query = '';
    this.mudouValorBusca.next('')
  }
  public onValueChange(value: any): void {
    this.stream.next(this.query)

  }
}
