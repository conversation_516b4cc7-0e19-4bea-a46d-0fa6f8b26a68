<div class="row pt-2" style="position: relative" class="{{empresa.tema}}" >

  <div class="col-12 card panel-finalizar" style="background: var(--cor-fundo-elementos, #fff); border-color: var(--cor-borda, #dee2e6);">

    <div class="card-body">

      <div>
        <h3 class="pt-2 card-title" style="color: var(--cor-texto-primaria, inherit);">Finalizar pedido<ng-container *ngIf="apenasAgendamento"> (apenas agendamento)</ng-container></h3>

        <form class="k-form" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
              novalidate #frm="ngForm" (ngSubmit)="fazerPedido()"  [hidden]="pedido.codigo">

          <ng-container *ngIf="camposAdicionais && camposAdicionais.length">
            <h4 class="mb-2" style="color: var(--cor-texto-primaria, inherit);">Extras do Pedido</h4>

            <div *ngFor="let campoAdicional of camposAdicionais; let posicao = index">
              <app-site-campo-adicional #adicionalComponent [id]="'adicional_' + campoAdicional.id" [campoAdicional]="campoAdicional"
                                        [itemPedido]="pedido" [posicao]="campoAdicional.posicao"
                                        (onDesmarcouOpcao)="desmarcouNovaOpcao($event)"  (onMarcouOpcao)="escolheuNovaOpcao($event)">

              </app-site-campo-adicional>
            </div>

            <hr class="linha" style="border-color: var(--cor-borda, #dee2e6);">
          </ng-container>


          <ng-container *ngIf="!pedido.mesa">
            <h4 class="mt-3" style="color: var(--cor-texto-primaria, inherit);">Entrega</h4>

            <p *ngIf="empresa.id && !empresa.formasDeEntrega.length" class="text-muted" style="color: var(--cor-texto-secundaria, #6c757d) !important;">Nenhuma forma de entrega está disponível no momento</p>
            <div class="form-group mb-2 escolher">
              <ng-container *ngFor="let formaEntrega of formasDeEntrega;let i = index;">
              <span [ngClass]="{'selecionado': pedido.entrega && pedido.entrega.formaDeEntrega ===formaEntrega.nome}" class="radio radio-blue mb-2"
                    *ngIf="formaEntrega.ativa" style="background-color: var(--cor-fundo-elementos, #f7f8f8);">
                  <input id="formaDeEntrega{{i}}" name="formaDeEntrega{{i}}" type="radio" class="k-radio" kendoRadioButton
                         (change)="alterouFormaEntrega(formaEntrega)"
                         [(ngModel)]="pedido.entrega.formaDeEntrega" [value]="formaEntrega.nome"
                         [required]="true"/>
                  <label for="formaDeEntrega{{i}}" class="ml-1 d-inline" style="color: var(--cor-texto-primaria, inherit);">{{formaEntrega.nome}}</label>
              </span>
              </ng-container>

              <div class="invalid-feedback" *ngIf="!pedido.entrega.formaDeEntrega">
                <p>Escolha a forma de retirada do seu pedido</p>
              </div>
            </div>

            <div class="form-group col" *ngIf=" pedido.entrega?.ehRetirada()">
              <div class="alert alert-danger" *ngIf="!pedido.temValorMinimo(FormaDeEntrega.RETIRAR, empresa.formasDeEntrega)" style="border-color: var(--cor-borda, #f5c6cb); background-color: var(--cor-fundo-elementos, #f8d7da);">
                <p style="color: var(--cor-texto-primaria, #721c24);">Valor mínimo do pedido para retirar é de
                  {{pedido.obtenhaValorMinimoParaEntrega(FormaDeEntrega.RETIRAR, empresa.formasDeEntrega) | currency: "BRL"}} </p>
              </div>

              <div class="alert alert-danger" *ngIf="pedido.ultrapassouValorMaximo(FormaDeEntrega.RETIRAR, empresa.formasDeEntrega)" style="border-color: var(--cor-borda, #f5c6cb); background-color: var(--cor-fundo-elementos, #f8d7da);">
                <p style="color: var(--cor-texto-primaria, #721c24);">Valor máximo do pedido para retirar é de
                  {{pedido.obtenhaValorMaximoParaEntrega(FormaDeEntrega.RETIRAR, empresa.formasDeEntrega) | currency: "BRL"}} </p>
              </div>

            </div>

            <div class="form-group col mt-2" *ngIf="pedido.entrega?.ehDelivery()">
              <label style="color: var(--cor-texto-primaria, inherit);">Endereço</label>

              <div *ngIf="pedido.temValorMinimo(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega) &&
                            !pedido.ultrapassouValorMaximo(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega)">
                <i class="fe-map-pin fa-lg" aria-hidden="true" style="color: var(--cor-destaque, inherit);"></i>

                <a class="text-blue cpointer ml-2" *ngIf="!pedido.entrega?.endereco" (click)="abraModalEscolherEndereco()" style="color: var(--cor-botao, #007bff);"><b>Adicionar</b></a>

                <span *ngIf="pedido.entrega?.endereco" style="color: var(--cor-texto-primaria, inherit);">

                 <div class="d-inline ml-2">
                    {{pedido.entrega.endereco.obtenhaEnderecoCompleto()}}
                   <a class="text-blue cpointer ml-2" (click)="abraModalEscolherEndereco()" style="color: var(--cor-botao, #007bff);"><b>Trocar</b></a>
                 </div>
               </span>
              </div>

              <div class="alert alert-danger" *ngIf="empresa.id && !pedido.temValorMinimo(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega)" style="border-color: var(--cor-borda, #f5c6cb); background-color: var(--cor-fundo-elementos, #f8d7da);">
                <p style="color: var(--cor-texto-primaria, #721c24);">Valor mínimo do pedido para entrega é de
                  {{pedido.obtenhaValorMinimoParaEntrega(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega) | currency: "BRL"}} </p>
              </div>

              <div class="alert alert-danger" *ngIf="empresa.id && pedido.ultrapassouValorMaximo(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega)" style="border-color: var(--cor-borda, #f5c6cb); background-color: var(--cor-fundo-elementos, #f8d7da);">
                <p style="color: var(--cor-texto-primaria, #721c24);">Valor máximo do pedido para entrega é de
                  {{pedido.obtenhaValorMaximoParaEntrega(FormaDeEntrega.RECEBER_EM_CASA, empresa.formasDeEntrega) | currency: "BRL"}} </p>
              </div>


            </div>

         </ng-container>

          <span [hidden]="!pedido.temValorMinimoDaFormaEscolhida(empresa.formasDeEntrega) || pedido.ultrapassouValorMaximoDaFormaEscolhida(empresa.formasDeEntrega)">
            <ng-container *ngIf="!pedido.mesa">
              <div class="col mb-3" *ngIf="pedido.entrega && pedido.entrega.taxaDeEntrega >= -1">
                <p class="mb-0" style="color: var(--cor-texto-primaria, inherit);">
                  Taxa de Entrega
                </p>
                <h5 class="mt-1" *ngIf="pedido.entrega.taxaDeEntrega !== -1" style="color: var(--cor-preco, #6db31b); font-weight: 700;">
                  {{pedido.obtenhaValorTaxaEntrega() | currency: 'BRL'}}
                </h5>
                <h5 class="mt-1" *ngIf="pedido.entrega.taxaDeEntrega === -1" style="color: var(--cor-texto-primaria, inherit);">
                  A Informar
                </h5>
              </div>
             <ng-container *ngIf="pedido.entrega">
                <app-agendar-entrega [tela]="this" [pedido]="pedido" [empresa]="empresa"
                                     [agendarEntrega]="agendouEntrega || apenasAgendamento"
                                     [apenasAgendamento]="apenasAgendamento"
                                      [formasDeEntrega]="formasDeEntrega">

                </app-agendar-entrega>


                <div class="invalid-feedback" *ngIf="agendarEntrega && (!pedido.dataEntrega || !pedido.horarioEntrega)">
                  <p>É necessário escolher o dia e horário da entrega</p>
                </div>
             </ng-container>

              <hr class="linha" style="border-color: var(--cor-borda, #dee2e6);">

              <ng-container *ngIf="exibirDadosCliente()">
                 <h4 class="mt-3" style="color: var(--cor-texto-primaria, inherit);"> Dados do cliente: </h4>

                <div class="form-group mb-1 col col-12">
                  <label for="telefone" style="color: var(--cor-texto-primaria, inherit);">Telefone</label>
                  <div class="input-group ">
                    <div style="display: flex">
                    <app-seletor-codigo-pais
                     [selectedCountryCode]="pedido.contato.codigoPais ? pedido.contato.codigoPais : '+55' "
                      (phoneMaskChange)="onPhoneMaskChange($event)"
                      (selectedCountryCodeChange)="onCountrySelected($event)"></app-seletor-codigo-pais>

                    <kendo-maskedtextbox id="telefone" name="telefone" #telefone="ngModel" [mask]="phoneMask" (change)="informouTelefone()"
                                          #kendoMaskedTextBox="kendoMaskedTextBox"
                           class="form-control ml-1" [disabled]="buscando"
                           [(ngModel)]="pedido.contato.telefone" required
                           style="background-color: var(--cor-fundo-elementos, white); color: var(--cor-texto-primaria, inherit); border-color: var(--cor-borda, #ced4da);">
                      </kendo-maskedtextbox>

                    </div>

                    <div class="invalid-feedback">
                      <p *ngIf="telefone.errors?.required">Telefone é obrigatório</p>
                      <p *ngIf="telefone.errors?.patternError">Telefone inválido</p>
                    </div>
                  </div>
                </div>

                <div class="form-group mb-2 col">
                  <label for="nome" style="color: var(--cor-texto-primaria, inherit);">Nome </label>
                  <input kendoTextBox id="nome" name="nome" placeholder="Nome completo"
                         class="form-control" #nome="ngModel"  nomeCompleto [validarNome]="empresa.vendeOnline"
                         [(ngModel)]="pedido.contato.nome" required
                         style="background-color: var(--cor-fundo-elementos, white); color: var(--cor-texto-primaria, inherit); border-color: var(--cor-borda, #ced4da);"/>

                  <div class="invalid-feedback">
                    <p *ngIf="nome.errors?.required">Nome é obrigatório</p>
                    <p *ngIf="nome.errors?.nomeCompleto">Informe seu nome completo</p>
                  </div>
                </div>

                <div class="form-group mb-2 col" *ngIf="informarCpf">
                  <label style="color: var(--cor-texto-primaria, inherit);">CPF</label>
                  <input type="text" class="form-control" autocomplete="off"
                         name="cpf" [(ngModel)]="pedido.contato.cpf" #cpf="ngModel" mask="000.000.000-00" cpfValido
                         placeholder="___.___.___-__" [required]="cpfObrigatorio" (change)="informouCpf()"
                         style="background-color: var(--cor-fundo-elementos, white); color: var(--cor-texto-primaria, inherit); border-color: var(--cor-borda, #ced4da);">
                  <div class="invalid-feedback">
                    <p *ngIf="cpf.errors?.required">CPF é obrigatório</p>
                    <p *ngIf="!cpf.errors?.required && cpf.errors?.cpfInvalido">CPF inválido</p>
                  </div>
                </div>

                 <div class="form-group mb-2 col col-md-6" *ngIf="informarDataNascimento">
                    <label style="color: var(--cor-texto-primaria, inherit);">Data Nascimento</label>
                  <kendo-datepicker #dataNascimento='ngModel' [(ngModel)]="pedido.contato.dataNascimento"
                                    class="form-control" format="dd/MM/yyyy" activeView="decade"
                                    name="dataNascimento" activeView="decade"
                                    [required]="dataNascimentoObrigatorio"
                                    style="background-color: var(--cor-fundo-elementos, white); color: var(--cor-texto-primaria, inherit); border-color: var(--cor-borda, #ced4da);">
                    <kendo-datepicker-messages
                      today="Hoje"
                      toggle="Trocar calendário"
                    ></kendo-datepicker-messages>

                  </kendo-datepicker>

                     <div class="invalid-feedback">
                        <p *ngIf="dataNascimento.errors?.required">Data de nascimento é obrigatória</p>

                    </div>

                 </div>

                <div class="form-group mb-2 col aceite-fidelidade" *ngIf="fidelidadeExterna && fidelidadeExterna.aceitarFidelidade">
                    <input id="optin" name="optin" type="checkbox" kendoCheckBox [(ngModel)]="fidelidadeExterna.optin" class="k-checkbox" (change)="alterouOptinFidelidade()" />
                        <label for="optin" class="ml-1 usar-saldo" style="color: var(--cor-texto-primaria, inherit);">
                         Quero ganhar cashback com {{empresa.idRede === 1 ? 'Meu China in Box' : 'Gendai Vip'}}
                          <img src="{{fidelidadeExterna.logo}}" class="ml-2" *ngIf="fidelidadeExterna.logo" style="max-height: 40px">

                          <br>
                          <span class="ml-3" style="color: var(--cor-texto-secundaria, #6c757d);">Estou de acordo com o <a href="{{fidelidadeExterna.linkRegras}}" target="_blank" style="color: var(--cor-botao, #007bff);">Regulamento</a>.</span>
                        </label>

                </div>

                <span *ngIf="criarConta || fidelidadeExterna?.optin">
                  <fieldset style="border-color: var(--cor-borda, #ccc);">
                    <legend style="color: var(--cor-texto-primaria, inherit);">Dados de acesso</legend>
                    <div class="form-group mb-2">
                      <label for="email" style="color: var(--cor-texto-primaria, inherit);">E-mail </label>
                      <input type="email" class="form-control" autocomplete="off" [required]="true" email="true"
                             id="email" name="email" [(ngModel)]="pedido.contato.email" #email="ngModel"
                             placeholder="Email do usuário"
                             style="background-color: var(--cor-fundo-elementos, white); color: var(--cor-texto-primaria, inherit); border-color: var(--cor-borda, #ced4da);">
                      <div class="invalid-feedback">
                        <p *ngIf="email.errors?.required">Email é obrigatório</p>
                      </div>
                    </div>

                    <div class="form-group mb-2">
                      <label for="senha" style="color: var(--cor-texto-primaria, inherit);">Senha</label>
                      <label class="col-form-label float-right cpointer text-blue" (click)="exibirSenhaTela()" style="color: var(--cor-botao, #007bff);">
                        <i class="fa fa-eye fa-lg" *ngIf="!exibirSenha"></i>
                        <i class="fa fa-eye-slash fa-lg" *ngIf="exibirSenha"></i>
                      </label>
                      <span *ngIf="!exibirSenha">
                          <input class="form-control" type="password" id="senha" name="senha" #senha="ngModel"
                                 [(ngModel)]="pedido.contato.senha" placeholder="Informe sua senha" [required]="true"
                                 style="background-color: var(--cor-fundo-elementos, white); color: var(--cor-texto-primaria, inherit); border-color: var(--cor-borda, #ced4da);">
                          <div class="invalid-feedback">
                            <p *ngIf="senha.errors?.required">Senha é obrigatório</p>
                          </div>
                      </span>

                      <span *ngIf="exibirSenha">
                          <input class="form-control" type="text" id="senhaTexto" name="senhaTexto" #senhaTexto="ngModel"
                                 [(ngModel)]="pedido.contato.senha" placeholder="Informe sua senha" [required]="true"
                                 style="background-color: var(--cor-fundo-elementos, white); color: var(--cor-texto-primaria, inherit); border-color: var(--cor-borda, #ced4da);">
                          <div class="invalid-feedback">
                            <p *ngIf="senhaTexto.errors?.required">Senha é obrigatório</p>
                          </div>
                      </span>

                    </div>
                  </fieldset>
                </span>

                <hr class="linha" style="border-color: var(--cor-borda, #dee2e6);">
              </ng-container>

              <h4 class="mb-2" style="color: var(--cor-texto-primaria, inherit);">Pagamento</h4>

              <!-- Mensagem quando pagamento está bloqueado -->
              <div class="alert alert-warning" *ngIf="!formaPagamentoHabilitada()" 
                   style="background-color: var(--cor-fundo-elementos, #fff3cd); border-color: var(--cor-borda, #ffeeba); color: var(--cor-texto-primaria, #856404);">
                <i class="fa fa-exclamation-triangle mr-2"></i>
                <span *ngIf="!pedido.entrega?.formaDeEntrega">
                  Selecione uma forma de entrega para liberar as opções de pagamento
                </span>
                <span *ngIf="pedido.entrega?.ehDelivery() && !pedido.entrega?.endereco">
                  Adicione o endereço de entrega para liberar as opções de pagamento
                </span>
                <span *ngIf="(agendarEntrega || apenasAgendamento) && (!pedido.dataEntrega || !pedido.horarioEntrega)">
                  Selecione a data e horário de agendamento para liberar as opções de pagamento
                </span>
                <span *ngIf="!pedido.contato?.telefone || !pedido.contato?.nome">
                  Preencha os dados do cliente (telefone e nome) para liberar as opções de pagamento
                </span>
              </div>

              <!-- Seção de Pagamento com overlay quando bloqueada -->
              <div class="pagamento-section" [class.disabled]="!formaPagamentoHabilitada()">

                <ng-container *ngIf="cashback &&  cashback.podeUsarNaLoja" >
                <h5 style="color: var(--cor-texto-primaria, inherit);"> Usar meu <b>saldo de cashback</b> para pagar </h5>
                <div class="k-display-inline-flex pagarfidelidade" [ngClass]="{'gendai': empresa.idRede === 2}" style="background-color: var(--cor-fundo-elementos, #f7f8f8); border-left-color: var(--cor-destaque, #367ef6);">
                  <div class="flex-row">

                     <a (click)="abraModalAtualizarCadastro()" *ngIf="cashback.atualizarCadastro" href="" style="color: var(--cor-botao, #007bff);">
                      <b>Atualizar meu cadastro </b>
                       <br>
                      <small class="text-muted" style="color: var(--cor-texto-secundaria, #6c757d) !important;">Atualize seu cadastro para utilizar seu cashback</small>
                     </a>

                    <ng-container *ngIf="cashback.fazerLogin">
                        <a (click)="abraModalLogin()" href="" style="color: var(--cor-botao, #007bff);">
                      <b>Identifique-se</b>
                       <br>
                      <small class="text-muted" style="color: var(--cor-texto-secundaria, #6c757d) !important;">Faça login para utilizar seu cashback </small>
                     </a>

                      <br>

                     <button (click)="abraModalValidarConta()" class="btn btn-blue btn-xs mt-1" style="background-color: var(--cor-botao, #007bff); border-color: var(--cor-botao, #007bff); color: var(--cor-texto-botao, white);">Ver meu saldo</button>

                    </ng-container>

                     <a (click)="abraModalFazerOptin()" *ngIf="cashback.fazerOptin ||  cashback.fazerOptinUsarSaldo" href="" style="color: var(--cor-botao, #007bff);">
                        <b>Quero ganhar cashback</b>
                         <br>
                        <small class="text-muted" style="color: var(--cor-texto-secundaria, #6c757d) !important;">Participe do plano fidelidade para ganhar até 20% de cashback</small>
                     </a>

                    <div class="form-group mb-2 mt-2" [hidden]="cashback.atualizarCadastro || cashback.fazerLogin || cashback.fazerOptin ||  cashback.fazerOptinUsarSaldo">
                        <input id="usarSaldo" name="usarSaldo" type="checkbox" (change)="alterouUsarSaldo()"
                               [(ngModel)]="cashback.usar" class="k-checkbox" kendoCheckBox
                               [disabled]="!cashback.podeUsar || !cashback.podeUsarNoPedido || (pedido.teveAutenticacaoPagamento()) "/>
                        <label for="usarSaldo" class="ml-1 usar-saldo" style="color: var(--cor-texto-primaria, inherit);">
                          Saldo cashback disponível <span class="preco" style="color: var(--cor-preco, #6db31b); font-weight: 700;"><b>{{ obtenhaSaldo()}}</b></span>
                        </label>

                          <p class="mt-1" *ngIf="!cashback.podeUsar" style="color: var(--cor-texto-secundaria, #6c757d);">
                            <i class="fa fa-exclamation-circle mr-1 text-warning" style="color: #ffc107;"></i>
                            Acumule <span style="color: var(--cor-botao, #007bff);"><b>{{ cashback.minimo | currency:"BRL"}}</b></span> de saldo
                            e utilize como forma de pagamento.</p>

                           <p class="mt-1" *ngIf="cashback.podeUsar && !cashback.podeUsarNoPedido" style="color: var(--cor-texto-secundaria, #6c757d);">
                            <i class="fa fa-exclamation-circle mr-1 text-warning" style="color: #ffc107;"></i>
                            Faça um pedido a partir de <span style="color: var(--cor-botao, #007bff);"><b>{{ cashback.minimoPedido | currency:"BRL"}}</b></span>
                            e utilize seu saldo como forma de pagamento.</p>


                </div>
                  </div>
                  <div class="flex-row">
                   <img src="{{empresa.integracaoFidelidade?.logo}}" class="ml-2 imagem-programa" style="max-height: 40px;"
                        *ngIf="empresa.integracaoFidelidade?.logo">
                  </div>
                </div>

                <div class="produto pt-0 pb-2" [hidden]="obtenhaTotalPagar() === 0" style="background-color: var(--cor-fundo-elementos, #fff); border-color: var(--cor-borda, #e8e8e8);">
                  <div class="media mt-2">
                    <div class="media-body">
                      <h5 class="mt-0 mb-1" style="color: var(--cor-texto-primaria, inherit);"> Total restante a pagar

                      <span class="preco font-15" style="color: var(--cor-preco, #6db31b); font-weight: 700;"><b>{{obtenhaTotalPagar() | currency: 'BRL'}}</b></span></h5>
                    </div>

                  </div>
                </div>

             </ng-container>

              <ng-container *ngIf="obtenhaTotalPagar() > 0">
                 <div *ngIf="!empresa.formasDePagamento?.length" class="alert alert-info" style="background-color: var(--cor-fundo-elementos, #d1ecf1); border-color: var(--cor-borda, #bee5eb); color: var(--cor-texto-primaria, #0c5460);">
                   Nenhuma forma de pagamento disponível
                 </div>

                 <app-loja-formaspagamento-antigo #formasPagamentoAntigas *ngIf="!empresa.exibirBandeiras"
                                                   (carteiraDigitalConfirmada)="onCarteiraDigitalConfirmada()">
                 </app-loja-formaspagamento-antigo>

                 <app-loja-formaspagamento-nova #formasPagamentoNova *ngIf="empresa.exibirBandeiras"
                                                (carteiraDigitalConfirmada)="onCarteiraDigitalConfirmada()">
                 </app-loja-formaspagamento-nova>

                 <hr class="linha" style="border-color: var(--cor-borda, #dee2e6);">
                </ng-container>
              </div>
            </ng-container>

            <ng-container *ngIf="(formasPagamentoAntigas?.exigeCartao  || formasPagamentoNova?.exigeCartao) &&
            !pedido.pagamento.dadosCartao && !pedido.entrega.formaDeEntrega">
              <div class="alert alert-warning" style="background-color: var(--cor-fundo-elementos, #fff3cd); border-color: var(--cor-borda, #ffeeba); color: var(--cor-texto-primaria, #856404);">
                <span>Selecione a Forma de Entrega Antes de Adicionar os dados de cartão</span>
              </div>
            </ng-container>

            <ng-container *ngIf="pedido.maisDeUmaFormaPagamento()">
              <div class="media-body mt-1">
              <h5 class="mt-0 mb-1" style="color: var(--cor-texto-primaria, inherit);"><span>Formas De Pagamento:</span></h5>
            </div>
            <div class="clearfix"></div>
            <div class="mt-2 d-flex flex-column w-100">

            <h5 class="mt-0 flex-row text-right" *ngIf="pedido.cashback" style="color: var(--cor-texto-primaria, inherit);">
              <strong>Fidelidade Cashback:
                <span class="preco" style="color: var(--cor-preco, #6db31b); font-weight: 700;">{{obtenhaSaldo()}}</span>
               </strong>
            </h5>


            <h5 class="mt-0 flex-row text-right" *ngIf="pedido.totalResgatado" style="color: var(--cor-texto-primaria, inherit);">
              <strong>Fidelidade resgate:
                <span class="preco-resgate" style="color: var(--cor-preco-adicional, inherit);">-{{pedido.totalResgatado}} {{pedido.acumulo}}</span>
               </strong>
            </h5>


            <h5 class="mt-0 text-right flex-row" *ngIf="pedido.pagamento?.formaDePagamento" style="color: var(--cor-texto-primaria, inherit);">
              <strong>{{pedido.pagamento.formaDePagamento.descricao}}:
                <span class="preco" style="color: var(--cor-preco, #6db31b); font-weight: 700;">{{ pedido.obtenhaValorAhPagar() | currency: "BRL"}}</span>
             </strong>
            </h5>
            </div>


            </ng-container>

            <div class="col mt-2 mb-2" *ngIf="msgErro">
                      <div class="alert alert-danger" role="alert" style="background-color: var(--cor-fundo-elementos, #f8d7da); border-color: var(--cor-borda, #f5c6cb); color: var(--cor-texto-primaria, #721c24);">
                      <i class="fas fa-exclamation-triangle"></i> {{msgErro}}
                     </div>
            </div>

            <div class="col mt-4" *ngIf="!criarConta && !pedido.contato.id && !fidelidadeExterna?.optin && !isCarteiraDigitalSelecionada()">

            <button class="btn btn-blue btn-block" type="submit" *ngIf="!pedido.codigo" [disabled]="eviandoPedido || buscando"
                    style="background-color: var(--cor-botao, #007bff); border-color: var(--cor-botao, #007bff); color: var(--cor-texto-botao, white);">
            <i class="k-icon k-i-loading mr-1" *ngIf="eviandoPedido"></i>
            Fazer Pedido sem Criar Conta</button>

            <button class="btn btn-primary btn-block" type="button" (click)="exibirCriarConta()" [disabled]="eviandoPedido || buscando"
                    style="background-color: var(--cor-botao, #007bff); border-color: var(--cor-botao, #007bff); color: var(--cor-texto-botao, white);">
            Criar conta</button>

            </div>

            <div class="mt-3" *ngIf="(criarConta || pedido.contato.id || fidelidadeExterna?.optin) && !isCarteiraDigitalSelecionada()">
            <button class="btn btn-blue btn-block" type="submit" *ngIf="!pedido.codigo" [disabled]="eviandoPedido || buscando"
                    style="background-color: var(--cor-botao, #007bff); border-color: var(--cor-botao, #007bff); color: var(--cor-texto-botao, white);">
            <i class="k-icon k-i-loading mr-1" *ngIf="eviandoPedido"></i>
            Fazer pedido </button>

            <button class="btn btn-light btn-block mb-2 mt-2" *ngIf="criarConta" type="button" [disabled]="eviandoPedido || buscando"
                   (click)="canceleCriarConta()" style="background-color: var(--cor-fundo-elementos, #f8f9fa); border-color: var(--cor-borda, #f8f9fa); color: var(--cor-texto-primaria, #212529);">Voltar</button>

            </div>

              <div id="google-pay-container" [hidden]="!isGooglePaySelecionado()" class="mt-3"></div>

              <div id="applePayDiv" [hidden]="!isApplePaySelecionado()" class="mt-3"></div>

          </span>

        </form>

      </div>

    </div>

  </div>

  <div class="col-8 panel-pedido" style="border-radius: 3px; border-color: var(--cor-borda, #ccc3);">
    <app-loja-carrinho #panelCarrinho></app-loja-carrinho>
  </div>
</div>


<div id="alertaGerandoPagamento" class="modal fade" tabindex="-1" role="dialog" aria-modal="true" data-backdrop="static">
  <div class="modal-dialog">
    <div class="modal-content" style="background-color: var(--cor-fundo-elementos, white); border-color: var(--cor-borda, rgba(0, 0, 0, 0.2));">
      <div class="modal-body p-4">
        <div class="text-center">

          <ng-container *ngIf="!msgErroLink">
            <h4 class="mt-2" style="color: var(--cor-texto-primaria, inherit);">
              <i class="k-icon k-i-loading mr-1"></i>
              Aguarde que você será redirecionado para tela de pagamento.</h4>
          </ng-container>

          <ng-container *ngIf="msgErroLink">

            <i class="k-icon k-i-error mr-1" style="color: #dc3545;"></i> Falha ao gerar link de pagamento:
            <br>
            <b style="color: var(--cor-texto-primaria, inherit);">{{msgErroLink}}</b>
            <br> <br>
            <button class="btn btn-blue mt-1" (click)="gereLinkPagamento()"
                    style="background-color: var(--cor-botao, #007bff); border-color: var(--cor-botao, #007bff); color: var(--cor-texto-botao, white);">Tentar de novo</button>

          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>
