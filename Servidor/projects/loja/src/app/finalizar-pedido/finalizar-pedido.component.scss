.pedido-finalizado{
  padding-top: 25px;
  padding-bottom: 25px;
  .alert-success{
    background: var(--cor-fundo-elementos, #fff);
    font-size: 20px;
  }
}

.k-checkbox:disabled{
  border-color: rgba(0, 0, 0, 0.1) !important;
}

.usar-saldo{
  display: inline;
  top: 2px;
  position: relative;
}

.k-form .k-label{
  display: inline !important;
}

.panel-finalizar{
  background: var(--cor-fundo-site, #fff);
  min-height: 700px;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;

  .card-body{
    >div{
      max-width: 600px;
    }
  }

}

.panel-pedido{
  position: absolute;
  right: 15px;
  top: 30px;
  max-width: 400px;
  background: var(--cor-fundo-elementos, #f7f8f8);
  border: 1px solid var(--cor-borda, #ccc3);
 /*  box-shadow: 0 4px 10px -2px #E2E3E3; */
}


::ng-deep body{
  background-color: var(--cor-fundo-site, #f7f8f8) !important;
}

input.troco {
  max-width: 215px;
}

.k-dateinput-wrap {
    padding: 0 !important;
}

::ng-deep.escolher {
  span.radio-blue {
    position: relative;
    min-width: 230px;
    display: inline-block;
    min-height: 40px;
    padding: 10px;
    background: var(--cor-fundo-elementos, #f7f8f8);
    /* border-right: 1px solid #ccc; */
    margin-left: 10px;

    &.auto{
      min-width: inherit;
    }

    &.cartao{
      width: 100%;
    }
  }

  span.selecionado:before {
    border-left: 5px solid var(--cor-destaque, #3483fa);
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
  }

  &.cartoes{
    span {
      width: 100%;
      background: var(--cor-fundo-elementos, #f7f8f870);

      &.selecionado{
        background: var(--cor-fundo-elementos, #f7f8f8) !important;
      }

    }


  }

  &.menor{
    span {
      width: 190px;
    }
  }

  &.troco{
    span{
      width: 120px;
      label{
        display: inline;
        top: 2px;
        position: relative;
      }
    }
  }

  .cartao-online{
    .fa{
      top: 7px;
      position: relative;
    }
    label small{
      left: 85px;
      position: relative;
    }

    .acao-cartao{
      position: absolute;
      right: 10px;
      top: 15px;
    }
  }

}



.icone.whatsapp {
  width: 16px;
  display: inline-block;
  margin: 0
}

.icone_voltar {
  display: inline-block;
  fill: var(--cor-botao, #4a81d4);
  vertical-align: middle;
}


fieldset {
  border: 1px solid var(--cor-borda, #ccc);
  padding: 20px;
  margin-top: 20px;
  padding-top: 10px;
  margin-left: 12px;
  margin-right: 12px;
  legend{
    max-width: 140px;
    font-size: 1.0em;
  }
}

::ng-deep .k-dialog-titlebar {
  background: var(--cor-botao, #3A44B9);
  color: var(--cor-texto-botao, #fff);
}

.preco {
  color: var(--cor-preco, #6db31b);
  font-weight: 700;
}

h5{
  font-size: 1em !important;
}
.pagarfidelidade{
  background: var(--cor-fundo-elementos, #f7f8f8);
  padding: 10px;
  border-left: 5px solid var(--cor-destaque, #367ef6);
  margin-left: 10px;
  &.gendai{
    img{
      top: 13px;
      position: relative;
    }
  }

}

.chinainbox{
  .btn-blue {
    background-color: var(--cor-botao, #e52a28de) !important;
    border-color: var(--cor-botao, #e52a28) !important;;
  }
}

.carnaval {
  .btn-blue, .btn-primary {
    background-color: var(--cor-botao, #5b1da6) !important;
    border-color: var(--cor-botao, #5b1da6) !important;
  }
}

::ng-deep  .k-tabstrip > .k-content  {
  overflow: hidden !important;
}

.digital-wallet-section {
  .wallet-content {
    background-color: var(--cor-fundo-elementos, #ffffff);
    border: 1px solid var(--cor-borda, #e0e0e0);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

    .payment-summary {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      background-color: var(--cor-fundo-elementos, #fafafa);
      border-bottom: 1px solid var(--cor-borda, #e0e0e0);

      .summary-text {
        .summary-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--cor-texto-primaria, #212529);
          margin: 0 0 4px 0;
        }

        .summary-description {
          font-size: 0.875rem;
          color: var(--cor-texto-secundaria, #6c757d);
          margin: 0;
        }
      }

      .summary-amount {
        text-align: right;

        .amount-label {
          display: block;
          font-size: 0.875rem;
          color: var(--cor-texto-secundaria, #6c757d);
          margin-bottom: 2px;
        }

        .amount-value {
          display: block;
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--cor-preco, #6db31b);
        }
      }
    }

    .wallet-button-area {
      padding: 24px;

      #google-pay-container,
      #applePayDiv {
        width: 100%;
        max-width: 320px;

        ::ng-deep {
          button {
            width: 100% !important;
            height: 48px !important;
            border-radius: 8px !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

::ng-deep#google-pay-container,
::ng-deep #applePayDiv {
  display: flex;
  justify-content: center;
  width: 100% !important;

  div {
    width: 100% !important;
  }

  apple-pay-button {
    width: 100% !important;
  }

  button {
    width: 100% !important;
    max-width: 100%;
  }
}

#gpay-button-online-api-id {
  width: 100% !important;
  max-width: 100%;
}

.pagamento-section {
  position: relative;
  transition: opacity 0.3s ease;
  
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
    user-select: none;
    
    // Overlay visual adicional
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.5);
      z-index: 1;
    }
  }
}
