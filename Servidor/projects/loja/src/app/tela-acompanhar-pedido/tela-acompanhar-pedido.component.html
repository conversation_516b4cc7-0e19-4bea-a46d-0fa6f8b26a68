<app-header-tela [titulo]="titulo" [retorno]="dominiosService.obtenhaUrlHome()" ></app-header-tela>

<div class="row  mt-2"  >
  <div class="col-12 col-xl-7">

    <div class="row" *ngIf="acabouFazerPedido && !pedido.cancelado" >
      <div  class="col-12 "  >
        <div class="card mb-2 pedido-finalizado" >
          <div class="card-body p-2">
            <div  class=" text-center" *ngIf="!pedido.aguardandoPagamentoOnline">
              <img src="/assets/icons/icon-success.png"  >

              <p class="alert-success"> Pedido #{{pedido.codigo}} realizado com sucesso!</p>
            </div>

            <div  class=" text-center" *ngIf="pedido.aguardandoPagamentoOnline">
              <img src="/assets/icons/aguardando-pagamento.png"  >

              <p class="    alert-warning">
                <b>Atenção:</b>  Seu pedido será enviado à cozinha somente após a confirmação do pagamento.
                Realize o pagamento agora para liberar seu pedido.
              </p>

              <div class="alert alert-info" *ngIf="!falhaNoPagamentoOnline" [hidden]="aguardandoTokenizar || true">
                <i class="fe-alert-circle mr-1 fa-lg"></i>

                {{!autenticarPagamento ? 'Seu pedido está aguardando confirmação do pagamento':
                'Seu pedido está aguardando autenticação do cartão'}}

              </div>


              <div class="alert alert-info" *ngIf="aguardandoTokenizar">
                <i class="k-icon k-i-loading mr-1"></i>
                Aguarde que o pagamento está sendo processado <b>não feche a tela!</b>, isso pode demorar alguns segundos
              </div>


              <div class="alert alert-danger" *ngIf="falhaNoPagamentoOnline">
                <i class="fe-alert-circle mr-1 fa-lg"></i>Houve algum problema ao processar o pagamento: {{mensagemFalhaPagamento}}
              </div>
              <div class="alert alert-danger" *ngIf="foiReembolsado">
                <i class="fe-alert-circle mr-1 fa-lg"></i>{{mensagemFalhaPagamento}}
              </div>

              <a class="btn btn-blue  waves-effect mb-2    btn-block" *ngIf="pedido.linkPagamento"
                 [href]="pedido.linkPagamento" target="_blank">
                Tentar novo pagamento
              </a>

              <button class="btn btn-blue  waves-effect mb-2    btn-block" *ngIf="(pedido.novaTentativaCartao)"
                      [disabled]="tentandoNovoPagamento"
                      (click)="abrirPopupNovoPagamento()" target="_blank"    >
                {{tentandoNovoPagamento ?"Aguarde enquanto enviamos seu novo pagamento..." : "Tentar novo pagamento"}}
              </button>

              <button class="btn btn-blue  waves-effect mb-2    btn-block" *ngIf="(pedido.novaTentativaPix)"
                      [disabled]="tentandoNovoPagamento"
                      (click)="gerePix()" target="_blank"    >
                {{tentandoNovoPagamento ? "Gerando pix, aguarde..." : "Tentar gerar novamente"}}
              </button>
            </div>
            <!-- Removido pois estamos abrindo automaticamente
            <a class="btn btn-success waves-effect btn-block"  *ngIf="!pedido.mesa" [hidden]="pedido.aguardandoPagamentoOnline"
               [href]="urlArir" target="_blank" >
              <i class="fab fa-whatsapp fa-2x" ></i>
              Enviar para Whatsapp {{empresa.nome}}
            </a>
            -->
          </div>

        </div>
      </div>
    </div>

    <ng-container *ngIf="pagamento?.formaDePagamento?.pix">
      <app-pagamento-pix-qrcode #telaPix [pedido]="pedido"
                                [pagamento]="pagamento" [desktop]="desktop"
        (onGerarPix)="gerePix()">
      </app-pagamento-pix-qrcode>
    </ng-container>

    <div class="row">

      <div class="col-12" *ngIf="msg">
        <div class="alert alert-warning font-18">
          <i class="fe-star-on mr-1 fa-lg"></i> {{msg}}
        </div>

      </div>

      <div class="col-12" *ngIf="foiPago">
        <div class="alert alert-success font-18">
          <i class="fe-check-circle mr-1 fa-lg"></i> Seu pagamento foi confirmado.
        </div>

      </div>

      <div class="col-12" *ngIf="avaliacao">
        <div class="card-box ribbon-box">
          <h4 class="header-title mb-1">Avaliação do pedido</h4>
          <ngb-rating [(rate)]="avaliacao.nota" [max]="5" [readonly]="true">
            <ng-template let-fill="fill" let-index="index">
              <span class="star"  [class.filled]="index < avaliacao.nota" [class.bad]="avaliacao.nota <= 3">&#9733;</span>
            </ng-template>
          </ngb-rating>
          <div class="ml-0">
            Gostou da entrega: <strong>{{avaliacao.gostouDaEntrega ? 'Sim' : 'Não'}}</strong>
          </div>
          <div class="ml-2 mt-2">
            {{avaliacao.comentario}}
          </div>
        </div>
      </div>

      <div class="col-12">
        <div class="card-box ribbon-box"  >
          <h4 class="header-title mb-3   ">Pedido <b>#{{pedido?.codigo}}</b>


          </h4>

          <div class="ribbon-content">

            <div  class="fundo" >
              <div class="d-flex justify-content-center align-items-center" style="height: 400px" *ngIf="!pedido && carregou">
                <div class="align-self-center text-center" style="color: #F1556C;">
                  <i class="fas fa-exclamation-triangle" style="font-size: 48px"></i>
                  <p>
                    Não encontrado!
                  </p>
                </div>
              </div>

              <div *ngIf="pedido.id">
                <div class="desktop">
                  <div class="produto   pb-0"    *ngIf="pedido.mesa">
                    <div class="media mt-2">
                      <div class="media-body">
                        <h5 class="mt-0 mb-1"> <i class="fa fa-table fa-lg"></i><span class="ml-1">{{pedido.empresa.identificadorMesa}}</span></h5>
                      </div>
                      <h5 class="mt-0 text-right">
                        <strong>  {{pedido.mesa.nome}}</strong>
                      </h5>
                    </div>

                  </div>

                  <div class="produto   pb-0"    >
                    <div class="media mt-2">
                      <div class="media-body">
                        <h5 class="mt-0 mb-1"> <i class="fe-user fa-lg"></i><span>Cliente</span></h5>
                      </div>
                      <h5 class="mt-0 text-right">
                        <strong>{{pedido.cliente.nome}}</strong> <br>
                        <label class="text-muted mt-1">{{pedido.cliente.telefone | telefone: pedido.cliente.codigoPais}}</label>
                      </h5>
                    </div>

                  </div>

                  <div class="produto pt-2 pb-1" *ngIf="pedido.formaDeEntrega">
                    <div class="media mt-1">
                      <div class="media-body">
                        <h5 class="mt-0 mb-1"><span> <i class="fe-map-pin fa-lg"></i> Forma de entrega</span></h5>
                      </div>
                      <h5 class="mt-0"><strong>{{pedido.formaDeEntrega}}</strong></h5>
                    </div>
                    <div class="media mt-1" *ngIf="pedido.horarioEntregaAgendada">
                      <div class="media-body">
                        <h5 class="mt-0 mb-1"><span> <i class="fe-clock fa-lg"></i> Agendado para</span></h5>
                      </div>
                      <h5 class="mt-0"><strong>{{this.textoHorarioEntregaAgendada}}</strong></h5>
                    </div>

                    <div class="pt-0 pb-0 ml-2" *ngIf="pedido.formaDeEntrega === FormaDeEntrega.RECEBER_EM_CASA">
                      <h5 class="text-muted"><i class="fas fa-map-marker-alt"></i> Endereço Escolhido</h5>
                      <h5>
                        {{pedido.enderecoCompleto}}
                      </h5>
                    </div>

                  </div>


                  <h5 class="mt-2 mb-1"><span>
             <i class="fe-shopping-bag fa-lg"></i>
            Produtos</span></h5>

                  <div *ngFor="let item of pedido.itens" class="produto pt-2 pb-2 ml-1 item_produto dashed">
                    <div class="media">
                      <div class="media-body">
                        <h5 class="mt-0 mb-1">
                          {{item.qtde}}{{item.unidadeTxt}}   {{item.descricao}}
                        </h5>

                        <p class="mb-0 descricao-resumida">
                          {{item.produto.descricao}}
                        </p>

                        <div *ngIf=" item.adicionaisImprirmir?.length" >
                       <span  *ngFor="let last = last;let adicional of item.adicionaisImprirmir " class="d-block ml-2 font-weight-bold">
                         {{adicional.descricao}}{{last ? '' : ', '}}
                       </span>
                        </div>
                        <span *ngIf="item.observacao"  class="mt-1 d-block"> Obs.:<i>"{{item.observacao}}"</i></span>
                      </div>
                      <h5 class="preco mt-0" *ngIf="!item.valorResgatado">
                        {{item.total | currency: 'BRL'}}
                      </h5>
                      <h5 class="mt-0" *ngIf="item.valorResgatado">
                        -{{item.valorResgatado}} {{pedido.acumulo}}
                      </h5>
                    </div>
                  </div>
                </div>

                <div class="produto pt-2 pb-1" *ngIf="pedido.observacoes">
                  <div class="media mt-1">
                    <div class="media-body">
                      <h5 class="mt-0 mb-1"><span>Observação</span></h5>
                    </div>
                    <h5 class="mt-0">
                      <i>
                        {{pedido.observacoes}}
                      </i>
                    </h5>
                  </div>
                </div>

                <div class="linha mt-0 mb-0" *ngIf="pedido.itens.length > 3"></div>

                <div class="  totais  pt-2 pb-0">
                  <div class="media">
                    <div class="media-body">
                      <h5 class="mt-0 mb-1 text-muted"><span>Subtotal</span></h5>
                    </div>
                    <h5 class="mt-0 text-muted font-16"> {{pedido.subvalor | currency: 'BRL'}}</h5>
                  </div>

                  <div class="media" *ngIf=" pedido.desconto">
                    <div class="media-body">
                      <h5 class="mt-0 mb-1"><span>Desconto</span><br>
                        <span style="font-size: 10px" *ngIf="pedido.promocoesAplicadas"><i>{{ obtenhaDescricaoPromocoes()  }}</i></span><br>
                        <span style="font-size: 10px" *ngIf="pedido.cupom"><i>{{ pedido.cupom  }}</i></span></h5>

                    </div>
                    <h5 class="mt-0">-{{pedido.desconto | currency: 'BRL'}}</h5>

                  </div>

                  <div class="media" *ngIf=" pedido.descontoFormaDePagamento">
                    <div class="media-body">
                      <h5 class="mt-0 mb-1"><span>{{pedido.descricaoDescontoFormaDePagamento}}</span><br></h5>

                    </div>
                    <h5 class="mt-0">-{{pedido.descontoFormaDePagamento | currency: 'BRL'}}</h5>

                  </div>
                  <div class="media" *ngIf=" pedido.taxaEntrega">
                    <div class="media-body">
                      <h5 class="mt-0 mb-1"><span>Taxa De Entrega</span></h5>
                    </div>
                    <h5 class="mt-0">+{{pedido.taxaEntrega | currency: 'BRL'}}</h5>
                  </div>

                  <div class="media" *ngIf=" pedido.taxaFormaDePagamento">
                    <div class="media-body">
                      <h5 class="mt-0 mb-1"><span>Taxa forma de pagamento</span></h5>
                    </div>
                    <h5 class="mt-0">+{{pedido.taxaFormaDePagamento | currency: 'BRL'}}</h5>
                  </div>


                  <div class="media" *ngIf="pedido?.pagamento && pedido.pagamento.formaDePagamento === 'Dinheiro'">
                    <div class="media-body">
                      <h5 class="mt-0 mb-1"><span>Troco</span></h5>
                    </div>
                    <h5 class="mt-0 preco">{{pedido.troco | currency: 'BRL'}}</h5>
                  </div>
                  <div class="media mt-0">
                    <div class="media-body">
                      <h5 class="mt-0 mb-1"><span>Total</span></h5>
                    </div>
                    <h5 class="mt-0 preco font-18"><strong>{{pedido.total | currency: 'BRL'}}</strong></h5>
                  </div>
                </div>

                <div class="pamento "  *ngFor="let pagamento of pedido.pagamentos; let primeiro = first;">

                  <div class="media">
                    <div class="media-body" *ngIf="primeiro && pedido.pagamentos.length === 1">
                      <h5 class="mt-0 mb-1"><span>  Pagamento em:</span>
                      </h5>

                    </div>
                    <h5 class="mt-0  "  >
                      {{pagamento.descricao }}
                      <ng-container *ngIf="pedido.pagamentos.length > 1">
                        <span   [hidden]="pagamento.resgate"> ({{pagamento.valor | currency: "BRL" }})</span>
                        <span   [hidden]="!pagamento.resgate"> (-{{pagamento.valor}} {{pedido.acumulo}})</span>
                      </ng-container>

                      <span *ngIf="pagamento?.formaDePagamento?.chavePix">
                        : <b>{{pagamento.formaDePagamento.chavePix}}</b>
                      </span>
                    </h5>
                  </div>

                  <ng-container *ngIf="pagamento.levarTroco" >
                     <div class="media" >
                        <div class="media-body"  >
                          <h5 class="mt-0 mb-1"><span>  Troco para:</span>
                          </h5>

                        </div>
                         <h5 class="mt-0  ">
                            {{pagamento.trocoPara | currency: "BRL"}}
                          </h5>

                    </div>
                     <div class="media" >
                      <div class="media-body"  >
                        <h5 class="mt-0 mb-1"><span> Valor do troco:</span>
                        </h5>
                      </div>
                      <h5 class="mt-0  ">
                        {{pagamento.valorTroco | currency: "BRL"}}
                      </h5>

                    </div>
                  </ng-container>

                </div>

                <ng-container *ngIf="pedido.pontosGanhos" >
                  <div class="linha mt-0 mb-0"  ></div>
                  <div class="media mt-2" >
                    <div class="media-body"  >
                      <h5 class="mt-0 mb-1 text-muted"><span> {{ pedido.temCashback ? 'Cashback a confirmar' : ' Pontos a confirmar:' }} </span>
                      </h5>

                    </div>
                    <h5 class="mt-0  ">
                      {{pedido.pontosGanhosDescricao }}
                    </h5>

                  </div>
                </ng-container>

                <h5  *ngIf="pedido.observacoes"><span class="text-muted">Observações:</span>  "{{pedido.observacoes}}"</h5>


                <button class="btn btn-danger btn-block  waves-effect  mt-2" [disabled]="cancelando" style="top:-10px;"
                        (click)="cancelePedidoAguardando()"  *ngIf="pedido?.aguardandoPagamentoOnline"
                        [hidden]="aguardandoTokenizar || retorno3ds">
                  Cancelar Pedido <i class="k-icon k-i-loading ml-1" *ngIf="cancelando"></i>
                </button>

                <div style="height: 40px" class="mobile"></div>


              </div>
            </div>
          </div>
        </div>
      </div>


    </div>

  </div>


  <div class="col-12 col-xl-5">
    <div class="card-box ribbon-box">
      <app-pedido-ribbon [pedido]="pedido"></app-pedido-ribbon>

      <h4 class="header-title mb-3 mt-1">Acompanhamento do pedido  </h4>

      <div class="ribbon-content">
        <div class="track-order-list">

          <ul class="list-unstyled">
            <li *ngIf="!pedido.aguardandoPagamentoOnline" [ngClass]=" {'completed': pedido.statusOrdem > 0  ,
                              'actived': pedido.statusOrdem ==  0   }">
              <span class="active-dot dot" *ngIf="pedido.statusOrdem ==  0   "></span>
              <h5 class="mt-0 mb-1">Pedido Realizado</h5>
              <p class="text-muted">{{ pedido.horarioDescricao }}
                <span class="badge badge-light">{{pedido.duracaoDescricao}}</span>
              </p>

              <h5 class="text-success" *ngIf="!pedido.aceito && !pedido.cancelado">
                <b>Aguardando confirmação do pedido</b>
              </h5>
            </li>

            <li class="actived" *ngIf="pedido.aguardandoPagamentoOnline">
              <span class="active-dot dot"  ></span>
              <h5 class="mt-0 mb-1 text0">Aguardando confirmação pagamento online</h5>
              <p class="text-muted">{{ pedido.horarioDescricao }}
                <span class="badge badge-light">{{pedido.duracaoDescricao}}</span>
              </p>
            </li>

             <ng-container *ngIf="!pedido.aguardandoPagamentoOnline && !pedido.cancelado && pedido.aceito">
               <li [ngClass]=" {'completed':  pedido.statusOrdem > 1 }">
                  <span class="active-dot dot" *ngIf="pedido.statusOrdem ==  1">      </span>
                  <h5 class="mt-0 mb-1">Em preparação</h5>
                  <p class="text-muted"> &nbsp;
                    <span class="text-muted" *ngIf="pedido.statusOrdem == 1">{{pedido.horarioAtualizacaoDescricao}}</span>
                  </p>
               </li>

               <li [ngClass]=" {'completed': pedido.statusOrdem > 2  }">
                  <h5 class="mt-0 mb-1">Pronto</h5>
                  <span class="active-dot dot" *ngIf="pedido.statusOrdem == 2">                </span>
                  <p class="text-muted"> &nbsp;
                    <span class="text-muted" *ngIf="pedido.statusOrdem ==  2">{{pedido.horarioAtualizacaoDescricao}}</span>
                  </p>
                </li>
               <ng-container *ngIf="!pedido.mesa">
                 <li [ngClass]="{'completed': pedido.statusOrdem > 3  }">
                   <span class="active-dot dot" *ngIf="pedido.statusOrdem===3"></span>
                   <h5 class="mt-0 mb-1"> Saiu para entrega</h5>
                   <p class="text-muted"> &nbsp;
                     <span   *ngIf="pedido.statusOrdem === 3">{{pedido.horarioAtualizacaoDescricao}}</span>
                   </p>

                 </li>

                 <li [ngClass]="{'completed': pedido.statusOrdem == 4  }">

                   <h5 class="mt-0 mb-1">Entregue</h5>
                   <p class="text-muted"> &nbsp;
                     <span   *ngIf="pedido.statusOrdem === 4">{{pedido.horarioAtualizacaoDescricao}}</span>
                   </p>
                 </li>
               </ng-container>
              </ng-container>

              <ng-container *ngIf="pedido.cancelado">
                 <li class="completed">

                    <h5 class="mt-0 mb-1">{{pedido.status}}</h5>
                    <p class="text-muted">
                      <span class="text-muted"  >{{pedido.horarioAtualizacaoDescricao}}</span>
                    </p>
                  </li>
              </ng-container>



          </ul>

        </div>


      </div>
    </div>
  </div>

</div>


