.tab-bar {
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-around;
  background: #fff;
  left: 0;
  right: 0;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.24);
  z-index: 9998;

  &.powered-by {
    padding-bottom: calc(16px + env(safe-area-inset-bottom) + 10px);
    box-shadow: none;
  }

  .tab-item {
    position: relative;
    overflow: hidden;
    padding: 8px 0;
    width: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #3f3e3e73;
    text-decoration: none;
    border: none;
    background: none;

    &.ativo{
      color: #3f3e3e;
    }
    .tab-icon{
      i{
        font-size: 24px;
        text-align: center;
        display: block;
      }
    }
    .tab-title{
      font-size: 12px;
    }
  }
}

::ng-deep .natal {
  .tab-bar {
    color: yellow !important;
  }

  .tab-title {

  }

  .tab-item {
    .tab-title {
      color: #d39797 !important;
    }

    i {
      color: #d39797 !important;
    }

    &.ativo {
      .tab-title {
        color: #710101 !important;
      }

      i {
        color: #710101 !important;
      }
    }
  }
}

::ng-deep .tema-personalizado {
  .tab-bar {
    background: var(--cor-fundo, #5b1da6);
    border-top: solid 1px var(--cor-texto-secundaria, #999);

    .tab-item {
      color: var(--cor-texto, #999) !important;

      .tab-title {
        color: var(--cor-texto-rodape, #fce101) !important;
      }

      i {
        color: var(--cor-texto-rodape, #fce101) !important;
      }

      &.ativo {
        color: var(--cor-texto, #fce101) !important;

        .tab-title {
          color: var(--cor-texto, #fce101) !important;
        }

        i {
          color: var(--cor-texto, #fce101) !important;
        }
      }
    }
  }
}


::ng-deep .carnaval {
  .tab-bar {
    background: #D825A9;
    border-top: solid 1px #fff000;

    .tab-item {
      .tab-title {
        color: #ffffff !important;
      }

      i {
        color: #ffffff !important;
      }

      &.ativo {
        .tab-title {
          color: #fff000 !important;
        }

        i {
          color: #fff000 !important;
        }
      }
    }
  }
}

::ng-deep .black_friday_2022 {
  .tab-bar {
    background: #000;
    border-top: solid 1px #999;

    .tab-item {
      color: #999 !important;

      &.ativo {
        color: #F6A844 !important;
      }
    }
  }
}

::ng-deep .copa_2022 {
  .tab-bar {
    background: #1a7200;
    border-top: solid 1px #9abe90;

    .tab-item {
      color: #9abe90 !important;

      &.ativo {
        color: #ffd80e !important;
      }
    }
  }
}


::ng-deep .tema-personalizado {
  .tab-bar {
    background: var(--cor-fundo-rodape, #fff);
    border-top: solid 1px var(--cor-borda-rodape, #999);

    .tab-item {
      color: var(--cor-texto-rodape, #999) !important;

      &.ativo {
        .tab-title {
          color: var(--cor-item-ativo-rodape, var(--cor-destaque, #999)) !important;
        }

        i {
          color: var(--cor-item-ativo-rodape, var(--cor-destaque, #999)) !important;
        }
      }
    }
  }
}

::ng-deep .cacau_show {
  .tab-bar {
    background-color: var(--cor-fundo-elementos);
    border-top: none;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

    .tab-item {
      color: var(--cor-texto-secundaria) !important;

      .tab-title {
        color: var(--cor-texto-secundaria) !important;
      }

      i {
        color: var(--cor-texto-secundaria) !important;
      }

      &.ativo {
        .tab-title {
          color: var(--cor-destaque) !important;
        }

        i {
          color: var(--cor-destaque) !important;
        }
      }
    }
  }
}
