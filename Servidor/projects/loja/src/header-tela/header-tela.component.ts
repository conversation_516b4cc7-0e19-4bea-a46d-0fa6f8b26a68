import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Location} from "@angular/common";
import {DominiosService} from "../services/dominios.service";
import {MyDetectorDevice} from "../app/shared/MyDetectorDevice";

@Component({
  selector: 'app-header-tela',
  templateUrl: './header-tela.component.html',
  styleUrls: ['./header-tela.component.scss']
})
export class HeaderTelaComponent implements OnInit {
  @Input() titulo: any;
  @Input() icon: string;
  @Input() tema: any;
  @Input() exibirFechar = false;
  @Output() fechou = new EventEmitter<boolean>();
  @Output() clicouVoltar = new EventEmitter<boolean>();

  isMobile: boolean;

  @Input() public retorno ;
  constructor(private _location: Location, private dominiosService: DominiosService,
              private deviceService: MyDetectorDevice) {
    this.isMobile = this.deviceService.isMobile();
  }

  ngOnInit() {
    if( window['tema'])
      this.tema = window['tema'];
  }

  voltar() {
    if( this.clicouVoltar.observers.length > 0 ) {
      this.clicouVoltar.emit(true);
      return;
    }

    if(this.retorno)
      this.dominiosService.navegueParaUrl(this.retorno)
    else
      this._location.back();
  }

  fechar() {
    this.fechou.emit(true);
  }
}
