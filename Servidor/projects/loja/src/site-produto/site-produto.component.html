<app-header-tela [titulo]="'Detalhes Do Produto'" [exibirFechar]="true" (fechou)="fecheTela()" ></app-header-tela>

<div class="k-i-loading ml-1 mr-1 centralizado" *ngIf="carregando"  ></div>

<div *ngIf="itemPedido && produto">
  <div>
    <div>
      <div class="mt-2">
        <div class="row">
          <div class="col-12" *ngIf="(imagens && imagens.length > 0)">
            <div class="row">
              <div class="col-auto" *ngIf="imagens.length > 1" style="padding-left: 2px; padding-right: 2px">
                <div style="text-align: center">
                  <ng-container *ngFor="let imagem of imagens; let i = index">
                      <img  class="img-pequena img-fluid rounded" (mouseover)="selecionou(i)" (touchend)="selecionou(i)" [class.selecionada]="i == selecionada" [src]="'https://fibo.promokit.com.br/images/empresa/' + imagens[i].linkImagem"/><br>
                  </ng-container>
                </div>
              </div>
              <div class="col">

                <div style="text-align: center;" >
                  <img  class="img img-fluid rounded" (click)="exibaFullScreen()" [src]="'https://fibo.promokit.com.br/images/empresa/' + imagens[selecionada].linkImagem"/>
                </div>

                <div  style="position: absolute;left: -1500px">
                  <ng-image-slider #nav    [images]="imagensSlider"  (imageClick)="abriuImagem($event)"   [autoSlide]="1" slideImage="1"></ng-image-slider>
                </div>

              </div>
            </div>

          </div>

          <div class="col-12"  [ngClass]="{'esgotado': produto.indisponivel}">
            <h4>{{produto.nome}}</h4>

            <p class="descricao" [innerHTML]="produto.descricao"></p>

            <app-tags-alimentar [tags]="produto.tags" [exibirLabel]="true" *ngIf="produto.tags?.length"></app-tags-alimentar>



            <h4 class="preco"    *ngIf="!produto.brinde">
                <span *ngIf="produto.valorMinimo" class="text-muted font-14  ">A partir de</span>
              <ng-container *ngIf="!produto.precoInical">

                {{(produto.valorMinimo ? produto.valorMinimo : produto.preco) | currency: 'BRL'}}

                <span class=" text-muted font-13" *ngIf="exibirUnidade()"> / kg </span>
              </ng-container>
              <ng-container *ngIf="produto.precoInical">
                {{ produto.precoInical | currency: 'BRL'}} / {{produto.valorInicial}} {{produto.unidadeMedida.sigla}}
              </ng-container>

              </h4>
            <h4 class="preco"  *ngIf="produto.brinde" >
               {{produto.valorResgate}} {{produto.acumulo}}
            </h4>


            <h4 class="preco antigo"  *ngIf="produto.precoAntigo">
                {{(produto.precoAntigo) | currency: 'BRL'}}
              </h4>
              <i *ngIf="produto.qtdMaxima"><label class="text-muted" style="font-size: 10px">*Até {{produto.qtdMaxima}} por pedido&nbsp;&nbsp;</label></i>
            <ng-container *ngIf="!produto.indisponivel">
                <button class="btn btn-blue mt-2"  *ngIf="exibirUnidade()" (click)="informarPeso()">
                  ({{itemPedido.qtde | number:'1.0-2'}}{{itemPedido.produto.unidadeMedida.sigla}}) Alterar o peso</button>

                <button class="btn btn-blue mt-0 btn-sm"  *ngIf="!exibirUnidade()" (click)="informarNovaQtde()" [hidden]="exibirComboSelecao">
                  Alterar Quantidade</button>
            </ng-container>

            <h5 *ngIf="produto.indisponivel" class="text-danger">Indisponível!</h5>
        </div>
      </div>
    </div>


      <ng-container *ngIf="produto.tamanhos">

        <app-adicionais-customizados #adicionaisCustomizados [itemPedido]="itemPedido" (alterouTamanho)="onAlterouTamanho($event)" >

        </app-adicionais-customizados>

      </ng-container>

      <div *ngFor="let campoAdicional of produto.camposAdicionais;  " id="adicional{{campoAdicional.posicao}}">
        <app-site-campo-adicional #adicionalComponent [id]="'adicional_' + campoAdicional.id" [campoAdicional]="campoAdicional" [produto]="produto"
                                  [itemPedido]="itemPedido" [posicao]="campoAdicional.posicao"
               (onDesmarcouOpcao)="desmarcouNovaOpcao($event)"  (onMarcouOpcao)="escolheuNovaOpcao($event)" ></app-site-campo-adicional>
      </div>

      <ng-container *ngFor="let sabor of itemPedido.sabores">
        <ng-container *ngIf="sabor.produto !== produto.id">
          <div *ngFor="let adicionalSabor of sabor.camposAdicionais; ">

            <app-site-campo-adicional #adicionalComponent class="campo-adicional" [campoAdicional]="adicionalSabor" [produto]="sabor"
                                      [itemPedido]="itemPedido" [posicao]="adicionalSabor.posicao" [exibirSabor]="true" [tamanhoSabor]="sabor.produtoTamanho"
                                      (onDesmarcouOpcao)="desmarcouNovaOpcao($event)"  (onMarcouOpcao)="escolheuNovaOpcao($event)" >

            </app-site-campo-adicional>
          </div>
        </ng-container>
      </ng-container>

      <div class="mt-2">
      <div class="mt-3"></div>
      <div class="form-group mb-3">
        <label for="observacao"><i class="fas fa-comment-dots"></i> Alguma Observação?</label>
        <textarea type="text" class="form-control" autocomplete="off" maxlength="255"
               id="observacao" name="observacao" [(ngModel)]="itemPedido.observacao" #observacao="ngModel"
               placeholder="Inclua uma observação sobre o pedido." value="" required>
        </textarea>

          <div class="invalid-feedback">
          </div>
        </div>
      </div>
    </div>
  </div>

  <app-site-adicionar-produto #siteAdicionarProduto  (onInformarPeso)="informarPeso()"
     [itemPedido]="itemPedido" [pedido]="pedido" [indiceItem]="indiceItem" [window]="window"  >

  </app-site-adicionar-produto>


  <kendo-window [width]="300" title="Alterar peso"  [left]="leftWindow" [top]="topWindow"
                *ngIf="alterarPeso" (close)="fecheModal(false)" #alterarPesoModal>
    <p class="alert alert-danger mt-1 mb-2" *ngIf="msgErroAlterarPeso">
      {{msgErroAlterarPeso}}
    </p>

    <input #inputNovoPeso  name="novoPeso" type="text"   class="form-control troco" step="{{obtenhaIncremento()}}"
           type="text" inputmode="decimal" appSelecionarNoFoco
           currencyMask [options]="{ prefix: '', suffix: ' ' + this.produto.unidadeMedida.sigla, precision:
            this.produto.unidadeMedida.sigla === 'g' ? 0 : 3  , thousands: '', decimal: ',', align: 'left',
            allowNegative: false }"
           [(ngModel)]="novoPeso"  />

    <div class="clearfix mt-2"></div>
    <button type="button" class="btn btn-success mr-2"  (click)="salveNovoPeso()"  >Salvar</button>
    <button type="button" class="btn btn-light  " data-dismiss="modal"  (click)="fecheModal(false)">Cancelar</button>

  </kendo-window>

  <kendo-window [width]="300" title="Alterar Quantidade"  [left]="leftWindow" [top]="topWindow"
                *ngIf="alterarQtde" (close)="fecheModal(false)" #alterarPesoModal>
    <p class="alert alert-danger mt-1 mb-2" *ngIf="msgErroAlterarQtde">
      {{msgErroAlterarQtde}}
    </p>

    <ng-container *ngIf="!exibirComboSelecao">
      <input #inputNovaQtde  name="novoPeso" type="text"   class="form-control troco" step="1"
             type="text" inputmode="decimal" appSelecionarNoFoco
             currencyMask [options]="{ prefix: '', suffix: ' UN', precision: 0, thousands: '', decimal: ',', align: 'left',
            allowNegative: false }"
             [(ngModel)]="novaQtde"  />
    </ng-container>


    <ng-container *ngIf="!exibirComboSelecao">

    </ng-container>



    <div class="clearfix mt-2"></div>
    <button type="button" class="btn btn-success mr-2"  (click)="salveAltereQtde()"  >Salvar</button>
    <button type="button" class="btn btn-light  " data-dismiss="modal"  (click)="fecheModalQtde()">Cancelar</button>

  </kendo-window>

</div>


