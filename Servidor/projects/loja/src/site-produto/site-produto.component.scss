.centralizado {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -20px !important;
  margin-left: -20px !important;
}



.k-i-loading{
  font-size: 40px;height: 90px;
}


.preco {
  color: #6db31b;

  display: inline-block;
  margin-right: 10px;

  &.antigo{
    color: #ccc;
    text-decoration: line-through;
  }


}

.esgotado{
  .preco {
    color: #ccc !important;
  }

}




.container {
  max-width: 600px !important;
}


img.img {
  max-height: 300px;
}



img.img-pequena {
  object-fit: cover;
  margin-bottom: 5px;
  margin-left: 5px;
  margin-rithg: 5px;
  width: 40px;
  height: 40px;
  border: solid 1px lightgrey;
  cursor: pointer;
}

img.img-pequena.selecionada {
  border: solid 1px #2b7dc3;
  box-shadow: 0 1px 5px #1167b1;
}

@media (max-width: 900px) {
  #alertaFechado{
    .modal-dialog {
      margin-left: 10px;
      margin-right: 10px;
    }
  }
}

@media (max-width: 767px) {
  img.img-pequena {
    width: 50px;
    height: 50px;
  }
}

::ng-deep .tema-personalizado {
  .descricao {
    color: var(--cor-texto-secundaria, #333) !important;
  }
}

.form-group {
  margin-bottom: 100px !important;
}

