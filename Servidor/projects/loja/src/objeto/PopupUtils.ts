import {DialogCloseResult, DialogRef} from "@progress/kendo-angular-dialog";
import {ActivatedRoute, Router} from "@angular/router";
import {Location} from "@angular/common";

export class PopupUtils {
  static calculeAlturaLarguraMenor(isMobile){
    let altura: any = window.innerHeight - 100;
    if( isMobile ) {
      altura = '100%';
    }

    let largura: any = '100%';

    if( window.innerWidth > 600 ) {
      largura = 'calc(100% - ' + (window.innerWidth - 600) + 'px)';
    }

    return { altura: altura, largura: largura}
  }

  static calculeAlturaLargura(isMobile){
    let altura: any = window.innerHeight - 100;
    if( isMobile ) {
      altura = '100%';
    }

    let largura: any = '100%';

    if( window.innerWidth > 600 ) {
      largura = 'calc(100% - ' + (window.innerWidth - 600) + 'px)';
    }

    return { altura: altura, largura: largura}
  }

  static abraJanela(router: Router, location: Location, activatedRoute: ActivatedRoute, windowRef: DialogRef, params: any,
                    onClose = null, classe = 'dialog-produto') {
    const $divDialog = windowRef.dialog.location.nativeElement;

    if( $divDialog ) {
      const $divDialogContent = $divDialog.getElementsByClassName('k-dialog-content');

      if( $divDialogContent.length > 0 ) {
        $divDialogContent[0].classList.add(classe);
      }
    }

    const $elemento: HTMLElement = document.querySelector('.carrinho_desktop .is-sticky');

    if( document.body.className.indexOf('mobile') === -1 ) {
      //document.body.style.overflow = "hidden";
      document.body.style.paddingRight = "17px";

      if ($elemento) $elemento.style.paddingRight = '17px';
    }
    document.body.classList.add("noscroll");

    let deveFazerBack = false;

    router.navigate([], {
      relativeTo: activatedRoute,
      queryParams: params,
      queryParamsHandling: 'merge',
    }).then( mudou => {
      deveFazerBack = mudou;
    });

    windowRef.result.subscribe((result: any) => {
      if(onClose) onClose(result)

      if( result.back ) {
        document.body.classList.remove("noscroll");
        document.body.style.paddingRight = "";
        if( $elemento ) $elemento.style.paddingRight = '';
      }
      else if (result instanceof DialogCloseResult) {
        document.body.classList.remove("noscroll");

        document.body.style.paddingRight = "";
        if( $elemento ) $elemento.style.paddingRight = '';

        if( deveFazerBack ) {
          location.back();
        } else {
          router.navigate([], {
            relativeTo: activatedRoute,
            queryParams: {
              c: null,
              e: null
            },
            replaceUrl: true,
            queryParamsHandling: 'merge',
          }).then( () => {});
        }
      }
    });

    return windowRef;
  }
}
