.k-radio + .k-radio-label {
  margin-left: 0px !important;
}

kendo-label.k-radio-label > .k-label:first-child {
  padding-left: 8px !important;
}

.black_friday_2022 {
  input {
    background-color: #2e2e2e !important;
    border: solid 1px #0000 !important;
    color: #fff !important;
  }

  input[type="radio"], input[type="checkbox"] {
    background-color: #cfcfcf !important;
    border: solid 1px #0000 !important;
    color: #1a1a1a !important;
  }

  textarea {
    background-color: #2e2e2e !important;
    border: solid 1px #0000 !important;
    color: #fff !important;
  }

  .caixa_login {
    background: #0e0e0e !important;
  }

  label {
    color: #cfcfcf !important;
  }

  .text-dark {
    color: #cfcfcf !important;
  }

  h4 {
    color: #cfcfcf;
  }

  .text-black-50 {
    color: #676767 !important;
  }

  .bg-light {
    background-color: #0e0e0e !important;

    h4 {
      color: #cfcfcf;
    }
  }

  .footer {
    background: #0e0e0e !important;
  }

  .navbar {
    &.bg-light {
      h4 {
        color: #cfcfcf;
      }

      .btn-outline-blue {
        color: #cfcfcf;
        border-color: #cfcfcf;
      }
    }
  }
}

.cacau_show {
  --cor-fundo-site: #18181b;
  --cor-fundo-elementos: #222224;
  --cor-texto: rgb(250, 250, 250);
  --cor-texto-primaria: rgb(250, 250, 250);
  --cor-texto-secundaria: rgb(179, 179, 179);
  --cor-botao: #694a43;
  --cor-texto-botao: rgb(250, 250, 250);
  --cor-borda: rgb(42, 37, 34);
  --cor-destaque: rgb(218, 98, 11);
  --cor-hover: #ffb52e;
  --cor-gradiente-1: #222224;
  --cor-gradiente-2: #1e1e20;
  --cor-sombra: rgba(0, 0, 0, 0.2);
  --cor-preco: #c75a0c;

  background-color: var(--cor-fundo-site) !important;
  color: var(--cor-texto-primaria);

  input {
    background-color: var(--cor-fundo-elementos) !important;
    border: solid 1px var(--cor-borda) !important;
    color: var(--cor-texto-primaria) !important;
    box-shadow: none;
    transition: all 0.3s ease;

    &:focus {
      border-color: var(--cor-destaque) !important;
      box-shadow: none;
    }
  }

  input[type="radio"], input[type="checkbox"] {
    background-color: #694a43    !important;
    border: solid 1px var(--cor-borda) !important;
    color: var(--cor-fundo-site) !important;
  }

  textarea {
    background-color: var(--cor-fundo-elementos) !important;
    border: solid 1px var(--cor-borda) !important;
    color: var(--cor-texto-primaria) !important;
    box-shadow: none;
    transition: all 0.3s ease;

    &:focus {
      border-color: var(--cor-destaque) !important;
      box-shadow: none;
    }
  }

  .caixa_login {
    background: var(--cor-fundo-elementos) !important;
  }

  label {
    color: var(--cor-texto-primaria) !important;
  }

  .text-dark {
    color: var(--cor-texto-primaria) !important;
  }

  h4 {
    color: var(--cor-texto-primaria);
  }

  .text-black-50 {
    color: var(--cor-texto-secundaria) !important;
  }

  .bg-light {
    background-color: var(--cor-fundo-site) !important;

    h4 {
      color: var(--cor-texto-primaria);
    }
  }

  .footer {
    background: var(--cor-fundo-site) !important;
    border-top: solid 1px var(--cor-borda) !important;
  }

  .navbar {
    &.bg-light {
      h4 {
        color: var(--cor-texto-primaria);
      }

      .btn-outline-blue {
        color: var(--cor-texto-primaria);
        border-color: var(--cor-destaque);
      }
    }
  }

  .container, .container-fluid {
    background-color: var(--cor-fundo-site);
    box-shadow: none;
    border-radius: 0;
    backdrop-filter: none;
  }

  .card {
    background-color: var(--cor-fundo-elementos);
    border: none;
    box-shadow: none;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  .list-group-item {
    background-color: var(--cor-fundo-elementos);
    border: none;
    margin-bottom: 4px;
    transition: all 0.3s ease;

    &:hover {
      transform: none;
      border-color: var(--cor-destaque);
    }
  }

  .modal-content {
    background-color: var(--cor-fundo-elementos);
    border: none;
    box-shadow: 0 8px 16px var(--cor-sombra);
    backdrop-filter: none;
  }

  .modal-header {
    border-bottom-color: var(--cor-borda);
  }

  .modal-footer {
    border-top-color: var(--cor-borda);
  }

  .close {
    color: var(--cor-texto-primaria);
  }

  .bg-white, .bg-light {
    background-color: var(--cor-fundo-elementos) !important;
  }

  .text-dark, .text-body {
    color: var(--cor-texto-primaria) !important;
  }

  .text-muted {
    color: var(--cor-texto-secundaria) !important;
  }

  .border {
    border-color: var(--cor-borda) !important;
  }

  .btn-outline-primary {
    color: var(--cor-texto-secundaria);
    border-color: var(--cor-borda);
    &:hover {
      background-color: var(--cor-botao);
      color: var(--cor-texto-botao);
    }
  }

  .btn-primary {
    background-color: var(--cor-botao);
    border: none;
    color: var(--cor-texto-botao);
    box-shadow: none;
    transition: all 0.3s ease;

    &:hover {
      transform: none;
      box-shadow: none;
      background-color: var(--cor-hover);
    }

    &:active {
      transform: none;
    }
  }

  .form-control {
    background-color: var(--cor-fundo-elementos);
    border-color: var(--cor-borda);
    color: var(--cor-texto-primaria);
    &::placeholder {
      color: var(--cor-texto-secundaria);
    }
  }

  .dropdown-menu {
    background-color: var(--cor-fundo-elementos);
    border: 1px solid var(--cor-borda);
    box-shadow: 0 4px 8px var(--cor-sombra);
    backdrop-filter: none;

    .dropdown-item {
      transition: all 0.3s ease;
      color: var(--cor-texto-primaria);

      &:hover {
        background-color: var(--cor-fundo-site);
        color: var(--cor-destaque);
        padding-left: 1.5rem;
      }
    }
  }

  .table {
    th {
      background-color: var(--cor-fundo-elementos);
      border-bottom: 1px solid var(--cor-borda);
    }

    td {
      border-color: var(--cor-borda);
    }

    tbody tr {
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--cor-fundo-site);
      }
    }
  }

  .nav-link {
    position: relative;
    color: var(--cor-texto-primaria);
    transition: all 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 0;
      height: 2px;
      background-color: var(--cor-destaque);
      transition: width 0.3s ease;
    }

    &:hover {
      color: var(--cor-destaque);
      &::after {
        width: 100%;
      }
    }
  }

  .badge {
    background-color: var(--cor-destaque);
    border: none;
    box-shadow: none;
    color: var(--cor-texto-botao);
  }

  .alert {
    background-color: var(--cor-fundo-elementos);
    border-color: var(--cor-borda);
    color: var(--cor-texto-primaria);
  }

  .progress {
    background-color: var(--cor-fundo-site);
    .progress-bar {
      background-color: var(--cor-botao);
    }
  }

  .card-produto {
    background-color: var(--cor-fundo-elementos);
    border: none;
    box-shadow: none;
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;

    &:hover {
      transform: none;
      box-shadow: none;
    }

    .card-body {
      background: transparent;
    }

    .card-footer {
      background-color: var(--cor-fundo-elementos);
      border-top: none;
    }
  }

  .preco {
    color: var(--cor-preco) !important;
    font-weight: bold;
  }

  .valor {
    color: var(--cor-preco) !important;
    font-weight: bold;
  }

  .breadcrumb {
    background-color: var(--cor-fundo-site);
    .breadcrumb-item {
      color: var(--cor-texto-secundaria);
      &.active {
        color: var(--cor-texto-primaria);
      }
      & + .breadcrumb-item::before {
        color: var(--cor-borda);
      }
    }
  }

  .pagination {
    .page-item {
      .page-link {
        background-color: var(--cor-fundo-elementos);
        border-color: var(--cor-borda);
        color: var(--cor-texto-primaria);
        &:hover {
          background-color: var(--cor-fundo-site);
          color: var(--cor-destaque);
        }
      }
      &.active .page-link {
        background-color: var(--cor-botao);
        border-color: var(--cor-botao);
        color: var(--cor-texto-botao);
      }
    }
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--cor-fundo-site);
  }

  ::-webkit-scrollbar-thumb {
    background-color: var(--cor-botao);
    border-radius: 4px;

    &:hover {
      background-color: var(--cor-hover);
    }
  }

  /* Estilos específicos para produtos baseados na imagem */
  .produtos_categoria {
    background-color: var(--cor-fundo-site);
    padding: 0;
  }

  .produto {
    background-color: var(--cor-fundo-elementos);
    border: none;
    border-radius: 8px;
    margin-bottom: 10px;
    padding: 15px;
    transition: all 0.2s ease;

    .nome {
      color: var(--cor-texto-primaria);
      font-weight: 500;
      margin-bottom: 5px;
    }

    .descricao {
      color: var(--cor-texto-secundaria);
      font-size: 0.9em;
      margin-bottom: 10px;
    }

    .preco {
      color: var(--cor-preco) !important;
      font-weight: bold;
      font-size: 1.1em;
    }

    img {
      border-radius: 6px;
      object-fit: cover;
    }
  }

  /* Estilos para o cabeçalho de categorias */
  .categorias-header {
    background-color: var(--cor-fundo-elementos);
    border-radius: 0;
    border: none;
    padding: 15px;
    margin-bottom: 15px;

    .categoria-item {
      color: var(--cor-texto-secundaria);
      padding: 8px 15px;
      border-radius: 20px;
      transition: all 0.2s ease;

      &.active {
        background-color: var(--cor-destaque);
        color: var(--cor-texto-botao);
      }

      &:hover:not(.active) {
        color: var(--cor-texto-primaria);
      }
    }
  }

  /* Estilos para o rodapé de navegação */
  .tab-bar {
    background-color: var(--cor-fundo-elementos);
    border-top: none;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);

    .tab-item {
      color: var(--cor-texto-secundaria) !important;

      &.ativo {
        color: var(--cor-destaque) !important;

        .tab-title {
          color: var(--cor-destaque) !important;
        }

        i {
          color: var(--cor-destaque) !important;
        }
      }
    }
  }

  /* Estilos para os preços com desconto */
  .preco-original {
    text-decoration: line-through;
    color: var(--cor-texto-secundaria) !important;
    font-size: 0.9em;
    margin-right: 5px;
  }

  .preco-com-desconto {
    color: var(--cor-preco) !important;
    font-weight: bold;
  }

  /* Estilos para as estrelas de avaliação */
  .avaliacao {
    color: var(--cor-destaque);
    font-size: 0.9em;

    .estrelas {
      margin-right: 5px;
    }
  }

  /* Estilos para os botões de quantidade */
  .quantidade-controle {
    display: flex;
    align-items: center;

    .btn-quantidade {
      background-color: var(--cor-fundo-site);
      color: var(--cor-texto-primaria);
      border: none;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;

      &:hover {
        background-color: var(--cor-destaque);
        color: var(--cor-texto-botao);
      }
    }

    .quantidade-valor {
      padding: 0 10px;
      color: var(--cor-texto-primaria);
    }
  }
}

.form-group {
  .k-datepicker, .k-timepicker, .k-combobox {
    padding: 0px !important;
  }
}

.form-control {
  &.k-widget {
    padding: 0px !important;
  }
}

/* Fix for dark themes */
.tema-personalizado {
  .k-popup {
    .k-list-item {
      color: var(--cor-texto-primaria, #212529) !important;
      background-color: var(--cor-fundo-site, #fff) !important;

      &:hover {
        background-color: var(--cor-fundo-site, #f8f9fa) !important;
      }

      &.k-selected {
        background-color: var(--cor-fundo-elementos) !important;
        color: var(--cor-texto-primaria) !important;

        span {
           color: var(--cor-texto-primaria) !important;
        }
      }
    }
  }
}

.pac-container {
  z-index: 100001 !important; /* Ajuste o valor conforme necessário */
}

.tema-personalizado {
  --cor-texto-muted: var(--cor-texto-secundaria, #6c757d);

  .modal-content {
    background: var(--cor-fundo-elementos) !important;
  }

  // Estilos globais para todos os elementos dentro da classe tema-personalizado
  background-color: var(--cor-fundo-site, #fff);
  color: var(--cor-texto, inherit);

  h4, h3, h2, h1 {
    color: var(--cor-texto-primaria, inherit) !important;
  }

  h5, h6 {
    color: var(--cor-texto-secundaria, inherit) !important;
  }

  .text-muted {
    color: var(--cor-texto-muted) !important;
  }

  span, p, label {
    color: var(--cor-texto, inherit);

    &.badge-info {
      background-color: var(--cor-botao, #007bff) !important;
      color: var(--cor-texto-botao, #fff) !important;
    }

    &.badge-danger {
      background-color: var(--cor-botao, #F67682) !important;
      color: var(--cor-texto-botao, #fff) !important;
    }
  }

  /* Ensure Kendo UI Calendar text uses its default styling */
  .k-calendar, .k-calendar-view, .k-calendar-cell {
    span, label {
      color: inherit;
    }
  }

  .form-control {
    background-color: var(--cor-fundo-elementos, #fff);
    border-color: var(--cor-borda, #ced4da);
    color: var(--cor-texto, inherit);
  }

  .form-control[readonly] {
    opacity: 0.4;
    background-color: var(--cor-fundo-elementos, #fff) !important;
  }

  .bg-light {
    background-color: var(--cor-fundo, #fff) !important;
  }

  .k-window-titlebar {
    color: var(--cor-texto-primaria) !important;
    background-color: var(--cor-fundo) !important;
  }

  .k-window {
    background-color: var(--cor-fundo-elementos) !important;
    border: solid 1px var(--cor-borda) !important;
  }

  .btn-success {
    background-color: var(--cor-botao) !important;
    border-color: var(--cor-botao) !important;
    color: var(--cor-texto-botao) !important;
  }

  textarea {
    background:  var(--cor-fundo-elementos) !important;
    border: solid 1px var(--cor-borda) !important;
    color: var(--cor-texto-primaria) !important;
  }

  input {
    border: solid 1px var(--cor-borda) !important;
    color: var(--cor-texto-primaria) !important;
    background-color:  var(--cor-fundo-elementos) !important;

    &::placeholder {
      color: var(--cor-texto-secundaria) !important;
    }
  }

  footer {
    border-top: solid 1px var(--cor-borda-rodape, var(--cor-borda, #dee2e6)) !important;
    background: var(--cor-fundo-rodape, var(--cor-fundo, #222)) !important;
    color: var(--cor-texto-rodape, var(--cor-texto-fundo, white)) !important;

    .nav-link, a, p, span, small, .text-muted {
      color: var(--cor-texto-botao, var(--cor-texto-fundo, white)) !important;
    }

    .border, .border-top, .border-bottom, .border-left, .border-right {
      border-color: var(--cor-borda-rodape, var(--cor-borda, #dee2e6)) !important;
    }

    hr {
      border-color: var(--cor-borda-rodape, var(--cor-borda, #dee2e6)) !important;
      opacity: 0.5;
    }
  }

  .carrinho {
    border-top: solid 1px var(--cor-borda-rodape, var(--cor-borda, #dee2e6)) !important;
    background: var(--cor-fundo-rodape, var(--cor-fundo, #222)) !important;
    color: var(--cor-texto-rodape, var(--cor-texto-fundo, white)) !important;

    .nav-link, a, p, span, small, .text-muted {
      color: var(--cor-texto-primaria) !important;
    }

    .border, .border-top, .border-bottom, .border-left, .border-right {
      border-color: var(--cor-borda-rodape, var(--cor-borda, #dee2e6)) !important;
    }

    hr {
      border-color: var(--cor-borda-rodape, var(--cor-borda, #dee2e6)) !important;
      opacity: 0.5;
    }
  }

  .btn-primary, .btn-blue {
    background-color: var(--cor-botao) !important;
    border-color: var(--cor-botao) !important;
    color: var(--cor-texto-botao) !important;
  }

  .k-radio:checked,
  .k-radio.k-checked {
    background-color: var(--cor-texto-botao) !important;
    background-image: none !important;
    box-shadow: inset 0 0 0 4px var(--cor-botao) !important;
    border-color: var(--cor-botao) !important;
  }

  .k-radio {
    border-color: var(--cor-borda, #e2e3e3);
    transition: all 0.2s ease;
  }

  .k-label {
    color: var(--cor-texto-primaria) !important;
  }

  .preco-extra {
    background-color: var(--cor-preco-adicional) !important;
    color: var(--cor-texto-primaria) !important;
  }

  .btn-outline-blue {
    background-color: var(--cor-botao) !important;
    border-color: var(--cor-botao) !important;
    color: var(--cor-texto-botao) !important;
  }

  input[type="radio"], input[type="checkbox"] {
    border: solid 2px var(--cor-texto-primaria) !important;
    color: var(--cor-fundo-site) !important;
  }

  input[type="checkbox"]:checked {
    background-color: var(--cor-botao) !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    border-color: var(--cor-botao) !important;
  }

  /* Estilos para as tabs do Kendo UI */
  .k-tabstrip {
    .k-tabstrip-items {
      background-color: var(--cor-fundo-elementos, #f7f8f8) !important;
      border-bottom: solid 1px var(--cor-borda);

      .k-item {
        color: var(--cor-texto-secundaria, #6c757d) !important;
        background-color: var(--cor-fundo-site, #f7f8f8) !important;
        border-color: var(--cor-borda, #dee2e6) !important;

        &.k-active {
          color: var(--cor-texto-primaria, #212529) !important;
          background-color: var(--cor-fundo-elementos, #fff) !important;
          border-bottom-color: var(--cor-fundo-elementos, #fff) !important;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 2px;
            background-color: var(--cor-botao, #007bff);
          }
        }

        &:hover:not(.k-state-active) {
          color: var(--cor-texto-primaria, #007bff) !important;
        }
      }
    }

    .k-content {
      background-color: var(--cor-fundo-elementos, #fff) !important;
      border-color: var(--cor-borda, #dee2e6) !important;
      color: var(--cor-texto-primaria, #212529) !important;
    }
  }

  /* Estilos específicos para .nav-bordered utilizada nas tabs */
  .nav-bordered.k-tabstrip {
    border-bottom: 1px solid var(--cor-borda, #dee2e6) !important;

    .k-tabstrip-items {
      .k-item {
        margin-bottom: -1px;

        &.k-state-active {
          border-bottom: 2px solid var(--cor-botao, #007bff) !important;

          &::after {
            display: none;
          }
        }
      }
    }
  }


  // Aplicando a cor de texto do topo nos elementos de navegação
  .navbar, .navbar-light, nav.bg-light {
    .navbar-brand,
    .nav-link,
    h1, h2, h3, h4, h5, h6,
    .btn-outline-blue,
    .text-dark,
    i.fa, i.fas, i.far {
      color: var(--cor-texto-topo, inherit) !important;
    }
  }

  button {
    color: var(--cor-texto-botao, inherit) !important;

    &.k-button {
      background-color: var(--cor-botao, #007bff) !important;
    }
  }

  // Aplicando cor de texto do topo em elementos específicos do cabeçalho
  .topo {
    h4, p, span, a, button, .text-muted {
      color: var(--cor-texto-topo, inherit) !important;
    }

    .btn-outline-blue {
      border-color: var(--cor-texto-topo, #007bff);

      &:hover {
        background-color: var(--cor-texto-topo, #007bff);
        color: var(--cor-fundo, #ffffff) !important;
      }
    }
  }
}

// Adicionar estilos para o menu lateral de produto
.produto-menu-lateral {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: auto !important;
  margin: 0 !important;
  border-radius: 0 !important;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3) !important;

  .k-dialog-wrapper {
    position: static !important;
  }

  .k-dialog {
    border-radius: 0 !important;
    border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
    max-width: none !important;
    height: 100% !important;
    animation: slide-in-right 0.3s ease-out !important;
  }

  .k-dialog-content {
    padding: 0 !important;
    overflow-y: auto !important;
  }

  @keyframes slide-in-right {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }
}

// Ajustar o overlay para o menu lateral
.k-overlay.produto-menu-lateral-overlay {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

// Adicionar ao arquivo de estilos globais
.oculto-tablet-pedidos {
  display: none !important;
}

body.tablet-pedidos-ativo {
  padding-top: 0 !important;

  .container-fluid, .container {
    padding-top: 0 !important;
    margin-top: 0 !important;
  }

  app-tablet-pedidos {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background-color: #18181b;
  }
}

/* Estilos para a dialog de confirmação de pedido */
.confirmacao-pedido-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.confirmacao-pedido-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
}

.confirmacao-pedido-container {
  position: relative;
  width: 360px;
  padding: 30px 20px;
  background-color: #222;
  background-image: linear-gradient(135deg, #222, #333);
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  text-align: center;
  z-index: 1;
  animation: scaleIn 0.3s ease;

  &::before {
    content: "";
    display: block;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #ff6a00, #ee0979);
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 12px 12px 0 0;
  }
}

.confirmacao-icon {
  position: relative;
  width: 90px;
  height: 90px;
  margin: 0 auto 25px;

  i.fa-utensils {
    position: absolute;
    font-size: 50px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #ff6a00;
    background: -webkit-linear-gradient(45deg, #ff6a00, #ee0979);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    z-index: 1;
  }

  i.fa-check {
    position: absolute;
    font-size: 30px;
    right: 0;
    bottom: 0;
    background-color: #4CAF50;
    color: white;
    border-radius: 50%;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    animation: bounceIn 0.6s ease;
    z-index: 2;
  }
}

.confirmacao-titulo {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 10px;
  color: #bdb7af;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.confirmacao-mensagem {
  font-size: 16px;
  margin-bottom: 25px;
  color: rgba(255, 255, 255, 0.8);
}

.btn-confirmar {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 12px 40px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  min-width: 120px;
  margin-top: 10px;
  outline: none;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 106, 0, 0.2), rgba(238, 9, 121, 0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.3);

    &::before {
      opacity: 1;
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
}

body.confirmacao-pedido-ativo {
  overflow: hidden;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

::-webkit-scrollbar {
  height: 40px;
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #222;
}

::-webkit-scrollbar-thumb {
  background: #fff;
  border-radius: 4px;
}

// Estilização da scrollbar com comportamento mobile
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(51, 51, 51, 0.3);
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

// Só mostra a scrollbar durante a rolagem
*:hover::-webkit-scrollbar-thumb {
  opacity: 1;
}

*:not(:hover)::-webkit-scrollbar-thumb {
  opacity: 0;
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: transparent;
  }

::-webkit-scrollbar-track {
    background: var(--cor-fundo-site);
  }

::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 4px;
    color: yellow;
    background: var(--cor-texto-primaria);
    transition: opacity 0.3s ease;
  }

:hover::-webkit-scrollbar-thumb {
    opacity: 1;
  }

:not(:hover)::-webkit-scrollbar-thumb {
    opacity: 0;
  }

/* Garantir que h5 e h6 dentro de dropdown-menu tenham cor inherit quando tema personalizado estiver ativo */
.tema-personalizado .dropdown-menu {
  h5, h6, span {
    color: inherit !important;
  }
}

.noscroll {
  overflow: hidden;
  touch-action: none;
  -ms-touch-action: none;
  -webkit-overflow-scrolling: none;
  overscroll-behavior: none;
}
