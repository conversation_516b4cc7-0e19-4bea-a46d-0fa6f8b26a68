.footer{
  border-top: solid 1px #eeeeee;
  background: #fff;
  padding: 0px;
  position: fixed;
  z-index: 9999;
  position: absolute !important;
  bottom: 0px;
  width: 100%;
  left: 0% !important;
  background-color: #f7f8f8;
  padding-bottom: max(20px, env(safe-area-inset-bottom)); /* Espaço adaptativo */
  >div{
    padding: 12px 10px;
  }

  .input{
    display: inline-block;
    text-align: center;
    font-size: 16px;
    min-width: 35px;
    background: #fff;
    color: #000;
    margin-right: 5px;
  }

  .flex-fixed-width-item {
    margin-left: 10px;
    border: solid 1px #efefef;
    border-radius: 3px; background: #fff;

    .btn-outline-light:not(:disabled):not(.disabled):active,
    .btn-outline-light:hover {

      background-color:  #fff !important;
      border-color: #fff  !important;
    }


    .btn{
      color: #787878;
      position: relative;
    }

    .incre {
    }
    .decre{
      left: -2px;

    }

    &.com-unidade{
      .input{
        min-width: 60px;
      }
    }

  }

}
.btn-outline-light {
  border-color: transparent;
}


.decre:focus, .incre:focus {
  box-shadow: none !important;
}


@media (max-width: 900px) {
  .footer {
    position: fixed !important;

    &.powered-by {
      padding-bottom: calc(16px + env(safe-area-inset-bottom) + 10px);
    }

    .flex-fixed-width-item {

      &.com-unidade{
        .input {
          min-width: 60px;
        }
      }

    }
  }

  .espaco-footer {
    height: 70px;
  }
}


.btn-primary.disabled,   .btn-primary:disabled {
  background: #6DB31B !important;
  border: solid 1px #6DB31B !important;;
}
