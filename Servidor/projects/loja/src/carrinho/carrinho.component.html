<div *ngIf="exibirCarrinho">
  <div class="row carrinho {{tema}}"
       (click)="abraTelaCarrinho()" [ngClass]="{'deslocado': exibindoMenu, 'poweredby': exibirPoweredBy}">
    <div class="col flex-fixed-width-item pr-0">
      <span class="qtde_itens">{{pedido.qtde}}</span>
      <i class="fe-shopping-cart font-15"></i>
    </div>
    <div class="col ml-0 mr-0 text-center">
    <span>
    Ver Carrinho
    </span>
    </div>
    <div class="col-auto text-right mr-2">
    <span class="font-13">
      {{ pedido.total | currency: 'BRL'}}
    </span>
    </div>
  </div>
</div>



