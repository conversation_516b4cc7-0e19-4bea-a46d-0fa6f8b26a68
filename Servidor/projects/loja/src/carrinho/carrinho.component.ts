import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {CarrinhoService} from "../services/carrinho.service";
import {PedidoLoja} from "../objeto/PedidoLoja";
import {animate, state, style, transition, trigger} from "@angular/animations";
import {Router} from "@angular/router";
import {DominiosService} from "../services/dominios.service";
import {ConstantsService} from "../services/ConstantsService";

@Component({
  selector: 'app-carrinho',
  templateUrl: './carrinho.component.html',
  styleUrls: ['./carrinho.component.scss'],
  animations: [
    trigger('inivisivelVisivel', [
      state('invisivel', style({
        opacity: 0,
        top: '40px'
      })),
      state('visivel', style({
        opacity: 1,
        top: '0px'
      })),
      transition('invisivel => visivel', [
        animate('0.3s', style({ top: '0px', opacity: '1'})),
        animate('0.5s', style({ top: '0px', opacity: '1'})),
        animate('0.5s', style({ top: '0px', opacity: '.8'})),
      ])
    ])
  ]
})
export class CarrinhoComponent implements OnInit {
  @Input() exibindoMenu  ;
  @Input() tema  ;
  pedido: PedidoLoja;
  exibir = false;
  exibirCarrinho: any;
  linksExibiCarrinho = ['/', '/loja', '/loja/index', '/index', '/loja/busca', '/busca', '/desconto', '/categoria',
    '/loja/brindes', '/brindes'];
  @Output() clicouAbrirCarrinho = new EventEmitter<boolean>();
  exibirPoweredBy: any;

  constructor(private router: Router, private carrinhoService: CarrinhoService,
              private dominiosService: DominiosService, private constantsService: ConstantsService) {

  }

  ngOnInit() {
    this.constantsService.empresa$.subscribe(empresa => {
      if( !empresa ) return;

      this.exibirPoweredBy = empresa.exibirPoweredBy;
    });

    this.pedido = this.carrinhoService.obtenhaPedido();
    this.carrinhoService.alterouPedido.subscribe( (novoPedido) => {
      if( !novoPedido ) { return; }

      this.pedido = novoPedido;

      this.verifiqueDeveExibir();
    });

    setTimeout( () => {
      this.exibir = true;
    }, 500);
  }

  abraTelaCarrinho() {
    if( this.clicouAbrirCarrinho.observers.length > 0 ) {
      this.clicouAbrirCarrinho.emit(true);
      return;
    }

   this.dominiosService.navegueParaUrl('carrinho')
  }

  verifiqueDeveExibir() {
    let deveExibir: boolean = this.linksExibiCarrinho.indexOf(location.pathname) >= 0 ||
                      location.pathname.match(/local\/\w+$/) != null;

    if( location.pathname.indexOf('/categoria') !== -1 ) {
      deveExibir = true;
    }

    if( location.pathname.startsWith('/desconto') || location.pathname.startsWith('/busca/ecommerce')  ) {
      deveExibir = true;
    }

    if(deveExibir) {
      this.pedido = this.carrinhoService.obtenhaPedido();
      deveExibir = this.pedido.qtde > 0;
    }

    this.exibirCarrinho = deveExibir;

    if( window['tema'] )
      this.tema = window['tema'];
  }
}
