<kendo-tabstrip class="nav-bordered" id="tabs" name="tabs" (tabSelect)="alterouTipoPagamento($event)" *ngIf="pedido.pagamento">

  <kendo-tabstrip-tab [title]="'Pagar pelo site'"   [selected]="(tabSelect  === 0)" *ngIf="temPagamentoOnline()">
    <ng-template kendoTabContent>
      <div class="row escolher row">
        <div class="form-group col-12 mb-0" *ngIf="formasDePagamentoPix.length > 0 && estaHabilitado(formasDePagamentoPix[0], pedido)">
          <span class="mr-3 radio radio-blue  w-100"  *ngFor="let formaDePagamento of formasDePagamentoPix"
                [ngClass]="{'selecionado': pagamentoOnlineEstaSelecionado(formaDePagamento) }">

            <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"
                   [(ngModel)]="pedido.pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                   (ngModelChange)="alterouFormaDePagamentoParaPix()"
                   [required]="true"/>

             <img src="https://user-images.githubusercontent.com/33992396/99478353-00e4d600-2933-11eb-8228-4bafe8571507.png" style="width: 32px;" class="ml-3"/>
            <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline">
              PIX  <span class="text-muted float-right" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa">
                         <i>+{{formaDePagamento.taxaCobranca.percentual}}% taxa</i></span>
            </label>
          </span>

          <div class="invalid-feedback" *ngIf="!pedido.pagamento.formaDePagamento">
            <p>Escolha a forma de pagamengo do seu pedido</p>
          </div>

          <div class="ml-3 col" *ngIf="pedido.pagamento.formaDePagamento?.pix">

            <div class="form-group"  style="max-width: 200px" *ngIf="pedirCpfNoPix()">
              <h5  >Informe seu Cpf</h5>
              <div class="form-group">
                <input type="text" class="form-control" autocomplete="off" required  #txtCpfPix
                       id="cpf" name="cpf" [(ngModel)]="pedido.pagamento.dadosPix.cpf" #cpf="ngModel"
                       mask="000.000.000-00" cpfValido placeholder="___.___.___-__" value="">
                <div class="invalid-feedback">
                  <p *ngIf="cpf.errors?.required">Obrigatório</p>
                  <p *ngIf="cpf.errors?.cpfInvalido">CPF é invalido</p>
                </div>
              </div>
            </div>
            <div class="form-group" style="max-width: 350px" >
              <h5  >Informe seu Email</h5>
              <div class="input-group">
                <input id="emailPix" name="emailPix"  class="form-control" #txtEmailPix  email="true" type="email"
                       #emailPix="ngModel" placeholder="Informe seu email" [required]="true"
                       [(ngModel)]="pedido.pagamento.dadosPix.email"  />
                <div class="invalid-feedback">
                  <p *ngIf="emailPix.errors?.required">
                    Para pagamentos pix você deve informar seu email. Caso aconteça algum problema, usaremos esse email para te contactar.
                  </p>

                  <p *ngIf="emailPix.errors?.email">Informe um email válido.</p>

                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="form-group col-12 cartao-online mb-0"  *ngFor="let formaDePagamento of formasDePagamentoOnline">
          <div class="mt-1">
            <span class="radio radio-blue w-100" [hidden]="!estaHabilitado(formaDePagamento, pedido)"
                  [ngClass]="{'selecionado': pagamentoOnlineEstaSelecionado(formaDePagamento)}">
                        <input id="formaPagamento{{formaDePagamento.id}}" name="formaDePagamento" type="radio"
                               [(ngModel)]="pedido.pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                               [required]="true" (ngModelChange)="escolheuFormaDePagamentoOnline(formaDePagamento)"
                               #rdFormaDePagamento="ngModel"
                               [pagseguro]="{pedido: pedido, exigeCartao: exigeCartao, dadosCartao: pagamento.dadosCartao}"/>

                       <i class="fa fa-credit-card text-blue fa-2x ml-3" aria-hidden="true" style="    top: 5px;position: relative;"></i>



                        <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline">
                                 Cartão de Crédito
                                <span class="text-muted font-11 ml-1"
                                      *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa">
                                  (+{{formaDePagamento.taxaCobranca.percentual}}% taxa)
                                </span>

                              <small class="font-11 text-muted">
                                <br>{{formaDePagamento.configMeioDePagamento.meioDePagamento}}
                              </small>

                        </label>


                          </span>

            <ng-container *ngIf="rdFormaDePagamento.errors">
              <div class="invalid-feedback" *ngIf="rdFormaDePagamento?.errors?.endereco_obrigatorio">
                <p>Informe o endereço de cobrança da fatura do cartão de crédito</p>
              </div>
            </ng-container>
            <div class="invalid-feedback" *ngIf="rdFormaDePagamento?.errors?.dados_cartao_obrigatorio">
              <p>Informe os dados do cartão de cŕedito</p>
            </div>
          </div>


          <label class="ml-1 d-inline acao-cartao ">
            <div class="text-right" *ngIf="pagamentoOnlineEstaSelecionado(formaDePagamento)">
              <ng-container *ngIf="pedido.pagamento.dadosCartao">
                <div class="d-flex align-items-center">
                  <i class="fas fa-credit-card font-16"></i>
                  <span class="ml-2" *ngIf="!pedido.pagamento.dadosCartao.descricaoPagamento">
                          ****-{{pedido.pagamento.dadosCartao.ultimosNumeros}}
                        </span>
                  <span class="ml-2" *ngIf="pedido.pagamento.dadosCartao.descricaoPagamento">
                          {{pedido.pagamento.dadosCartao.descricaoPagamento}}
                        </span>
                  <button type="button" class="btn btn-blue ml-2" (click)="trocarCartao()">
                    <i class="far fa-credit-card mr-1 fa-lg"></i>
                    Alterar Cartão
                  </button>
                </div>
              </ng-container>
              <ng-container *ngIf="!pedido.pagamento.dadosCartao && pedido.entrega.formaDeEntrega">
                <button class="btn btn-blue" type="button" (click)="trocarCartao()">
                  <i class="far fa-credit-card mr-1 fa-lg"></i>
                  Adicionar Cartão
                </button>
              </ng-container>
            </div>
          </label>

        </div>

        <div class="col-12"  *ngIf="formaPagamentoCarteirasDigitais">
          <app-tunnapay-digitais [formaDePagamento]="formaPagamentoCarteirasDigitais" [pedido]="pedido"
                                 (definiuCartao)="setCartaoEscolhido($event)"
                                 (escolheuFormaPagamento)="escolheuFormaDePagamentoOnline($event)">

          </app-tunnapay-digitais>
        </div>

      </div>


    </ng-template>

  </kendo-tabstrip-tab>
  <kendo-tabstrip-tab [title]="'Pagar na entrega'" [selected]="(tabSelect  === 1)"  *ngIf="formasDePagamento.length">
    <ng-template kendoTabContent>
      <div class=""  >
        <div class="form-group mb-2 mt-2 escolher row">
          <ng-container *ngFor="let formaDePagamento of formasDePagamento">
            <div class="col-6 radio radio-blue mt-1"  [hidden]="!estaHabilitado(formaDePagamento, pedido)">
                        <span class="radio radio-blue"   [ngClass]="{'selecionado': pedido.pagamento?.formaDePagamento?.id === formaDePagamento.id }">
                            <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"
                                   [(ngModel)]="pedido.pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                                   #formaDePagamento="ngModel"
                                   [required]="true"  (ngModelChange)="escolheuFormaDePagamentoPresencial(formaDePagamento)"/>
                            <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline"> {{formaDePagamento.descricao}}

                            </label>
                            <label class="text-muted font-11 ml-1 d-inline" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa"
                            >(+{{formaDePagamento.taxaCobranca.percentual}}%)</label>

                          <label class="text-muted font-11 ml-1 d-inline" *ngIf="formaDePagamento.possuiDesconto && formaDePagamento.desconto > 0"
                          >(-{{formaDePagamento.desconto}}% taxa)</label>
                           </span>
            </div>
          </ng-container>
        </div>

        <ng-container *ngIf="pedido.pagamento?.formaDePagamento?.nome === 'dinheiro'" >
          <div  class="col ml-3" >
            <h6 class="mt-3"  >Vai precisar de troco?  </h6>

            <div class="form-group escolher troco">

                        <span [ngClass]="{'selecionado': pedido.pagamento.temTroco==='sim' }" class="radio radio-blue">
                          <input id="trocoSim" name="temTroco" type="radio"    kendoRadioButton class="k-radio"
                                 [(ngModel)]="pedido.pagamento.temTroco" value="sim"
                                 [required]="pedido.pagamento?.formaDePagamento?.nome === 'dinheiro'"/>
                          <label for="trocoSim" class="ml-1">&nbsp;Sim</label>

                        </span>

              <span [ngClass]="{'selecionado': pedido.pagamento.temTroco=== 'nao' }" class="radio radio-blue">
                          <input id="trocoNao" name="temTroco" type="radio"    kendoRadioButton class="k-radio"
                                 [(ngModel)]="pedido.pagamento.temTroco" value="nao"
                                 [required]="pedido.pagamento?.formaDePagamento?.nome === 'dinheiro'"/>
                          <label for="trocoNao" class="ml-1">&nbsp;Não</label>

                        </span>

              <div class="invalid-feedback" *ngIf="!pedido.pagamento.temTroco">
                informe se vai precisar de troco.
              </div>
            </div>
          </div>
          <div class="form-group col  ml-3" *ngIf="pedido.pagamento.temTroco ==='sim'">
            <h6 for="trocoParaM">Troco para quanto?</h6>
            <input id="trocoParaM" name="trocoParaM" type="text" inputmode="decimal" class="form-control troco" #txtTrocoM="ngModel"
                   [trocoMinimo]="obtenhaTotalPagar()"
                   (ngModelChange)="calculeTroco($event)" appSelecionarNoFoco
                   [(ngModel)]="pedido.pagamento.trocoPara"
                   currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',', align: 'left' }"/>

            <div class="invalid-feedback">
              <p *ngIf="txtTrocoM.errors?.trocoMinimo">
                A nota tem que ser maior que  {{obtenhaTotalPagar() | currency: "R$"}}</p>
            </div>
          </div>
        </ng-container>
      </div>
    </ng-template>
  </kendo-tabstrip-tab>

</kendo-tabstrip>
