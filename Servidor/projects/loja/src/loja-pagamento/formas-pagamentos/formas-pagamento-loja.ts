import {DialogCloseResult, DialogService} from "@progress/kendo-angular-dialog";
import {MyDetectorDevice} from "../../app/shared/MyDetectorDevice";
import {CarrinhoService} from "../../services/carrinho.service";
import {ApplicationRef, ElementRef, EventEmitter, Injectable, Output, ViewChild} from "@angular/core";
import {FormaDePagamento} from "../../objeto/FormaDePagamento";
import {TelaCartaoDeCreditoComponent} from "../../app/tela-cartao-de-credito/tela-cartao-de-credito.component";
import {DadosCartao} from "../../objeto/DadosCartao";
import {DadosPix} from "../../objeto/DadosPix";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {NgForm} from "@angular/forms";
import {ConstantsService} from "../../services/ConstantsService";
import {TabStripComponent} from "@progress/kendo-angular-layout";

declare  var $;
declare var fbq;
declare var gtag;

@Injectable()
export class FormasPagamentoLoja {
  @ViewChild('txtEmailPix', {static: false}) txtEmailPix: ElementRef;
  @ViewChild('txtCpfPix', {static: false}) txtCpfPix: ElementRef;
  @ViewChild('tabFormas') tabFormas: TabStripComponent;
  telaPagamento: TelaCartaoDeCreditoComponent;
  frm: NgForm;
  pedido: any  = {};
  pagamento: any  = { formaDePagamento: null};
  isMobile: boolean;
  exigeCartao: boolean;
  cashback: any;
  formasDePagamento: Array<FormaDePagamento> = [];
  formasDePagamentoOnline: Array<FormaDePagamento> = [];
  formasDePagamentoPix: Array<FormaDePagamento> = [];
  formasDePagamentoManual: Array<FormaDePagamento> = [];
  cpfObrigatorio: boolean;
  tabSelect = 0
  TIPOMANUAL =  'MANUAL'
  formaPagamentoCarteirasDigitais: any;
 constructor(protected dialogService: DialogService,    protected detectorDevice: MyDetectorDevice,
             protected carrinhoService: CarrinhoService,   protected app: ApplicationRef,
             protected constantsService: ConstantsService) {

   this.isMobile = this.detectorDevice.isMobile();

   this.constantsService.campoCpf$.subscribe( campoCpf => {
     this.cpfObrigatorio = campoCpf && !campoCpf.opcional;
   });

 }

  setFormasPagamento(empresa: any, pedido: any, pagamento: any, form: NgForm){
    this.pedido = pedido;
    this.pagamento = pagamento;
    this.frm = form;

    this.formasDePagamento = [];
    this.formasDePagamentoOnline = [];
    this.formasDePagamentoPix = [];
    this.formasDePagamentoManual = [];

    if(!this.pagamento.tipoDePagamento ) this.pagamento.tipoDePagamento = 'offline'

    empresa.formasDePagamento.forEach(formaDePagamento => {
      const formaDePagamentoPedido = pagamento.formaDePagamento;

      if( !formaDePagamento.exibirCardapio && !(formaDePagamentoPedido && formaDePagamentoPedido.id === formaDePagamento.id) )
        return;

      if( !formaDePagamento.online ) {
        if(formaDePagamento.nome === 'dinheiro'){
          this.formasDePagamento.splice(0, 0, formaDePagamento);
        } else  if(formaDePagamento.formaDePagamentoPdv && formaDePagamento.formaDePagamentoPdv.tipo === this.TIPOMANUAL) {
          //formaDePagamento.descricao = String(`${formaDePagamento.descricao} - chave: ${formaDePagamento.chavePix}`)
          this.formasDePagamentoManual.push(formaDePagamento)
        } else {
          //bug cashback sem bandeira
          let inserirInicio = false;
          if(formaDePagamento.formaDePagamentoPdv && formaDePagamento.bandeira){
            let metodo = formaDePagamento.formaDePagamentoPdv.metodo.replace("_", ' ');

            if(formaDePagamento.formaDePagamentoPdv.metodo !== 'PIX')
              formaDePagamento.descricao  = String(`${formaDePagamento.bandeira.nome} - ${this.capitalizeString(metodo)}`)
            else
              inserirInicio = true;

          }

          if(inserirInicio)
            this.formasDePagamento.splice(1, 0, formaDePagamento);
          else
           this.formasDePagamento.push(formaDePagamento);
        }

      } else if( formaDePagamento.pix ) {
        this.formasDePagamentoPix.push(formaDePagamento);
      } else {
        this.formasDePagamentoOnline.push(formaDePagamento);
        if(formaDePagamento.configMeioDePagamento.meioDePagamento === 'tunapay')
          this.formaPagamentoCarteirasDigitais = formaDePagamento;
      }
    });


    if(this.pagamento)
      if(this.meioOnlineExigeCartao( this.pagamento.formaDePagamento))
        this.exigeCartao = true;


    if(!this.temPagamentoOnline())
      this.tabSelect = 1

    if(this.pagamento.formaDePagamento && this.pagamento.formaDePagamento.id){
      if(this.tipoManualSelecionado())
        this.tabSelect = 2
      else if(!this.pagamento.formaDePagamento.online)
        this.tabSelect = 1
    }
  }

  temPagamentoOnline(){
    return this.formasDePagamentoOnline.length || this.formasDePagamentoPix.length;
  }

  tipoManualSelecionado(){
    if(!this.pagamento.formaDePagamento || !this.pagamento.formaDePagamento.formaDePagamentoPdv)
      return false;

    return  this.pagamento.formaDePagamento.formaDePagamentoPdv.tipo === this.TIPOMANUAL
  }


  capitalizeString(str) {
    // Verifica se a string está vazia
    if (str.length === 0) {
      return str;
    }

    // Converte a primeira letra para maiúscula e o restante para minúscula
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  meioOnlineExigeCartao(forma: FormaDePagamento){
    if(!forma || !forma.configMeioDePagamento || forma.pix)
      return false;

    return forma.online;
  }

  trocarCartao(mensagemAlerta: string = null) {
    this.exigeCartao = true;

    if( !this.pedido.entrega.formaDeEntrega ) {
      return;
    }

    let pagamento = this.pedido.pagamento || this.pagamento;
    let dadosCartao  = ( pagamento ? pagamento.dadosCartao : null)

    const windowRef = TelaCartaoDeCreditoComponent.abraComoPopup(this.dialogService, this.isMobile, this.pedido,
      pagamento, dadosCartao);
    this.telaPagamento = windowRef.content.instance;

    if(mensagemAlerta) this.telaPagamento.mensagemAlerta = mensagemAlerta;

    windowRef.result.subscribe((result: any) => {
      if( result instanceof DialogCloseResult) {

      } else if (result) {
        this.setCartaoEscolhido((result))
      }
    });

    this.app.tick();
  }

  setCartaoEscolhido(result: any){
    this.pagamento.dadosCartao = (result as DadosCartao).outroSemDadosSensitivos();

    Object.keys(this.frm.controls).forEach(key => {
      this.frm.controls[key].updateValueAndValidity();
    });

    // Se for Google Pay ou Apple Pay, emite evento para disparar o pedido automaticamente
    if(result.externalProvider === 'GooglePay' || result.externalProvider === 'ApplePay') {
      setTimeout(() => {
        this.dispareEventoCarteiraDigitalConfigrmada();
      }, 500);
    }
  }

  dispareEventoCarteiraDigitalConfigrmada() {

  }

  alterouTipoPagamento($event = null){
    //this.pedido.pagamento.tipoDePagamento = '' //offline ou online, nanual

    if($event.title === 'Pix (Chave)')
      this.escolheuFormaDePagamentoManual();

  }

  selecioneFormaPagamento( ){
   this.escolheuFormaDePagamentoPresencial( this.pagamento.formaDePagamento);
  }

  escolheuFormaDePagamentoPresencial(formaDePagamento: any) {
    this.exigeCartao = false;
    this.pedido.calculeTotal();
    this.mudouFormaPagamento();

    if (typeof fbq !== 'undefined')   fbq('track', 'AddPaymentInfo');

  }

  escolheuFormaDePagamentoManual( ){
    if(this.tipoManualSelecionado()) return;

    if(this.pagamento.formaDePagamento && this.pagamento.formaDePagamento.tipo)
      this.pagamento.formaDePagamento = null
  }

  escolheuFormaDePagamentoOnline(forma: FormaDePagamento ) {
    this.pedido.calculeTotal();
    if (typeof fbq !== 'undefined')
      fbq('track', 'AddPaymentInfo');

    this.exigeCartao = this.meioOnlineExigeCartao(forma);

    if(this.pagamento.dadosCartao && this.pagamento.dadosCartao.externalProvider !== forma.carteiraDigital)
      delete this.pagamento.dadosCartao

    if(this.exigeCartao && !forma.carteiraDigital) this.trocarCartao();
  }

  alterouFormaDePagamentoParaPix() {
    this.exigeCartao = false;
    this.pagamento.dadosPix = new DadosPix();

    this.alterouFormaDePagamento();

    setTimeout( () => {
      if(this.txtCpfPix) this.txtCpfPix.nativeElement.focus();
      else if(  this.txtEmailPix)
        this.txtEmailPix.nativeElement.focus();
    }, 300);
  }

  alterouFormaDePagamento() {
    if(this.cashback && this.cashback.usar)
      this.pedido.cashback = this.cashback

    this.pedido.calculeTotal();
    delete  this.pedido.cashback;
  }

  estaHabilitado(formaDePagamento: FormaDePagamento, pedido: PedidoLoja) {
    if(!pedido.entrega || !pedido.entrega.foiPreenchida())
      return formaDePagamento.habilitarEntrega && formaDePagamento.habilitarRetirada;


    if(pedido.entrega?.ehDelivery() && formaDePagamento.habilitarEntrega ) return true;

    if(pedido.entrega?.ehRetirada() && formaDePagamento.habilitarRetirada) return true;

    return false;
  }

  mudouFormaPagamento() {
    this.carrinhoService.salvePedido(this.pedido);
  }

  obtenhaTotalPagar() {
    if(!this.cashback || !this.cashback.usar)
      return this.pedido.total;

    let totalPagar = this.pedido.total - this.cashback.valor;

    if(totalPagar <= 0){
      totalPagar = 0;
      delete this.pagamento.formaDePagamento
    }

    return Number(totalPagar.toFixed(2));
  }

  calculeTroco($event: any) {
    setTimeout(() => {
      this.pedido.calculeTroco();
    }, 0);
  }

  pedirCpfNoPix() {
    if(   this.cpfObrigatorio) return false;

   if(! this.pagamento || !this.pagamento.formaDePagamento) return false;

   let config: any =  this.pagamento.formaDePagamento.configMeioDePagamento;

   return config && config.meioDePagamento === 'pagarmehub'

  }


  pagamentoOnlineEstaSelecionado(formaDePagamento: any) {
    if(!this.pedido.pagamento || !this.pedido.pagamento.formaDePagamento) return false;

 //   if(this.pedido.pagamento.carteiraDigital) return false;

    const formaSelecionada: any =  this.pedido.pagamento.formaDePagamento;

    if(formaSelecionada.carteiraDigital) return false;

    return formaSelecionada.id === formaDePagamento.id
  }

}
