import {ApplicationRef, Component, EventEmitter, OnInit, Output} from "@angular/core";
import {DialogService} from "@progress/kendo-angular-dialog";
import {CarrinhoService} from "../../services/carrinho.service";
import {MyDetectorDevice} from "../../app/shared/MyDetectorDevice";
import {FormasPagamentoLojaMobile} from "./formas-pagamento-loja-mobile";
import {ControlContainer, NgForm} from "@angular/forms";
import {ConstantsService} from "../../services/ConstantsService";


@Component({
  selector: 'app-loja-formaspagamento-mobile-nova',
  templateUrl: './formas-pagamento-loja-mobile-nova.component.html',
  styleUrls: ['../loja-pagamento.component.scss'],
  viewProviders: [ { provide: ControlContainer, useExisting: NgForm } ]
})

export class FormasPagamentoLojaMobileNovaComponent extends FormasPagamentoLojaMobile implements OnInit {
  @Output() carteiraDigitalConfirmada = new EventEmitter();

  constructor(protected dialogService: DialogService,    protected detectorDevice: MyDetectorDevice,
              protected carrinhoService: CarrinhoService,   protected app: ApplicationRef,
              protected constantsService: ConstantsService) {
    super(dialogService, detectorDevice, carrinhoService, app, constantsService)
  }

  ngOnInit(): void {
  }

  dispareEventoCarteiraDigitalConfigrmada() {
    this.carteiraDigitalConfirmada.emit();
  }
}
