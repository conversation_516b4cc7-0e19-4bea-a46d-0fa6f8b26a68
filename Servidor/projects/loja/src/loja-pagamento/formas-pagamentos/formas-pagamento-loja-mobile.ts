import {FormaDePagamento} from "../../objeto/FormaDePagamento";
import {EnumMeioDePagamento} from "../../objeto/EnumMeioDePagamento";
import {TelaCartaoDeCreditoComponent} from "../../app/tela-cartao-de-credito/tela-cartao-de-credito.component";
import {DialogCloseResult, DialogService} from "@progress/kendo-angular-dialog";
import {DadosCartao} from "../../objeto/DadosCartao";
import {ApplicationRef, ElementRef, Inject, Injectable, ViewChild} from "@angular/core";
import {NgForm} from "@angular/forms";
import {CarrinhoService} from "../../services/carrinho.service";

import {MyDetectorDevice} from "../../app/shared/MyDetectorDevice";
import {FormasPagamentoLoja} from "./formas-pagamento-loja";
import {ConstantsService} from "../../services/ConstantsService";
import {TabStripComponent} from "@progress/kendo-angular-layout";

export class FormasPagamentoLojaMobile extends FormasPagamentoLoja{
  @ViewChild('txtTrocoM') txtTrocoM: ElementRef;


  constructor(protected dialogService: DialogService,    protected detectorDevice: MyDetectorDevice,
              protected carrinhoService: CarrinhoService,   protected app: ApplicationRef,
              protected constantsService: ConstantsService) {
    super(dialogService, detectorDevice, carrinhoService, app, constantsService)
  }

  setFormasPagamentoMobile(pagamento: any, empresa: any, pedido: any ,   frm: NgForm){


    this.setFormasPagamento(empresa, pedido, pagamento, frm)

    if( this.pedido.pagamento ) {
      this.exigeCartao = this.formaDePagamentoExigeCartao();
      this.habiliteTipoDinheiro();
    }

  }

  habiliteTipoDinheiro() {
    if( this.pagamento.formaDePagamento && this.pagamento.formaDePagamento.nome !== 'dinheiro' ) {
      this.pagamento.trocoPara = 0;
      this.pagamento.temTroco = '';
    }

    setTimeout( () => {
      if( !this.txtTrocoM ) {
        return;
      }
    }, 0);
  }

  alterouFormaDePagamento() {
    this.exigeCartao = this.formaDePagamentoExigeCartao();

    if(this.cashback && this.cashback.usar)
      this.pedido.cashback = this.cashback

    this.pedido.calculeTotal();
    delete  this.pedido.cashback;

    this.habiliteTipoDinheiro();
  }


  trocarCartao(mensagemAlerta: string = null) {
    this.exigeCartao = true;

    if( !this.pedido.entrega.formaDeEntrega)
      return;

    this.pedido.totalPagar = this.obtenhaTotalPagar();

    let pagamento: any = this.pedido.pagamento

    let dadosCartao =   ( pagamento ? pagamento.dadosCartao : null)

    const windowRef = TelaCartaoDeCreditoComponent.abraComoPopup(this.dialogService,
      true, this.pedido, pagamento, dadosCartao);
    this.telaPagamento = windowRef.content.instance;

    if(mensagemAlerta) this.telaPagamento.mensagemAlerta = mensagemAlerta;

    windowRef.result.subscribe((result: any) => {
      if( result instanceof DialogCloseResult) {

      } else if (result) {
        this.setCartaoEscolhido(result)

        this.carrinhoService.salvePedido(this.pedido);
      }
    });
  }

  private formaDePagamentoExigeCartao() {
    if(!this.pagamento) return false;

    return this.meioOnlineExigeCartao(this.pagamento.formaDePagamento)
  }

  precisaDeTroco(temTroco: boolean) {
    if( !temTroco ) {
      this.pagamento.trocoPara = null;
    }
  }
}
