<div class="row ">
  <div class="form-group col-12 cartao-online mb-0"  >
    <div class="mt-1">
     <span class="radio radio-blue w-100" [ngClass]="{'selecionado': pedido.pagamento?.formaDePagamento?.id === formaDePagamento.id &&
        pedido.pagamento?.carteiraDigital === 'GooglePay'}">
      <input id="formaPagamentoGooglePay"
             name="formaDePagamento"
             type="radio"
             [(ngModel)]="pedido.pagamento.formaDePagamento"
             [value]="formaPagamentoGooglePay"
             class="k-radio"
             kendoRadioButton
             (click)="escolheuPagamentoGoogle()" />

          <img src="/images/google-pay-logo.svg" alt="Google Pay"   class="google-pay-logo ml-1">

          <label for="formaPagamentoGooglePay" class="ml-0 d-inline">
            Google Pay
            <span class="text-muted font-11 ml-1 d-inline"
                  *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa">
              (+{{formaDePagamento.taxaCobranca.percentual}}% taxa)
            </span>
          </label>

          <label class="ml-1 d-inline acao-cartao"
                 [hidden]="!pedido.pagamento.formaDePagamento || pedido.pagamento.formaDePagamento.carteiraDigital !== 'GooglePay'">

          </label>
    </span>
    </div>
  </div>

  <div class="form-group col-12 cartao-online mb-0" [hidden]="!applePayDisponivel">
    <div class="mt-1">
     <span class="radio radio-blue w-100" [ngClass]="{'selecionado': pedido.pagamento?.formaDePagamento?.id === formaDePagamento.id &&
        pedido.pagamento?.carteiraDigital === 'ApplePay'}">
     <input id="formaPagamentoApplePay"
            name="formaDePagamento"
            type="radio"
            [(ngModel)]="pedido.pagamento.formaDePagamento"
            [value]="formaPagamentoApplePay"
            class="k-radio"
            kendoRadioButton
            (click)="escolheuPagamentoApplePay()" />
          <i class="fab fa-apple-pay fa-2x ml-2 mr-2"  ></i>
          <label for="formaPagamentoApplePay" class="ml-0 d-inline">
            Apple Pay
            <span class="text-muted font-11 ml-1 d-inline"
                  *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa">
              (+{{formaDePagamento.taxaCobranca.percentual}}% taxa)
            </span>
          </label>

          <div class="ml-1 d-inline acao-cartao" >

          </div>

      </span>
    </div>

  </div>


</div>







