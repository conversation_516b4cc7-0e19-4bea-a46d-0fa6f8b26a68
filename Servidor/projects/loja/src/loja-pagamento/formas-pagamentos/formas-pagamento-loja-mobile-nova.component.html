<kendo-tabstrip #tabFormas class="nav-bordered" id="tabs" name="tabs" (tabSelect)="alterouTipoPagamento($event)">

  <kendo-tabstrip-tab [title]=" formasDePagamentoManual?.length ? 'Pelo site' : 'Pagar pelo site'"
                      [selected]=" (tabSelect  === 0)"   *ngIf="temPagamentoOnline()">
    <ng-template kendoTabContent>
      <div class="row escolher  ">
        <div class="form-group  col-12  mt-1  mb-0 " *ngIf="formasDePagamentoPix.length > 0 && estaHabilitado(formasDePagamentoPix[0], pedido)">
          <span class="mr-3 radio radio-blue mb-1 auto"  *ngFor="let formaDePagamento of formasDePagamentoPix">

            <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"
                   [(ngModel)]="pedido.pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                   (ngModelChange)="alterouFormaDePagamentoParaPix()"
                   [required]="true"/>

             <img src="https://user-images.githubusercontent.com/33992396/99478353-00e4d600-2933-11eb-8228-4bafe8571507.png"
                  style="width: 32px;" class="ml-1"/>

            <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline">
              PIX  <span class="text-muted float-right" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa">
                         <i>+{{formaDePagamento.taxaCobranca.percentual}}% taxa</i></span>
            </label>
          </span>



          <div class="row mt-2" *ngIf="  pedido.pagamento.formaDePagamento?.pix">
            <div class="ml-3 col" >

              <div class="form-group"  style="max-width: 200px" *ngIf="pedirCpfNoPix()">
                <h5  >Informe seu Cpf</h5>
                <div class="form-group">
                  <input type="text" class="form-control" autocomplete="off" required  #txtCpfPix
                         id="cpf" name="cpf" [(ngModel)]="pedido.pagamento.dadosPix.cpf" #cpf="ngModel"
                         mask="000.000.000-00" cpfValido placeholder="___.___.___-__" value="">
                  <div class="invalid-feedback">
                    <p *ngIf="cpf.errors?.required">Obrigatório</p>
                    <p *ngIf="cpf.errors?.cpfInvalido">CPF é invalido</p>
                  </div>
                </div>
              </div>
              <div class="form-group" style="max-width: 350px" >
                <h5  >Informe seu Email</h5>
                <div class="input-group">
                  <input id="emailPix" name="emailPix"  class="form-control" #txtEmailPix  email="true" type="email"
                         #emailPix="ngModel" placeholder="Informe seu email" [required]="true"
                         [(ngModel)]="pedido.pagamento.dadosPix.email"  />
                  <div class="invalid-feedback">
                    <p *ngIf="emailPix.errors?.required">
                      Para pagamentos pix você deve informar seu email. Caso aconteça algum problema, usaremos esse email para te contactar.
                    </p>

                    <p *ngIf="emailPix.errors?.email">Informe um email válido.</p>

                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>

        <div class="form-group  col-12  mb-2 cartao-online"  *ngFor="let formaDePagamento of formasDePagamentoOnline">
          <div class="mt-2">
            <span class="radio radio-blue w-100" [hidden]="!estaHabilitado(formaDePagamento, pedido)"
                  [ngClass]="{'selecionado': pagamentoOnlineEstaSelecionado(formaDePagamento) }">
                        <input id="formaPagamento{{formaDePagamento.id}}" name="formaDePagamento" type="radio"
                               [(ngModel)]="pedido.pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                               (ngModelChange)="escolheuFormaDePagamentoOnline(formaDePagamento)"
                               #rdFormaDePagamento="ngModel"
                               [pagseguro]="{pedido: pedido, exigeCartao: exigeCartao, dadosCartao: pagamento.dadosCartao}"/>

                       <i class="fa fa-credit-card text-blue fa-2x ml-1" aria-hidden="true" style="    top: 5px;position: relative;"></i>

                        <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline">
                            Cartão de Crédito
                          <span class="text-muted font-11 ml-1 d-inline" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa"
                          >(+{{formaDePagamento.taxaCobranca.percentual}}% taxa)</span>

                        </label>


              </span>
            <label class="mt-1 acao-cartao " *ngIf="pagamentoOnlineEstaSelecionado(formaDePagamento)">
              <ng-container *ngIf="rdFormaDePagamento.errors">
                <div class="invalid-feedback" *ngIf="rdFormaDePagamento?.errors?.endereco_obrigatorio">
                  <p>Informe o endereço de cobrança da fatura do cartão de crédito</p>
                </div>
              </ng-container>
              <div class="invalid-feedback" *ngIf="rdFormaDePagamento?.errors?.dados_cartao_obrigatorio">
                <p>Informe os dados do cartão de cŕedito</p>
              </div>

              <div class="text-right" >
                <ng-container *ngIf="pagamento.dadosCartao">
                  <div class="">
                    <i class="fas fa-credit-card font-16"></i>
                    <span class="" *ngIf="!pagamento.dadosCartao.descricaoPagamento">
                          ****-{{pedido.pagamento.dadosCartao.ultimosNumeros}}
                        </span>
                    <span class="ml-2" *ngIf="pagamento.dadosCartao.descricaoPagamento">
                          {{pagamento.dadosCartao.descricaoPagamento}}
                        </span>
                    <button type="button" class="btn btn-blue ml-2" (click)="trocarCartao()">
                      <i class="far fa-credit-card mr-1 fa-lg"></i>
                      Alterar Cartão
                    </button>
                  </div>
                </ng-container>
                <ng-container *ngIf="!pagamento.dadosCartao  ">
                  <button class="btn btn-blue" type="button" (click)="trocarCartao()">
                    <i class="far fa-credit-card mr-1 fa-lg"></i>
                    Adicionar Cartão
                  </button>
                </ng-container>
              </div>
            </label>
          </div>

        </div>

        <div class="col-12"  *ngIf="formaPagamentoCarteirasDigitais">
          <app-tunnapay-digitais [formaDePagamento]="formaPagamentoCarteirasDigitais" [pedido]="pedido"
                                 (definiuCartao)="setCartaoEscolhido($event)" (escolheuFormaPagamento)="escolheuFormaDePagamentoOnline($event)">

          </app-tunnapay-digitais>
        </div>

        <div class="invalid-feedback ml-2" *ngIf="!pedido.pagamento.formaDePagamento">
          <p>Escolha a forma de pagamengo do seu pedido</p>
        </div>
      </div>

    </ng-template>

  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab [title]="formasDePagamentoManual?.length ? 'Na entrega' : 'Pagar na entrega'"
                      [selected]="tabSelect  === 1"  *ngIf="formasDePagamento.length">
    <ng-template kendoTabContent>

      <div class="form-group mb-2 mt-2 escolher formas">
        <ng-container *ngFor="let formaDePagamento of formasDePagamento; let i = index;">
          <div [hidden]="!estaHabilitado(formaDePagamento, pedido)" class="mr-3 radio radio-blue mb-1" >
            <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"
                   [(ngModel)]="pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                   (ngModelChange)="alterouFormaDePagamento()"
                   [required]="true"/>
            <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline">

              <app-loja-bandeira-logo [bandeira]=" formaDePagamento.bandeira" [descricao]="formaDePagamento.descricao"  >

              </app-loja-bandeira-logo>

              <span class="text-muted float-right" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa">
           <i>+{{formaDePagamento.taxaCobranca.percentual}}% taxa</i></span>

              <span class="text-muted float-right" *ngIf="formaDePagamento.possuiDesconto && formaDePagamento.desconto > 0">
           <i>-{{formaDePagamento.desconto}}% de desconto</i></span>
            </label>

            <ng-container *ngIf="i===0">
              <app-troco-loja [obtenhaTotalPagar]="this.obtenhaTotalPagar" [pagamento]="pagamento"
                              [pedido]="pedido"></app-troco-loja>
            </ng-container>
          </div>
        </ng-container>

      </div>

      <div class="invalid-feedback ml-2" *ngIf="!pedido.pagamento.formaDePagamento">
        <p>Escolha a forma de pagamengo do seu pedido</p>
      </div>
    </ng-template>

  </kendo-tabstrip-tab>

  <kendo-tabstrip-tab [title]="'Pix (Chave)'"  *ngIf="formasDePagamentoManual.length " [selected]="tabSelect===2">
    <ng-template kendoTabContent>
      <div class="form-group mb-2 mt-2 escolher formas">
        <ng-container *ngFor="let formaDePagamento of formasDePagamentoManual">
          <div [hidden]="!estaHabilitado(formaDePagamento, pedido)" class="mr-3 radio radio-blue mb-1" >
            <input [id]="'formaPagamento' + formaDePagamento.id" name="formaDePagamento" type="radio"
                   [(ngModel)]="pagamento.formaDePagamento" [value]="formaDePagamento" class="k-radio" kendoRadioButton
                   (ngModelChange)="alterouFormaDePagamento()"
                   [required]="true"/>
            <label for="formaPagamento{{formaDePagamento.id}}" class="ml-1 d-inline">

              <app-loja-bandeira-logo [bandeira]=" formaDePagamento.bandeira" [descricao]="formaDePagamento.descricao"  >

              </app-loja-bandeira-logo>

              <span class="text-muted float-right" *ngIf="formaDePagamento.taxaCobranca && formaDePagamento.taxaCobranca.ativa">
             <i>+{{formaDePagamento.taxaCobranca.percentual}}% taxa</i></span>

              <span class="text-muted float-right" *ngIf="formaDePagamento.possuiDesconto && formaDePagamento.desconto > 0">
             <i>-{{formaDePagamento.desconto}}% de desconto</i></span>
            </label>

            <div class="ml-2 font-12 mt-1" *ngIf="formaDePagamento.chavePix">
              Chave para transferência do PIX: <b>{{formaDePagamento.chavePix}}</b>

              <div class="alert alert-info mt-2">
                Após realizar o pedido, faça o pagamento envie o comprovante  para o lojista
              </div>
            </div>
          </div>


        </ng-container>
      </div>
    </ng-template>
  </kendo-tabstrip-tab>
</kendo-tabstrip>





