import {ApplicationRef, Component, EventEmitter, Input, OnInit, Output, Renderer2} from "@angular/core";
import {Control<PERSON>ontainer, NgForm} from "@angular/forms";
import {TunapayService} from "../../services/tunapay.service";
import {DadosCartao} from "../../objeto/DadosCartao";
import {FormaDePagamento} from "../../objeto/FormaDePagamento";
import {TunapayScript} from "./tunapay.script";



@Component({
  selector: 'app-tunnapay-digitais',
  templateUrl: './tunapay-carteirasdigitais.component.html',
  styleUrls: ['./tunapay-carteirasdigitais.component.scss'],
  viewProviders: [ { provide: ControlContainer, useExisting: NgForm } ]
})

export class TunapayCarteirasDigitaisComponent extends TunapayScript implements   OnInit {
  @Input() formaDePagamento: any = {};
  @Input() pedido: any = {};
  @Output() definiuCartao = new EventEmitter();
  @Output() escolheuFormaPagamento = new EventEmitter();

  processando = false;

  msgErro: string;

  googlePayDisponivel = false;
  applePayDisponivel = false;
  formaPagamentoGooglePay: any;
  formaPagamentoApplePay: any;

  constructor(protected tunapayService: TunapayService, protected app: ApplicationRef,
              protected renderer: Renderer2) {
    super(tunapayService, app, renderer)
    this.formaPagamentoGooglePay = new FormaDePagamento()
    this.formaPagamentoApplePay = new FormaDePagamento();
  }

  ngOnInit(): void {

    Object.assign(this.formaPagamentoGooglePay, this.formaDePagamento)
    this.formaPagamentoGooglePay.carteiraDigital = 'GooglePay';

    Object.assign(this.formaPagamentoApplePay, this.formaDePagamento)
    this.formaPagamentoApplePay.carteiraDigital = 'ApplePay';

    this.inicializeTuna(this.pedido.guid).then((err) => {
       if(!err){
         // Configura Google Pay após inicializar Tuna
         this.configureGooglePay();
         this.configureApplePay();
       }
    });
  }



  setDadosGooglPay(payment: any){
    //PAYMENT_GATEWAY     response.paymentMethodData.tokenizationData.type
    this.cartao.token =   payment.tokenizationData.token;
    this.cartao.bandeira = payment.info.cardNetwork;
    this.cartao.descricaoPagamento = `Google Pay (${payment.description})` ; //response.info.cardDetails so numeros

    if(payment.info){
      this.cartao.ultimosNumeros = payment.info.cardDetails;
      this.cartao.bandeira = payment.info.cardNetwork;
    }

    this.cartao.externalProvider = 'GooglePay';
    this.definiuCartao.emit(this.cartao)

  }

  private configureApplePay() {

    const applePaySettings = {
      buttom: {
        selector: '#applePayDiv',
        style: {
          buttonStyle: "black",
          type: "check-out",
          locale: "pt-BR",
          width: "10px",
          height: "10px",
          borderRadius: "10px",
          padding: "10px",
          boxSizing: "10px"
        }
      },
      applePayCallback: async (applePayPayload: any) => {
        try {
          console.log('retorno applePayPayload')
          console.log(applePayPayload)
          this.cartao.token = applePayPayload.details.token;
        //  this.cartao.bandeira = 'Apple Pay';
          this.cartao.descricaoPagamento = `Apple Pay`;
          this.cartao.externalProvider = 'ApplePay';

          this.definiuCartao.emit(this.cartao);
          return true;
        } catch (error) {
          console.error('Erro no processamento Apple Pay:', error);
          return false;
        }
      },
      cart: {
        lineItems: [{
          label: "Pedido",
          amount: (this.pedido.obtenhaValorAhPagar()).toFixed(2)
        }],
        total: {
          label: "Total do Carrinho",
          amount: {value: (this.pedido.obtenhaValorAhPagar()).toFixed(2), currency: "BRL"}
        }
      },
      merchantId: 'merchant.uy.tunahmlg',
      displayName: 'fibo.meucardapio',
    };

    console.log(applePaySettings)

    this.tunaInstance.useApplePay(applePaySettings)
      .then((flag, err) => {
        console.log('Apple Pay  run: ' + flag);
        this.applePayDisponivel = flag;
        this.app.tick();
      })
      .catch((err: any) => {
        console.error('Erro detalhado ao configurar Apple Pay:', err);
        this.applePayDisponivel = false;
        this.app.tick();
      });
  }

  private configureGooglePay() {

    const merchantInfo = {
      merchantId: 'BCR2DN6TR7QYLIKK'
    };

    const transactionInfo = {
      transactionId: this.pedido.guid,
      totalPriceStatus: 'FINAL',
      totalPrice: this.pedido.obtenhaValorAhPagar().toString(),
      currencyCode: "BRL",
      countryCode: "BR"
    };

    // Callback quando o pagamento for concluído
    const checkoutCallback = async (response: any) => {
      const payment: any = response.paymentMethodData;

      if (payment) {
        this.setDadosGooglPay(payment)
      } else {
        console.error("Erro ao processar o pagamento com Google Pay:", response);
      }
    };


    // Configura o Google Pay usando a API do Tuna
    this.tunaInstance.useGooglePay(
      '#google-pay-container', // ID do container do botão
      merchantInfo,
      checkoutCallback,
      null,
      transactionInfo
    )

    this.googlePayDisponivel = true;

  }

  escolheuPagamentoApplePay() {
    if (this.applePayDisponivel) {
      this.pedido.calculeTotal();
      this.escolheuFormaPagamento.emit(this.formaPagamentoApplePay);
    }
  }

  escolheuPagamentoGoogle() {
    this.pedido.calculeTotal();

    if(this.googlePayDisponivel){
      this.escolheuFormaPagamento.emit(this.formaPagamentoGooglePay)
    }
  }
}
