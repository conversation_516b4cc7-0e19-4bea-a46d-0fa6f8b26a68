.mobile {
  .btn-block {
    padding: 1em 0;
  }
}

.form-control {
  border-radius: 2px;
}

.box-resgate{
  border: 1px solid #ebebeb;
  padding: 10px 10px;
  border-bottom: 2px solid #eaedef;
}

.footer {
  border-top: solid 1px #eeeeee;
  background: #fff;
  padding: 0px;
  position: fixed;
}

.radio.mb-1 {
  margin-bottom: 0.5rem !important
}

.preco {
  color: #6db31b;
}

.negativo {
  color: #c40e34;
}

.resgate-preco{
  color: #F67682;
}


.linha{
  border-top: 1px solid #ececec;
  border-bottom: 1px solid #fff;
}

.form-group .radio label{
  top: 3px;
  position: relative;
}

@media (min-width: 1025px) {
  .footer {
    position: initial !important;
    margin-top: 30px;
  }
}

.escolher{
   span{
     min-width: 75px;
     display: inline-block;
   }
}


::ng-deep .k-radio:checked, .k-checkbox:checked {
  border-color: #7e57c2;
  color: #ffffff;
  background-color: #7e57c2;
}

::ng-deep  .k-radio:checked:focus, .k-checkbox:checked:focus {
  box-shadow: 0 0 0 2px rgba(126, 87, 194, 0.3);
}

::ng-deep .k-radio {
  width: 22px;
  height: 22px;
}

.k-checkbox::before {
  width: 18px;
  height: 18px;
  font-size: 18px;
}

::ng-deep .radio label {
  margin-left: 10px;
  position: relative;
  top: 1px;
  display: inline-block;
}

.borda-pontilhada{
  border-bottom: 2px dashed #ccc;
}

.k-checkbox:disabled{
  border-color: rgba(0, 0, 0, 0.1) !important;
}

h5{
  font-size: 1rem;
}

.logo-plano{
  max-height: 35px;
  top: 5px;
  position: relative;

  &.gendai{
    max-width: 60px;
    top: 14px;
  }
}

.formas{
  .radio{
    margin-bottom: 0.5rem !important;
    padding: 10px 20px;
    border: 1px solid #ccc;
    border-radius: 6px;
  }
}


.chinainbox{
  .btn-success, .btn-primary {
    background-color: #e52a28de !important;
    border-color: #e52a28 !important;;
  }

  .btn-secondary {
    background-color: #ff9800d6  !important;
    border-color: #e79e406e  !important;
  }
}


@media (max-width: 768px) {
  .escolher.formas{
    .mr-3.radio{
       margin-right: 0px !important;
    }
  }
}


::ng-deep #google-pay-container{
  display: flex;
  justify-content: center;
  width: 100% !important;

  div {
    width: 100% !important;
  }

  apple-pay-button {
    width: 100% !important;
  }

  button {
    width: 100% !important;
    max-width: 100%;
  }
}

::ng-deep #applePayDiv {
  display: flex;
  justify-content: center;
  width: 100% !important;
  padding: 10px !important;
  padding-bottom: 20px !important;
  padding-left: 0px !important;

  div {

    width: 100% !important;
  }

  apple-pay-button {
    width: 100% !important;
  }

  .apple-pay-btn {
    width: 50% !important;
    height: 100px !important;
  }
}


@media (max-width: 900px) {
  .footer {
    padding-bottom: calc(16px + env(safe-area-inset-bottom) + 10px);
  }
}
