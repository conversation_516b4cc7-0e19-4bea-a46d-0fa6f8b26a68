import {Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {PedidoLoja} from "../objeto/PedidoLoja";
import {ClienteService} from "../services/cliente.service";
import {CarrinhoService} from "../services/carrinho.service";
import {ActivatedRoute, Router} from "@angular/router";
import {CurrencyPipe, Location} from "@angular/common";
import { formatCurrency } from '@angular/common';
import {Pagamento} from "../objeto/Pagamento";
import {NgForm} from "@angular/forms";
import {ITela} from "../objeto/ITela";
import {DominiosService} from "../services/dominios.service";
import {ConstantsService} from "../services/ConstantsService";
import {AutorizacaoLojaService} from "../services/autorizacao-loja.service";
import {DialogCloseResult, DialogService} from "@progress/kendo-angular-dialog";
import {  Inject,  LOCALE_ID } from '@angular/core';
import {FormasPagamentoLojaMobileComponent} from "./formas-pagamentos/formas-pagamento-loja-mobile.component";
import {FormasPagamentoLojaMobileNovaComponent} from "./formas-pagamentos/formas-pagamento-loja-mobile-nova.component";

declare var fbq;

@Component({
  selector: 'app-loja-pagamento',
  templateUrl: './loja-pagamento.component.html',
  styleUrls: ['./loja-pagamento.component.scss']
})
export class LojaPagamentoComponent implements OnInit, ITela {
  @ViewChild('frm')  frm: NgForm;

  @ViewChild('formasPagamentoAntigas', {static: false}) formasPagamentoAntigas: FormasPagamentoLojaMobileComponent;
  @ViewChild('formasPagamentoNova', {static: false}) formasPagamentoNova: FormasPagamentoLojaMobileNovaComponent;

  pedido: PedidoLoja;
  public formatOptions: any = {
    style: 'currency',
    currency: 'BRL'
  };
  pagamento: Pagamento;
  selecionou: boolean;
  nomePagina: string;
  msgErro: string;
  empresa: any = {};

  usarSaldo: any = false;
  cashback: any;
  resgate: any;
  carregando = false;
  abrirAutenticacaoCartao: boolean;
  mensagemAlerta: string;
  constructor(@Inject(LOCALE_ID) public locale: string,
              private clienteService: ClienteService, private carrinhoService: CarrinhoService,
              private route: ActivatedRoute,
              private router: Router, private location: Location, private dominiosService: DominiosService,
              private autorizacao: AutorizacaoLojaService, private dialogService: DialogService,
              private constantsService: ConstantsService) {
    this.nomePagina = dominiosService.obtenhaRaizCardapio();
    this.pagamento = new Pagamento();
  }


  ngOnInit() {
    this.pedido = this.carrinhoService.obtenhaPedido();
    if( this.pedido.pagamento )
      this.pagamento = this.pedido.pagamento;

    if(this.route.snapshot.queryParams.authct)
       this.abrirAutenticacaoCartao = true;

    if(this.route.snapshot.queryParams.msg)
      this.mensagemAlerta = this.route.snapshot.queryParams.msg;


    this.constantsService.empresa$.subscribe( (empresa) => {
      if(empresa) {
        this.empresa = empresa;

        setTimeout(() => { this.setFormasPagamentoExibir() }, 100)

        delete this.cashback;
        delete this.pedido.cashback;

        if(this.empresa.integracaoPedidoFidelidade || this.empresa.integracaoFidelidade ){
          this.cashback = { saldo: 0, podeUsarNaLoja: this.empresa.integracaoFidelidade != null}
          this.carregando = true;
          this.clienteService.obtenhaSaldoResgate().then( (dados: any) => {
            this.carregando = false;
            if(!dados.resgate){
              this.cashback = dados;
            } else {
              this.resgate = dados;
              this.resgate.usar = true
            }

            if( this.cashback){
              let valorPedido = this.pedido.obtenhaTotalSemTaxaEntrega();

              this.setPodeUsarCashback(valorPedido)
              this.setCashbackDasFormas();
            }
          }).catch((err) => {
            this.carregando = false;
            this.cashback.erro = err.message || err;
          })
        }
      }
    });
  }

  setPodeUsarCashback(valorPedidoPagar: number){
    if(!this.cashback) return;

    this.cashback.podeUsarNoPedido = valorPedidoPagar >= this.cashback.minimoPedido;
    this.cashback.valor = this.cashback.saldo >  valorPedidoPagar ? valorPedidoPagar : this.cashback.saldo;
    if(this.cashback.usarPadrao && this.cashback.saldo  > 0)
      this.cashback.usar =   this.cashback.podeUsar &&   this.cashback.podeUsarNoPedido;

    if( this.cashback.usar){
      this.pedido.cashback = this.cashback
      //forçar recalcular valor pedido
      if(this.pagamento && this.pagamento.formaDePagamento)
        this.pedido.calculeTotal();
    }  else{
      delete this.pedido.cashback
    }
  }

  setFormasPagamentoExibir(){

    let ctrlPagamento: any =  this.formasPagamentoNova ? this.formasPagamentoNova : this.formasPagamentoAntigas;

    if(ctrlPagamento){
      ctrlPagamento.setFormasPagamentoMobile(this.pagamento, this.empresa, this.pedido, this.frm )
     if(this.abrirAutenticacaoCartao){
       setTimeout(() => {
         ctrlPagamento.trocarCartao(this.mensagemAlerta);
       }, 1200)
     }
    }
  }

  setCashbackDasFormas(){
    if(this.formasPagamentoAntigas)
      this.formasPagamentoAntigas.cashback = this.cashback;

    if(this.formasPagamentoNova)
      this.formasPagamentoNova.cashback = this.cashback;

  }

  onSubmit() {
    delete this.msgErro;
    if( !this.frm.valid )
      return;

    if(!this.pagamento || !this.pagamento.formaDePagamento){
      this.msgErro = 'Escolha uma forma de pagamento'
      return;
    }

    if(this.pedido.totalResgatado){
      if(!this.resgate.usar){
        this.msgErro = "Marque opção 'usar meus " + this.pedido.acumulo + "'";
        return;
      }

      if(this.resgate.saldo < this.pedido.totalResgatado){
        this.msgErro = 'Seu saldo é insuficiente para o resgate';
        return;
      }

      this.resgate.valor = this.pedido.totalResgatado;
      this.pedido.resgate = this.resgate;
    }


    if( this.pedido.troco < 0 ){
      this.msgErro = 'Valor para troco tem que ser maior do que o pedido';
      return;
    }

    if( this.pedido.troco > 100 ){
      this.msgErro = 'O valor informado para o troco é inválido. Por favor, revise o valor e tente novamente.';
      return;
    }

    this.pedido.pagamento = this.pagamento;

    if (typeof fbq !== 'undefined')
      fbq('track', 'AddPaymentInfo');

    if(this.cashback && this.cashback.usar){
      this.pedido.cashback = this.cashback;
    } else {
      delete this.pedido.cashback
    }

    this.pedido.calculeTotal();

    this.carrinhoService.salvePedido(this.pedido);
    this.dominiosService.vaParaCarrinho();
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return false;
  }


  usarSaldoCartao() {  }

  onSelectedChange(  selected) {
    this.usarSaldo = selected
  }


  obtenhaTotalPagar() {
    if(!this.cashback || !this.cashback.usar)
      return this.pedido.total;

    let totalPagar = this.pedido.total - this.cashback.valor;

    if(totalPagar <= 0){
      totalPagar = 0;
      delete this.pagamento.formaDePagamento
    }

    return Number(totalPagar.toFixed(2));
  }


  deveTerBordas() {
    return true;
  }


  deveExibirBannerTema() {
    return false;
  }

  obtenhaSaldo() {
    let descricao = formatCurrency(this.cashback.saldo, this.locale, "R$")

    if(this.empresa.integracaoFidelidade && this.empresa.integracaoFidelidade.sistema === 'gcom')
      descricao = descricao.replace('R$', this.empresa.idRede === 2 ? "R$G" : "R$C")


    return descricao;
  }


  cashbackDesabilitado() {
    let desabilitado = !this.cashback.podeUsar || !this.cashback.podeUsarNoPedido || this.pedido.teveAutenticacaoPagamento();

    if(desabilitado) return true;

    //if(this.pedido.contato.id)
      //return !this.cashback.saldo

    return false;

  }

  irParaAtualizarCadastro(){
    this.pedido.contato.atualizarCadastro = true;
    this.dominiosService.vaParaCadastro(window.location.pathname, { contato: this.pedido.contato});
    return false;
  }

  irParaLogin(){
    this.dominiosService.vaParaLogin(window.location.pathname, this.pedido.contato.telefone)
    return false;
  }

  irParaVerSaldo(){
    this.dominiosService.vaParaValidarLogin(window.location.pathname, this.pedido.contato)
    return false;
  }

  irparaOptin(){
    this.pedido.contato.fazerOptin = true;
    this.dominiosService.vaParaCadastro(window.location.pathname, { contato: this.pedido.contato});
    return false;
  }

  marcouUsarCashback(){

    if( !this.cashback.saldo  ){
      let usuario = this.autorizacao.getUsuario();

      if(!usuario){
        if(this.pedido.contato.id)
          this.irParaLogin();
        else
          this.dominiosService.vaParaCadastro(window.location.pathname, { contato: this.pedido.contato});
      } else {
        if(this.cashback.fazerOptin){
          this.irparaOptin();
        } else {
          if(!usuario.cpf  )
             this.irParaAtualizarCadastro();

        }
      }
    }

    setTimeout(() => {
      this.setFormasPagamentoExibir();
      this.setCashbackDasFormas();
    }, 0)
  }

  isGooglePaySelecionado(): boolean {
    return this.pedido?.pagamento?.formaDePagamento?.carteiraDigital === 'GooglePay';
  }

  isApplePaySelecionado(): boolean {
    return this.pedido?.pagamento?.formaDePagamento?.carteiraDigital === 'ApplePay';
  }

  isCarteiraDigitalSelecionada(): boolean {
    return this.isGooglePaySelecionado() || this.isApplePaySelecionado();
  }

  onCarteiraDigitalConfirmada() {
    // Submete o formulário automaticamente quando Google Pay ou Apple Pay for confirmado
    if(this.frm.valid && !this.carregando) {
      this.onSubmit();
    }
  }

}
