<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout com Sidebar e Google Pay</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            overflow-x: hidden;
        }

        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background-color: #1a1a1a;
            color: white;
            overflow-y: auto;
            transition: width 0.3s ease;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4285f4, #ea4335, #fbbc04, #34a853);
            border-radius: 50%;
        }

        .nav-menu {
            list-style: none;
            padding: 1rem 0;
        }

        .nav-item {
            padding: 0;
            margin: 0;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            gap: 15px;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border-left: 4px solid #4285f4;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background-color: #f5f5f5;
            transition: margin-left 0.3s ease;
        }

        .content-header {
            background-color: white;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .content-body {
            padding: 2rem;
        }

        .card {
            background-color: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .google-pay-button {
            display: inline-flex;
            align-items: center;
            background-color: #000;
            color: white;
            padding: 12px 24px;
            border-radius: 24px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            gap: 8px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .google-pay-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .gpay-logo {
            height: 20px;
        }

        .menu-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background-color: #1a1a1a;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                transform: translateX(-100%);
            }

            .sidebar.active {
                width: 280px;
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: block;
            }

            .overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0,0,0,0.5);
                z-index: 999;
            }

            .overlay.active {
                display: block;
            }
        }

        .payment-methods {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .payment-card {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            gap: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-card:hover {
            border-color: #4285f4;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .card-icon {
            width: 40px;
            height: 25px;
            background-color: #ff5252;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }

        .card-icon::before {
            content: "";
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: rgba(255,255,255,0.3);
            border-radius: 50%;
            top: 50%;
            left: -5px;
            transform: translateY(-50%);
        }

        .card-icon::after {
            content: "";
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: rgba(255,255,255,0.3);
            border-radius: 50%;
            top: 50%;
            right: -5px;
            transform: translateY(-50%);
        }

        .card-number {
            font-family: monospace;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <button class="menu-toggle" onclick="toggleSidebar()">☰</button>
    
    <div class="overlay" onclick="toggleSidebar()"></div>
    
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <div class="logo-icon"></div>
                <span>PromoKit</span>
            </div>
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="#" class="nav-link active">
                    <span class="nav-icon">🏠</span>
                    <span>Dashboard</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <span class="nav-icon">💳</span>
                    <span>Pagamentos</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <span class="nav-icon">📊</span>
                    <span>Relatórios</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <span class="nav-icon">👥</span>
                    <span>Clientes</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <span class="nav-icon">⚙️</span>
                    <span>Configurações</span>
                </a>
            </li>
        </ul>
    </aside>
    
    <main class="main-content">
        <header class="content-header">
            <h1>Métodos de Pagamento</h1>
        </header>
        
        <div class="content-body">
            <div class="card">
                <h2>Adicionar Método de Pagamento</h2>
                
                <div class="payment-methods">
                    <button class="google-pay-button">
                        <svg class="gpay-logo" viewBox="0 0 80 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g>
                                <path d="M36.9 19.9V29.5H33.7V10.5H40.3C41.9 10.5 43.3 11.1 44.3 12.1C45.3 13.1 45.9 14.5 45.9 16.1C45.9 17.7 45.3 19.1 44.3 20.1C43.3 21.1 41.9 21.7 40.3 21.7H36.9V19.9ZM36.9 13.3V17.1H40.4C41.2 17.1 41.8 16.9 42.3 16.4C42.8 15.9 43 15.3 43 14.5C43 13.7 42.8 13.1 42.3 12.6C41.8 12.1 41.2 11.9 40.4 11.9H36.9V13.3Z" fill="white"/>
                                <path d="M51.8 20.1C52.6 20.1 53.3 20.3 53.8 20.8C54.3 21.3 54.6 21.9 54.6 22.7V29.5H51.6V28.3C51.1 29.1 50.3 29.5 49.1 29.5C48.3 29.5 47.6 29.3 47 29C46.4 28.7 46 28.3 45.7 27.8C45.4 27.3 45.3 26.8 45.3 26.2C45.3 25.3 45.6 24.6 46.3 24.1C47 23.6 48 23.3 49.4 23.3H51.4V23.1C51.4 22.5 51.2 22.1 50.9 21.8C50.6 21.5 50.1 21.4 49.5 21.4C49 21.4 48.6 21.5 48.2 21.6C47.8 21.7 47.4 21.9 47.1 22.2L45.9 20.6C46.4 20.2 47 19.9 47.7 19.7C48.4 19.5 49.1 19.4 49.9 19.4L51.8 20.1ZM49.8 27.9C50.3 27.9 50.7 27.8 51.1 27.6C51.5 27.4 51.7 27.1 51.9 26.7V24.8H50C48.8 24.8 48.2 25.2 48.2 26.1C48.2 26.5 48.3 26.8 48.6 27C48.9 27.2 49.3 27.3 49.8 27.3V27.9Z" fill="white"/>
                                <path d="M60.8 29.5L57.1 19.9H60.2L62.5 26.6L64.9 19.9H67.9L63.4 31.5C63 32.5 62.5 33.2 61.9 33.6C61.3 34 60.5 34.2 59.6 34.2C59.1 34.2 58.6 34.1 58.1 34C57.6 33.9 57.2 33.7 56.9 33.5L57.7 31.7C57.9 31.8 58.1 31.9 58.4 32C58.7 32.1 58.9 32.1 59.2 32.1C59.5 32.1 59.8 32 60 31.9C60.2 31.8 60.4 31.5 60.5 31.2L60.6 31L60.8 29.5Z" fill="white"/>
                            </g>
                            <g>
                                <path d="M25.1 21.8V17.3C25.1 17.2 25.1 17.2 25.1 17.1C25.1 13 23.4 9.3 20.5 6.8L17.9 9.4C19.8 11.1 21 13.5 21 16.2V21.8C21 23.5 19.6 24.9 17.9 24.9C16.2 24.9 14.8 23.5 14.8 21.8V10.5H10.7V21.8C10.7 25.8 14 29.1 18 29.1C22 29.1 25.3 25.8 25.3 21.8H25.1Z" fill="#4285F4"/>
                                <path d="M10.7 18.7C9 18.7 7.6 17.3 7.6 15.6C7.6 13.9 9 12.5 10.7 12.5V8.4C6.7 8.4 3.5 11.6 3.5 15.6C3.5 19.6 6.7 22.8 10.7 22.8V18.7Z" fill="#34A853"/>
                                <path d="M18.1 6.5V10.6C19.8 10.6 21.2 12 21.2 13.7H25.3C25.3 9.7 22.1 6.5 18.1 6.5Z" fill="#FBBC04"/>
                                <path d="M10.7 18.7V22.8C12.7 22.8 14.5 21.9 15.7 20.4L12.6 17.8C12.1 18.4 11.4 18.7 10.7 18.7Z" fill="#EA4335"/>
                            </g>
                        </svg>
                        Google Pay
                    </button>
                    
                    <div class="payment-card">
                        <div class="card-icon"></div>
                        <span class="card-number">•••• 1990</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>Histórico de Transações</h2>
                <p>Suas transações recentes aparecerão aqui.</p>
            </div>
        </div>
    </main>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.querySelector('.overlay');
            
            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
        }

        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                if (window.innerWidth <= 768) {
                    toggleSidebar();
                }
            });
        });

        document.querySelector('.google-pay-button').addEventListener('click', function() {
            alert('Integração com Google Pay seria iniciada aqui');
        });

        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                document.getElementById('sidebar').classList.remove('active');
                document.querySelector('.overlay').classList.remove('active');
            }
        });
    </script>
</body>
</html>