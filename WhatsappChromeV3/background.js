// Proxy para requisições API do Promokit

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'PROMOKIT_API_REQUEST') {
    const { url, method = 'GET', headers = {}, body } = request;

    fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: body ? JSON.stringify(body) : undefined,
      credentials: 'include'
    })
    .then(response => {
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return response.json().then(data => ({
          ok: response.ok,
          status: response.status,
          data
        }));
      } else {
        return response.text().then(text => ({
          ok: response.ok,
          status: response.status,
          data: text
        }));
      }
    })
    .then(result => sendResponse({ success: true, ...result }))
    .catch(error => sendResponse({ success: false, error: error.message }));

    return true; // Indica que a resposta será enviada de forma assíncrona
  }
});

// Listener para abrir nova aba se necessário
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.type === 'OPEN_TAB') {
    chrome.tabs.create({ url: request.url });
  }
});