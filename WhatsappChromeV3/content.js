var tabId = null;
var urlServidor = null;
var LARGURA = 325;

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function insiraIFrame() {
    var extensionOrigin = chrome.runtime.getURL("frame.html");

    var iframe = document.createElement('iframe');
    // Must be declared at web_accessible_resources in manifest.json
    iframe.src = extensionOrigin;

    iframe.id = "frame_promokit";
    iframe.style.cssText = 'position:fixed;top:0;right:0;display:block;' +
        'width:' + LARGURA + 'px;height: 100%;z-index:1000;';
    document.body.appendChild(iframe);

    return document.getElementById('frame_promokit');
}

async function injectScript(file, node) {
  var th = document.getElementsByTagName(node)[0];
  var s = document.createElement('script');
  s.setAttribute('type', 'text/javascript');
  s.setAttribute('src', chrome.runtime.getURL(file));
  th.appendChild(s);
}


function insiraCSS(urlCSS) {
    return new Promise( (resolve, reject) => {
        var link = document.createElement('link');
        link.rel  = 'stylesheet';
        link.type = 'text/css';
        link.href = urlCSS;
        link.media = 'all';
        (document.head||document.documentElement).appendChild(link);

        resolve(true);
    });
}

async function injetarScript() {
    var iFrame = await insiraIFrame();

    iFrame.addEventListener("load", function() {
    });
    
    // Injetar o script injected-api.js
    await injectScript('injected-api.js', 'body');
}

injetarScript();

// Listener para mensagens da página (requisições API)
window.addEventListener('message', async (event) => {
    if (event.data && event.data.type === 'PROMOKIT_API_REQUEST') {
        const { id, url, method, headers, body } = event.data;
        
        // Enviar para o background script
        chrome.runtime.sendMessage({
            type: 'PROMOKIT_API_REQUEST',
            url,
            method,
            headers,
            body
        }, response => {
            // Enviar resposta de volta para a página
            window.postMessage({
                type: 'PROMOKIT_API_RESPONSE',
                id: id,
                response: response
            }, '*');
        });
    }
});

window.onmessage = async (e) => {
    if( e.data.tipo === 'URL_SERVIDOR' ) {
        var urlServidor = new URL(e.data.url).origin;

        debugger;
        await injectScript('wapi.js', 'body');
    }
};