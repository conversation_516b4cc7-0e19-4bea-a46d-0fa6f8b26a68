// API injetada na página para comunicação com a extensão
(function() {
  let requestId = 0;
  const pendingRequests = new Map();

  // Listener para respostas do content script
  window.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'PROMOKIT_API_RESPONSE') {
      const { id, response } = event.data;
      const resolver = pendingRequests.get(id);
      if (resolver) {
        pendingRequests.delete(id);
        if (response.success) {
          resolver.resolve(response);
        } else {
          resolver.reject(new Error(response.error));
        }
      }
    }
  });

  // API pública
  window.promokitAPI = {
    request: function(url, options = {}) {
      return new Promise((resolve, reject) => {
        const id = ++requestId;
        pendingRequests.set(id, { resolve, reject });
        
        window.postMessage({
          type: 'PROMOKIT_API_REQUEST',
          id: id,
          url: url,
          method: options.method || 'GET',
          headers: options.headers || {},
          body: options.body
        }, '*');
        
        // Timeout após 30 segundos
        setTimeout(() => {
          if (pendingRequests.has(id)) {
            pendingRequests.delete(id);
            reject(new Error('Request timeout'));
          }
        }, 30000);
      });
    },
    
    // Métodos de conveniência
    get: function(url, headers = {}) {
      return this.request(url, { method: 'GET', headers });
    },
    
    post: function(url, body, headers = {}) {
      return this.request(url, { method: 'POST', body, headers });
    },
    
    put: function(url, body, headers = {}) {
      return this.request(url, { method: 'PUT', body, headers });
    },
    
    delete: function(url, headers = {}) {
      return this.request(url, { method: 'DELETE', headers });
    }
  };

  console.log('Promokit API ready. Use window.promokitAPI to make requests.');
})();