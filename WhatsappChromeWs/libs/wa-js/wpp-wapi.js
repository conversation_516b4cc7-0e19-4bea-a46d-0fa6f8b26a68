var TEMPO_PEGAR_MENSAGEM_RECENTE = 5;

function ajusteTelefoneWhatsapp(telefone) {
    var tipo = libphonenumber.parsePhoneNumber('+' + telefone).getType();

    if( telefone.indexOf('8431131968') !== -1 ) {
        return telefone;
    }
    if( tipo !== 'FIXED_LINE' && telefone.length  < 13 && telefone.indexOf("55") === 0 ) {
        telefone = telefone.substr(0, 4) + '9' + telefone.substr(4);
    }

    return telefone;
}

window.WAPI = {};

window.WAPI.obtenhaMeuNumero = function() {
    let numero = WAPI.getMe().user;
    numero = ajusteTelefoneWhatsapp(numero);

    return numero.substring(2);
}

window.WAPI.sendMessageToID = async function (id, message) {
    return WPP.chat.sendTextMessage(id, message);
}

window.WAPI.isLoggedIn = function () {
    // Contact always exists when logged in
    return WPP.isFullReady;
};

window.WAPI.marqueDigitando = async function(id) {
    const comandoDigitando = await WPP.chat.markIsComposing(id, 3000);

    return comandoDigitando;
}

function gereVariacoesTelefone(telefone) {
    var tipo = libphonenumber.parsePhoneNumber('+' + telefone).getType();
    var variacoes = [telefone]; // Inclui a versão original do número

    // Exceção específica para não ajustar
    if(telefone.indexOf('8431131968') !== -1) {
        return variacoes;
    }

    // Adiciona a versão do número com o "9", se aplicável
    if(tipo !== 'FIXED_LINE' && telefone.length < 13 && telefone.indexOf("55") === 0) {
        variacoes.push(telefone.substr(0, 4) + '9' + telefone.substr(4));
    }

    // Adiciona a versão do número sem o "9", se aplicável
    if(tipo !== 'FIXED_LINE' && telefone.length === 13 && telefone.substr(4, 1) === '9') {
        variacoes.push(telefone.substr(0, 4) + telefone.substr(5));
    }

    return variacoes;
}

window.WAPI.queryExistsLocal = async function (telefone) {
    telefone = telefone.replace('@c.us', '').replace('@g.us', '');

    var contatos = await WPP.contact.list();

    const telefones = gereVariacoesTelefone(telefone);

    const contatosEncontrados = contatos.filter(contato =>
        telefones.includes(contato.__x_id.user)
    );

    console.log('contatos:', contatosEncontrados);

    let contatoEncontrado = null;
    if( contatosEncontrados.length > 1 ) {
        let chat0 = await WPP.chat.get(contatosEncontrados[0].id);
        let chat1 = await WPP.chat.get(contatosEncontrados[1].id);

        contatoEncontrado = chat0.msgsLength > chat1.msgsLength ? contatosEncontrados[0] : contatosEncontrados[1];
    } else if( contatosEncontrados.length === 1 ) {
        contatoEncontrado = contatosEncontrados[0];
    }

    if( contatoEncontrado ) {
        const data = {
            numberExists: true,
            id: contatoEncontrado.__x_id,
            status: 200,
            isBusiness: (contatoEncontrado.__x_isBusiness === true),
            canReceiveMessage: true
        };
        return data;
    } else {
        return null;
    }
}

window.WAPI.checkNumberStatus = async function (id) {
    //remove o + do numero
    id = id.replace('+', '');

    if ((id.startsWith('559999') && id.endsWith('<EMAIL>')) || id.startsWith('550000') ) {
        const data = {
            numberExists: false,
            status: 422,
            isBusiness: false,
            canReceiveMessage: false
        };

        return data;
    }

    const respLocal = await WAPI.queryExistsLocal(id);

    if( respLocal ) {
        return respLocal;
    }

    console.log('buscando telefone: ' + id);

    const resp = await WPP.contact.queryExists(id);

    if( resp ) {
        const data = {
            numberExists: true,
            id: resp.wid,
            status: 200,
            isBusiness: (resp.biz === true),
            canReceiveMessage: true
        };
        return data;
    } else {
        const data = {
            numberExists: false,
            status: 404,
            isBusiness: false,
            canReceiveMessage: false
        };

        return data;
    }
}

window.WAPI.notifiqueNovaMsg = async function(newMessage, callback) {
    if (newMessage) {
        let pm = window.WAPI.processMessageObj(newMessage, true, true);
        let message = pm ? JSON.parse(JSON.stringify(pm)) : WAPI.quickClean(newMessage.attributes);
        if (message) {
            var ultimaMsg = await new WhatsappApi().obtenhaUltimaMensagemObjIdChat(message.chatId, true, message);

            if( ultimaMsg ) {
                console.log(ultimaMsg);
                ultimaMsg = JSON.parse(JSON.stringify(ultimaMsg));

                message.ultimaMsg = ultimaMsg;
            }

            callback(message)
        }
    }
}

window.WAPI.isConnected = function () {
    // Phone or connection Disconnected icon appears when phone or connection is disconnected
    const isConnected=(document.querySelector('[data-testid="alert-phone"]') == null && document.querySelector('[data-testid="alert-computer"]') == null) ? true : false;
    return isConnected;
};

window.WAPI.getAllContacts = async function() {
    return await WPP.contact.list();
}

window.WAPI.getMe = function() {
    return WPP.conn.getMyUserId();
}

window.WAPI._serializeChatObj = (obj) => {
    if (obj == undefined) {
        return null;
    }
    return Object.assign(window.WAPI._serializeRawObj(obj), {
        id: obj.id._serialized,
        kind: obj.kind,
        isGroup: obj.isGroup,
        formattedTitle: obj.formattedTitle,
        contact: obj['contact'] ? window.WAPI._serializeContactObj(obj['contact']) : null,
        groupMetadata: obj["groupMetadata"] ? window.WAPI._serializeRawObj(obj["groupMetadata"]) : null,
        presence: obj["presence"] ? window.WAPI._serializeRawObj(obj["presence"]) : null,
        msgs: null
    });
};

window.WAPI._serializeContactObj = (obj) => {
    if (obj == undefined) {
        return null;
    }
    return Object.assign(window.WAPI._serializeRawObj(obj), {
        id: obj.id._serialized,
        formattedName: obj.formattedName,
        pushname: obj.pushname,
        isMyContact: obj.isMyContact,
        isPSA: obj.isPSA,
        isUser: obj.isUser,
        isWAContact: obj.isWAContact,
        profilePicThumbObj: obj.profilePicThumb ? WAPI._serializeProfilePicThumb(obj.profilePicThumb) : {},
        statusMute: obj.statusMute,
        msgs: null
    });
};

window.WAPI._serializeProfilePicThumb = (obj) => {
    if (obj == undefined) {
        return null;
    }

    return {
        eurl: obj.eurl,
        id: obj.id,
        img: obj.img,
        imgFull: obj.imgFull,
        raw: obj.raw,
        tag: obj.tag
    }
};

window.WAPI._serializeMessageObj = (obj) => {
    if (obj == undefined) {
        return null;
    }

    const data = {
        id: obj.id._serialized,
        sender: obj["senderObj"] ? WAPI._serializeContactObj(obj["senderObj"]) : null,
        timestamp: obj.t,
        content: obj.body,
        isGroupMsg: obj.isGroupMsg,
        isLink: obj.isLink,
        isMMS: obj.isMMS,
        isMedia: obj.isMedia,
        isNotification: obj.isNotification,
        isPSA: obj.isPSA,
        type: obj.type,
        chat: WAPI._serializeChatObj(obj['chat']),
        chatId: obj.id.remote,
        quotedMsgObj: WAPI._serializeMessageObj(obj['_quotedMsgObj']),
        mediaData: window.WAPI._serializeRawObj(obj['mediaData'])
    };

    if (obj.isMedia && obj.mediaData.filehash) {
        data.deprecatedMms3Url = obj.mediaData.filehash;
    }

    if (obj.quotedMsg && obj.quotedMsgObj) {
        data.quotedMsg = WAPI._serializeMessageObj(obj.quotedMsgObj);
    }

    return data;
};

window.WAPI._serializeNumberStatusObj = (obj) => {
    if (obj == undefined) {
        return null;
    }

    return Object.assign({}, {
        id: obj.jid,
        status: obj.status,
        isBusiness: (obj.biz === true),
        canReceiveMessage: (obj.status === 200)
    });
};

window.WAPI.processMessageObj = function (messageObj, includeMe, includeNotifications) {
    return messageObj;
};

window.WAPI._serializeRawObj = (obj) => {
    if (obj) {
        return obj.toJSON();
    }
    return {}
};

window.WAPI.obtenhaTodasMensagensRecentesNaoLidas = function(minutos) {
    if( !minutos ) {
        minutos = TEMPO_PEGAR_MENSAGEM_RECENTE;
    }

    const dataLimite = new Date();
    dataLimite.setMinutes(dataLimite.getMinutes() - minutos);

    const chats = WPP.chat.list();
    const mensagens = [];

    for (let i = 0; i < chats.length; i++) {
        const chat = WPP.chat.get(chats[i].id);
        const msgs = chat.msgs._models;

        for (let j = msgs.length - 1; j >= 0; j--) {
            const msg = msgs[j];
            const dataMsg = new Date(msg.t * 1000);

            if (dataMsg < dataLimite) {
                break;
            }

            if (!msg.isMe && !msg.isNotification) {
                const msgSerializada = WAPI.processMessageObj(msg, true, true);
                if (msgSerializada) {
                    mensagens.push(msgSerializada);
                }
            }
        }
    }

    return mensagens;
};

window.WAPI.enviouMensagemAtivacao = function(id) {
    const chat = WPP.chat.get(id);
    if (!chat) return false;

    const msgs = chat.msgs._models;
    const dataLimite = new Date();
    dataLimite.setDate(dataLimite.getDate() - 1); // Últimas 24 horas

    for (let i = msgs.length - 1; i >= 0; i--) {
        const msg = msgs[i];
        const dataMsg = new Date(msg.t * 1000);

        if (dataMsg < dataLimite) break;

        if (msg.isMe && msg.body && msg.body.includes("ativação")) {
            return true;
        }
    }
    return false;
};

window.WAPI.versaoBeta = function() {
    return false;
};

//------
//WhatsappAPI para manter compatibilidade

var regex_url = /(?:^|[ \t])((https?:\/\/)?(?:localhost|[\w-]+(?:\.[\w-]+)+)(:\d+)?(\/\S*)?)/gm

class RespostaContato {
    constructor(contato, sucesso, status, mensagem) {
        this.contato = contato;
        this.sucesso = sucesso;
        this.status = status;
        this.mensagem = mensagem;
    }
}

class WhatsappApi {
    constructor() {
        this.imprimaLog('criando de novo');
    }

    async aguardeLogin(page) {
        if (this.page == null) {
            return;
        }
        //TODO: avoid using delay and make it in a way that it would react to the event.
        //await utils.delay(10000);
        await page.waitForSelector("img[alt='Scan me!']");
        const imageData = await page.evaluate(`document.querySelector("img[alt='Scan me!']").parentElement.getAttribute("data-ref")`);
        //this.imprimaLog(imageData);
        let isLoggedIn = await this.injectScripts(page);
        this.imprimaLog('respondeu');
        while (!isLoggedIn) {
            this.imprimaLog("page is loading");
            await this.sleep(300);
            isLoggedIn = await this.injectScripts(page);
        }
        if (isLoggedIn) {
            this.imprimaLog("Looks like you are logged in now");
            this.inicializou = true;
        }
    }

    sleep(ms) {
        return new Promise(resolve => {
            setTimeout(resolve, ms);
        });
    }
    async estahLogado() {
        if (!this.chequeConexao()) {
            return new Promise((resolve, reject) => {
                return resolve(false);
            });
        }

        return new Promise((resolve, reject) => {
            resolve(WAPI.isLoggedIn());
        });
    }

    obtenhaMensagemLog(mensagem, objeto) {
        var classe = 'WhatsappAPI';
        var currentdate = new Date();
        var datetime = new Date().toISOString();

        var mensagemFinal = '[' + classe + '] ' + datetime + ': ';

        if( typeof mensagem === 'object' ) {
            mensagemFinal += JSON.stringify(mensagem);
        } else {
            mensagemFinal += mensagem;
        }

        if( objeto ) {
            mensagemFinal +=  " -> " + JSON.stringify(objeto);
        }

        return mensagemFinal;
    }

    imprimaLog(mensagem, objeto) {
        console.log(this.obtenhaMensagemLog(mensagem, objeto));
    }

    async obtenhaUltimaMensagemObj(telefone, apenasMinhas) {
        return await this.obtenhaUltimaMensagemObjIdChat(telefone, apenasMinhas);
    }

    //TODO: otimizar método
    async obtenhaUltimaMensagemObjIdChat(idChat, apenasMinhas, mensagemDiferente) {
        var mensagens = [];

        if( apenasMinhas === null || apenasMinhas === undefined ) {
            apenasMinhas = true;
        }

        try {
            mensagens = await WPP.chat.getMessages(idChat, {count: 50});
            //imprimir o body das mensagens
            console.log(mensagens);
        } catch( erro ) {
            console.log("erro pegar msgs", erro);
        }

        const eu = WAPI.getMe()._serialized;

        var msgProcurada = null;
        let indice = 0;
        for( var i = mensagens.length - 1; i >= 0; i -- ) {
            var msgCorrente = mensagens[i];

            if( msgCorrente.broadcast ) {
                continue;
            }

            if( mensagemDiferente && mensagemDiferente.id === msgCorrente.id ) {
                continue;
            }

            if( typeof(msgCorrente.body) === 'string' ) {
                if( apenasMinhas && msgCorrente.from._serialized !== eu ) {
                    continue;
                }

                msgProcurada = msgCorrente;
                break;
            }
        }

        if( msgProcurada ) {
            const msgProcessada = WAPI.processMessageObj(msgProcurada, true, true);
            return msgProcessada;
        }

        return null;
    }

    async obtenhaUltimaMensagem(telefone) {
        var msg = await this.obtenhaUltimaMensagemObj(telefone);

        if( msg ) {
            if( msg.type === 'image' ) {
                return msg.caption;
            }
            return msg.body;
        }

        return "";
    }

    async envieMensagem(telefone, mensagem, delayDigitando = 2000, imagem, objMsg) {
        const dividirEmVariasMsgs = /(\[Nova_Mensagem\])/g;

        const mensagens = mensagem.split(dividirEmVariasMsgs);

        let resposta = null;
        for(let msg of mensagens ) {
            if( msg && msg === '[Nova_Mensagem]')
                continue;

            resposta = await this._envieMensagem(telefone, msg.trimStart(), delayDigitando, imagem, objMsg);
        }

        return resposta;
    }

    async _envieMensagem(telefone, mensagem, delayDigitando = 2000, imagem, msg) {
        const tempoPorLetra = Math.floor(Math.random() * 20) + 15;
        let delaySendSeen = Math.random() * 600;

        delayDigitando = (tempoPorLetra) * (mensagem.length);

        if( msg && msg.tipo === 'CHAT' || msg.tipo === 'SAUDACAO' ) {
            delayDigitando = 0;
        } else if( msg && (msg.tipoDeNotificacao === 'ConfirmacaoPedido' || msg.tipoDeNotificacao === 'Cardapio') &&
            delayDigitando > 1000) {
            delayDigitando = Math.floor(Math.random() * 1500) + Math.floor(Math.random() * 1000) + 1000;
        }

        console.log('[WhatsappAPI] enviando mensagem: ' + mensagem);
        return new Promise(async (resolve, reject) => {
            const estahLogado = await this.estahLogado();
            console.log('[WhatsappAPI] está logado: ' + estahLogado);

            if (!this.chequeConexao() || !estahLogado) {
                console.log(this.obtenhaMensagemLog('\tNão está pronto para enviar'));
                return resolve({
                    sucesso: false,
                    status: 'NAO_PRONTO',
                    mensagem: 'Não está pronto para enviar'
                });
            }

            const respostaContato = await this.verifiqueTelefone(telefone);

            console.log('[WhatsappAPI] verificacao telefone: ', respostaContato);
            if (respostaContato && !respostaContato.sucesso) {
                return resolve({
                    sucesso: false,
                    status: respostaContato.status,
                    mensagem: 'Número não tem Whatsapp.'
                });
            }

            const contato = respostaContato.contato;

            telefone = contato.telefoneVerificado + '@c.us';

            console.log('[WhatsappAPI] tipo msg: ' + msg.tipo);

            var contatoWhatsapp = await WPP.contact.get(telefone);

            var chat = await WPP.chat.find(telefone);
            var deveMarcarComoNaoLido = false;

            if( (chat && chat.unreadCount > 0) || !msg.marcarComoLida) {
                deveMarcarComoNaoLido = true;
            }
            if( contatoWhatsapp && contatoWhatsapp.isContactBlocked ) {
                resolve({
                    sucesso: true,
                    status: 'CONTATO_BLOQUEADO',
                    mensagem: 'Contato está bloqueado'
                });
                return;
            }

            if( msg.tipo !== 'CHAT' && msg.tipo !== 'SAUDACAO') {
                const inicio = new Date();
                var ultimaMensagemEnviada = await this.obtenhaUltimaMensagem(contato.telefoneVerificado);

                if (ultimaMensagemEnviada && mensagem && ultimaMensagemEnviada.trim() === mensagem.trim()) {
                    resolve({
                        sucesso: true,
                        idw: ultimaMensagemEnviada.id,
                        status: 'ENVIADA',
                        mensagem: 'Mensagem já havia sido enviada'
                    });
                    return;
                }
            }

            if( document.hidden ) {
                await WPP.whatsapp.ChatPresence.sendPresenceAvailable();
            }

            const comandoSendSeen = await WPP.chat.markIsRead(telefone);

            if( msg.abrirChat ) {
                await WPP.chat.openChatBottom(telefone);
            }

            setTimeout(async () => {
                let resposta = await this.comandoDigitando(telefone);

                const delay = delayDigitando;

                console.log('\t[WhatsappAPI] vai esperar: ' + delay);

                setTimeout(async () => {
                    console.log('\t[WhatsappAPI] vai mandar o comando');
                    let resposta = null;
                    const urlEncontrada = mensagem.match(regex_url);

                    if( msg.temMenu && msg.menu && msg.menu.opcoes ) {
                        if( msg.menu.opcoes.length < 3 ) {
                            //cria os botões usando as opcoes do menu
                            const rows = [];
                            for( let i = 0; i < msg.menu.opcoes.length; i ++ ) {
                                rows.push({
                                    id: msg.menu.opcoes[i].id,
                                    text: msg.menu.opcoes[i].texto
                                })
                            }
                            WPP.chat.sendTextMessage(telefone, mensagem, {
                                buttons: rows,
                                title: mensagem, // Optional
                            });
                        }

                        const rows = [];
                        for( let i = 0; i < msg.menu.opcoes.length; i ++ ) {
                            rows.push({
                                rowId: msg.menu.opcoes[i].id,
                                title: msg.menu.opcoes[i].texto
                            })
                        }
                        var menu = {
                            "buttonText": msg.menu.textoBotao,
                            "listType": 1,
                            "description": mensagem,
                            "sections": [
                                {
                                    "title": msg.menu.textoSecao,
                                    "rows": rows
                                }
                            ]
                        };

                        if( msg.menu.tituloMensagem ) {
                            menu.title = msg.menu.tituloMensagem || 'teste';
                        }

                        resposta = await WPP.chat.sendListMessage(telefone, menu);
                    }
                    else if( imagem ) {
                        const extensao = msg.imagem.split('.').pop();
                        const tipo = (extensao.indexOf('pdf?v=') !== -1 || extensao.indexOf('pdf') !== -1) ? 'document' : 'image';

                        resposta = await WPP.chat.sendFileMessage(
                            telefone,
                            imagem,
                            {
                                type: tipo,
                                caption: mensagem, // Optional
                                filename: 'arquivo.' + extensao
                            }
                        );

                        if( extensao.indexOf('pdf?v=') !== -1 || extensao.indexOf('pdf') !== -1 ) {
                            let respostaTexto = await WAPI.sendMessageToID(telefone, mensagem);
                        }
                    } else if (urlEncontrada && msg.enviarLinksBotao) {
                        resposta = await WPP.chat.sendTextMessage(telefone, mensagem, {
                            buttons: [
                                {
                                    url: urlEncontrada[0].trim(),
                                    text: 'Acesse o cardápio digital'
                                }
                            ],
                            footer: "Faça seu pedido pelo botão abaixo."
                        });
                    } else {
                        if( msg.fazerPreview && urlEncontrada && msg.linkPreview ) {
                            //resposta = await WAPI.sendLinkWithAutoPreview(telefone, urlEncontrada[0], mensagem);
                            //resposta = await WAPI.sendMessageToID(telefone, mensagem);
                            resposta = WPP.chat.sendTextMessage(telefone, mensagem, {linkPreview: msg.linkPreview});
                        }
                        else {
                            resposta = await WAPI.sendMessageToID(telefone, mensagem);
                        }
                    }

                    if( deveMarcarComoNaoLido ) {
                        let delayMarkAsUnred = Math.random() * 1500 + 1000;

                        setTimeout( async() => {
                            await WPP.chat.markIsUnread(telefone);
                        }, delayMarkAsUnred);
                    }

                    console.log('\t[WhatsappAPI] enviou: ' + resposta);
                    if (resposta) {
                        resolve({
                            sucesso: true,
                            status: 'ENVIADA',
                            idw: resposta.id,
                            mensagem: 'Mensagem enviada com sucesso'
                        });
                    }
                    else {
                        resolve({
                            sucesso: false,
                            status: 'IMPOSSIVEL_ENVIAR',
                            mensagem: 'Falha ao enviar mensagem'
                        });
                    }
                }, delay);
            }, delaySendSeen);
        });
    }

    chequeConexao() {
        return window.WAPI != null && window.WAPI.isConnected();
    }

    async comandoDigitando(telefone) {
        const comandoDigitando = await WPP.chat.markIsComposing(telefone, 3000);

        this.imprimaLog('marque: ' + comandoDigitando);

        return comandoDigitando;
    }

    ehComandoCriarCartao(textoMensagem) {
        if (!textoMensagem)
            return false;
        const opcoes = ['meu cartão', 'meucartao', 'meucartão', 'novo cartão', 'meu cartao', 'novo cartao'].join(",").toUpperCase();
        return (opcoes.indexOf(textoMensagem.toUpperCase()) != -1);
    }

    ehComandoOlaBot(textoMensagem) {
        if (!textoMensagem)
            return false;
        const nomeBot = UrlUtils_1.UrlUtils.obtenhaNomeBot();
        const opcoes = ['oi ' + nomeBot, 'oie ' + nomeBot, 'Olá ' + nomeBot].join(",").toUpperCase();

        return (opcoes.indexOf(textoMensagem.toUpperCase()) != -1);
    }

    async chegaramMensagens(mensagem) {
        try {
            debugger;
            console.log('chegaramMensagens');
            
            mensagem = window.WAPI.processMessageObj(mensagem, true, true);

            mensagem.horario = new Date(mensagem.t * 1000);

            const diferencaEmMinutos = Math.round((new Date() - mensagem.horario) / 1000 / 60);

            if( !mensagem.senderObj ) {
                console.log(`[WAPI] [chegaram mensagens] mensagem sem sender: ${mensagem.id} ${diferencaEmMinutos} min. Type: ${mensagem.type} Body: ${mensagem.body}, Mensagem:`, mensagem);
                return;
            }

            debugger;

            if( diferencaEmMinutos > TEMPO_PEGAR_MENSAGEM_RECENTE ) {
                console.log(`[WAPI] [chegaram mensagens] mensagem muito antiga: ${mensagem.id} ${diferencaEmMinutos} min. Body: ${mensagem.body}`,
                    `T: ${mensagem.t} Timestamp: ${mensagem.timestamp}`, 'Sender: ', mensagem.sender, new Date(mensagem.timestamp * 1000).toLocaleString(), 'Mensagem: ', mensagem);
                return;
            }

            console.log(`[WAPI] [chegaram mensagens] ${mensagem.id} tempo mensagem: ${diferencaEmMinutos} min. Nova: ${mensagem.isNewMsg}
T: ${mensagem.t} Timestamp: ${mensagem.timestamp} Body: ${mensagem.body}, Mensagem: `, mensagem);

            if (mensagem.chat == null || mensagem.chat.contact === null || mensagem.isNotification ) {
                console.log(`[WAPI] [chegaram mensagens]  Ignorando mensagem: ${mensagem.id}`, mensagem);
                return;
            }

            if( mensagem.isGroupMsg ) {
                return;
            }

            if( mensagem.chat && mensagem.chat.id && mensagem.chat.id._serialized && mensagem.chatId._serialized.indexOf('status@') !== -1 ) {
                return;
            }

            var chat = mensagem.chat;
            if( chat ) {
                mensagem.nomeChat = chat.formattedTitle ? chat.formattedTitle : chat.name;
            }

            console.log(`[chegaram mensagens] ${mensagem.id} Body: ${mensagem.body}. sender: `, mensagem.senderObj);

            const sender = mensagem.senderObj;


            const ultimaMensagem = await new WhatsappApi().obtenhaUltimaMensagemObj(sender.id);

            let horarioUltimaMensagem = new Date();

            if( ultimaMensagem ) {
                horarioUltimaMensagem = new Date(ultimaMensagem.timestamp * 1000);
            } else { //setando uma data antiga pra entender que nunca falou com o contato
                horarioUltimaMensagem.setTime(0);
            }

            mensagem.horarioUltimaMensagem = horarioUltimaMensagem;


            console.log(`[WAPI] [chegaram mensagens] ${mensagem.id} Body: ${mensagem.body}`, 'Sender: ', mensagem.sender,
                'Mensagem: ', mensagem);

            // Enviar evento para background.js
            window.postMessage({
                fromPromokit: true,
                type: 'whatsapp_new_message',
                message: JSON.stringify(mensagem)
            }, '*');
            console.log(`[WAPI] [chegaram mensagens] ${mensagem.id} Evento enviado para background Body: ${mensagem.body}`,
                `Sender: ${mensagem.sender}`);
        } catch (error) {
            console.error(`${mensagem.id} - Nome do erro: ${error.name} Mensagem do erro: ${error.message}`);
            console.error(`${mensagem.id} - Pilha de chamadas (stack trace):`, error.stack);
        }
        finally {
        }
    }

    aguardeMensagens() {
        WPP.on('chat.new_message', this.chegaramMensagens);
    }

    async envieEProcesseRespostaDialogFlow(nome, telefone, textoMensagem) {
        const resposta = await SessaoConversa_1.SessaoConversa.Instance.executeComando(telefone, textoMensagem);
        for (const sMensagem of resposta.mensagens) {
            const objResposta = await this.envieMensagem(telefone, sMensagem, 500);
        }
        var nomesContextos = resposta.contextos.join(',');
        if (nomesContextos.indexOf('criar_cartao') != -1) {
            let nomeCartao = nome;
            if (resposta.variaveis.person) {
                nomeCartao = resposta.variaveis.person.name;
            }
            else if (resposta.variaveis.nomeDuvida) {
                nomeCartao = resposta.variaveis.nomeDuvida;
            }
            this.crieCartao(nomeCartao, telefone).then(async (resposta) => {
                if (resposta.sucesso) {
                    SessaoConversa_1.SessaoConversa.Instance.apagueSessao(telefone);
                    const mensagem2 = 'Cartão criado com sucesso! Acesse agora pelo link: ' +
                        'https://' + UrlUtils_1.UrlUtils.obtenhaNomeEmpresa() + '.promokit.com.br/cliente';
                    let objResposta2 = await this.envieMensagem(telefone, mensagem2, 0);
                    objResposta2 = await this.envieMensagem(telefone, 'Até mais! ' +
                        'Se quiser falar comigo novamente só enviar um Oi.', 0);
                }
                else {
                    const mensagem2 = 'Não foi possível criar seu cartão. Tente novamente mais tarde!';
                    const objResposta2 = await this.envieMensagem(telefone, mensagem2, 0);
                }
            }).catch((erro) => {
            });
        }
        else if (nomesContextos.indexOf('fim') != -1) {
            SessaoConversa_1.SessaoConversa.Instance.apagueSessao(telefone);
        }
    }
    //adicionar parametro id da mensagem
    crieCartao(nome, telefone) {
        const url = UrlUtils_1.UrlUtils.obtenhaURLEmpresa() + '/cartoes2/novo';
        if (telefone.length < 11) {
            telefone = telefone.substr(0, 2) + '9' + telefone.substr(2);
        }
        const dados = {
            nome: nome,
            telefone: telefone
        };
        var options = {
            method: 'POST',
            uri: url,
            body: {
                contato: dados
            },
            json: true // Automatically stringifies the body to JSON
        };
        return new Promise((resolve, reject) => {
            rp(options)
                .then(function (resposta) {
                    resolve(resposta);
                })
                .catch((err) => {
                    //this.this.imprimaLog(err);
                    this.imprimaLog(err);
                    resolve({
                        sucesso: false
                    });M
                });
        });
    }

    obtenhaContatoBD() {
        return null;
    }

    verifiqueTelefone(telefone) {
        let contato = null;

        telefone = telefone + '';


        return new Promise(async (resolve, reject) => {
            var estahLogado = await this.estahLogado();
            var conexaoOk = this.chequeConexao();

            if( !estahLogado || !conexaoOk ) {
                this.imprimaLog('\tnão está pronto para enviar');
                const resposta = new RespostaContato(null, false,
                    'NAO_PRONTO', 'Não está pronto para enviar');

                return resolve(resposta);
            }

            contato = this.obtenhaContatoBD(telefone);

            if( contato ) {
                this.imprimaLog(contato);
            } else {
                this.statusTelefone(telefone).then((respostaStatus) => {
                    this.imprimaLog('status', respostaStatus);

                    if (respostaStatus.status === 200) {
                        const telefoneVerificado = respostaStatus.id.user;
                        const objetoContato = {
                            telefone: telefone,
                            telefoneVerificado: telefoneVerificado
                        };
                        contato = objetoContato;

                        this.imprimaLog(objetoContato);

                        //const resposta = new RespostaContato_1.RespostaContato;

                        const resposta = new RespostaContato(contato, true, 'VALIDOU', "Whatsapp encontrado!");

                        this.salveContato(contato);

                        return resolve(resposta);
                    } else if( respostaStatus.status === 422 ) {
                        const resposta = new RespostaContato(null, false, 'CONSUMIDOR_NAO_IDENTIFICADO',
                            'Não tem consumidor identificado');

                        return resolve(resposta);
                    }
                    else {
                        const resposta = new RespostaContato(null, false, 'NAO_TEM_WHATSAPP',
                            'Numero de telefone não tem Whatsapp');

                        return resolve(resposta);
                    }
                });
            }
        });
    }

    statusTelefone(telefone) {
        return new Promise( async(resolve, reject) => {
            if (!this.chequeConexao()) {
                return resolve({
                    sucesso: false
                });
            }

            telefone += '@c.us';
            var status = await WAPI.checkNumberStatus(telefone);

            return resolve(status);
        });
    }

    async contatoEnviouMensagem(telefone) {
        this.imprimaLog('Verificando se contato enviou mensagem');

        if (!this.chequeConexao()) {
            return new Promise((resolve, reject) => {
                resolve({
                    sucesso: false,
                    status: 'NAO_PRONTO'
                });
            });
        }

        const respostaContato = await this.verifiqueTelefone(telefone);
        if (respostaContato && !respostaContato.sucesso) {
            return respostaContato;
        }

        const contato = respostaContato.contato;
        telefone = contato.telefone + '@c.us';

        const resposta = await WAPI.enviouMensagemAtivacao(telefone);

        this.imprimaLog(resposta);
        if (resposta == null) {
            return {
                sucesso: false,
                status: 'NAO_PRONTO',
                mensagem: 'Não está pronto'
            };
        }

        return {
            sucesso: resposta,
            mensagem: resposta ? 'Contato enviou mensagem' : 'Contato ainda não enviou mensagem'
        };
    }

    salveContato(contato) {

    }
}
