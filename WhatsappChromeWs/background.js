// Importar Socket.IO e controlador de reconexão
importScripts('socket.io.min.js');
importScripts('reconnect-controller.js');

// Paleta de cores da badge
const BADGE_COLORS = {
  green: '#19b354',  // conectado
  red: '#DC2626',    // desconectado/erro fatal
  amber: '#daa300',  // erro transitório/tentando
  blue: '#3B82F6',   // conectando
  gray: '#6B7280'    // manual/off
};

class BackgroundManager {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = Infinity; // ilimitado
    this.reconnectDelay = 1000;
    this.heartbeatInterval = null;
    // Teto dmáximo para o backoff (ex.: 30s). Ao atingir o teto, aplicamos jitter no intervalo [cap/2, cap]
    this.reconnectMaxDelayMs = 30000;
    this.messageQueue = [];
    this.serverUrl = '';

    // Controlador de reconexão (decorrelated jitter)
    this.reconnect = new ReconnectController({
      baseDelayMs: this.reconnectDelay,
      maxDelayMs: this.reconnectMaxDelayMs,
      maxAttempts: this.maxReconnectAttempts,
      onCountdownUpdate: (secs) => this.updateBadge(`${secs}s`, BADGE_COLORS.amber),
      onLog: (message) => {
        console.log(message);
        this.notifyPopup('log', { message: `Reconnect: ${message}`, type: 'warning' });
      },
      onFire: async () => {
        this.reconnectAttempts = this.reconnect.attempts; // manter status visível
        await this.connect();
      }
    });
    this.init();
  }

  async init() {
    await this.loadConfig();
    this.setupEventListeners();
    this.setupTabListeners();
    this.setupStorageListeners();
    // Se já existir uma empresa selecionada e autoConnect estiver habilitado, conectar automaticamente
    await this.maybeAutoConnectIfCompanySelected();
    // Habilitar em todas as páginas
    chrome.action.enable();
  }

  async loadConfig() {
    // Buscar empresa selecionada como fonte primária da URL
    const companyResult = await chrome.storage.local.get(['selectedCompany']);
    const syncResult = await chrome.storage.sync.get(['autoConnect']);
    
    // Usar URL da empresa selecionada se disponível, senão usar valor padrão
    if (companyResult.selectedCompany && companyResult.selectedCompany.serverUrl) {
      this.serverUrl = companyResult.selectedCompany.serverUrl;
    } else {
      this.serverUrl = 'https://localhost';
    }
    
    this.autoConnect = syncResult.autoConnect !== false;
  }

  setupEventListeners() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      try {
        switch (request.action) {
          case 'connect':
            this.connect().then(() => {
              sendResponse({ success: true });
            }).catch((error) => {
              const errorMessage = error?.message || error?.toString() || 'Erro desconhecido';
              sendResponse({ success: false, error: errorMessage });
            });
            break;
          case 'disconnect':
            this.disconnect();
            sendResponse({ success: true });
            break;
          case 'getStatus':
            sendResponse({
              isConnected: this.isConnected,
              reconnectAttempts: this.reconnectAttempts,
              serverUrl: this.serverUrl
            });
            break;
          case 'sendMessage':
            this.sendMessage(request.data);
            sendResponse({ success: true });
            break;
          case 'updateConfig':
            this.saveConfig(request.config);
            sendResponse({ success: true });
            break;
          default:
            sendResponse({ error: 'Unknown action' });
        }
      } catch (error) {
        console.error('Erro no listener de mensagens:', error);
        const errorMessage = error?.message || error?.toString() || 'Erro interno';
        sendResponse({ success: false, error: errorMessage });
      }
      return true;
    });
  }

  setupStorageListeners() {
    // Reagir à definição/alteração da empresa selecionada e a mudanças no autoConnect
    chrome.storage.onChanged.addListener(async (changes, areaName) => {
      try {
        if (areaName === 'local' && changes.selectedCompany) {
          const newCompany = changes.selectedCompany.newValue;
          if (newCompany && newCompany.serverUrl) {
            this.serverUrl = newCompany.serverUrl;
            this.notifyPopup('log', { message: `Empresa selecionada: ${newCompany.subdomain || 'sem subdomínio'} - conectando...`, type: 'info' });
            await this.loadConfig();
            if (this.autoConnect) {
              await this.connect();
            }
          } else if (!newCompany) {
            // Empresa removida
            this.notifyPopup('log', { message: 'Empresa desmarcada. Desconectando...', type: 'warning' });
            this.disconnect();
          }
        }
        if (areaName === 'sync' && changes.autoConnect) {
          this.autoConnect = changes.autoConnect.newValue !== false;
          if (this.autoConnect) {
            await this.maybeAutoConnectIfCompanySelected();
          }
        }
      } catch (err) {
        console.error('Erro no listener de storage:', err);
      }
    });
  }

  async maybeAutoConnectIfCompanySelected() {
    try {
      const { selectedCompany } = await chrome.storage.local.get(['selectedCompany']);
      if (this.autoConnect && selectedCompany) {
        // Garantir serverUrl atualizado
        if (selectedCompany.serverUrl) {
          this.serverUrl = selectedCompany.serverUrl;
        }
        await this.connect();
      }
    } catch (err) {
      console.error('Erro ao tentar autoconectar com empresa selecionada:', err);
    }
  }

  async saveConfig(config) {
    // Salvar apenas configurações que não sejam URL (autoConnect, etc)
    const { serverUrl, ...configWithoutUrl } = config;
    await chrome.storage.sync.set(configWithoutUrl);
    
    // URL sempre vem de selectedCompany, recarregar config
    await this.loadConfig();
  }

  async connect() {
    // Verificar se já está conectado OU conectando
    if (this.socket && (this.socket.connected || this.socket.connecting)) {
      console.log('Socket.IO já está conectado ou conectando');
      this.notifyPopup('log', { message: 'Socket.IO já está conectado ou conectando', type: 'warning' });
      return;
    }

    // Cleanup de socket existente antes de criar novo
    if (this.socket) {
      console.log('🧹 Limpando socket existente...');
      this.socket.off(); // Remove TODOS os listeners
      this.socket.disconnect();
      this.socket = null;
    }

    try {
      // Recarregar configuração para garantir URL mais atual
      await this.loadConfig();
      
      // Buscar empresa selecionada do storage
      const result = await chrome.storage.local.get(['selectedCompany']);
      let empSubdomain = 'default'; // valor padrão
      
      if (result.selectedCompany && result.selectedCompany.subdomain) {
        empSubdomain = result.selectedCompany.subdomain;
        console.log(`Usando subdomínio da empresa configurada: ${empSubdomain}`);
        this.notifyPopup('log', { message: `Conectando com empresa: ${empSubdomain}`, type: 'info' });
      } else {
        console.warn('Nenhuma empresa configurada, usando valor padrão');
        this.notifyPopup('log', { message: 'Nenhuma empresa configurada, usando valor padrão', type: 'warning' });
      }

      console.log(`Conectando ao Socket.IO: ${this.serverUrl}`);
      this.notifyPopup('log', { message: `Conectando ao Socket.IO: ${this.serverUrl}`, type: 'info' });
      // Sinalizar estado de conectando
      this.updateBadge('CON', BADGE_COLORS.blue);

      if( this.serverUrl.includes('localhost') ) {
          this.serverUrl = `http://localhost:3000`;
      }
      this.socket = io(this.serverUrl, {
        transports: ['websocket'],
        reconnection: false, // não reconectar automaticamente
        path: '/testesocketio',
        query: {
          emp: empSubdomain
        }
      });
      
      // Configurar listeners uma única vez
      this.setupSocketListeners();

    } catch (error) {
      console.error('Erro ao criar Socket.IO:', error);
      this.updateBadge('ERR', BADGE_COLORS.amber);
      const errorMessage = error?.message || error?.toString() || 'Erro desconhecido';
      this.notifyPopup('log', { message: `Erro ao criar Socket.IO: ${errorMessage}`, type: 'error' });
    }
  }

  setupSocketListeners() {
    console.log('🔧 Configurando listeners do Socket.IO...');
    
    this.socket.on('connect', () => {
      console.log('Socket.IO conectado com sucesso');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      // reset e cancela qualquer countdown agendado
      this.reconnect.reset();
      this.reconnectAttempts = 0;
      this.updateBadge('ON', BADGE_COLORS.green);
      this.startHeartbeat();
      this.processMessageQueue();
      this.notifyPopup('connected');
      this.notifyPopup('log', { message: 'Socket.IO conectado com sucesso', type: 'success' });
    });

    this.socket.on('reload', (data) => {
      console.log('🔄 Evento RELOAD recebido do servidor:', data);
      console.log('🔄 Tipo de data:', typeof data);
      console.log('🔄 Data stringified:', JSON.stringify(data));
      this.notifyPopup('log', { message: `Evento RELOAD recebido: ${JSON.stringify(data)}`, type: 'info' });
      
      // Enviar evento reload para content scripts do WhatsApp Web
      console.log('📤 Enviando evento reload para content scripts...');
      this.notifyContentScripts('reload', data);
    });

    this.socket.on('ping', (data) => {
      console.log('🔔 Evento PING recebido do servidor:', data);
      console.log('🔔 Tipo de data:', typeof data);
      console.log('🔔 Data stringified:', JSON.stringify(data));
      this.notifyPopup('log', { message: `Evento PING recebido: ${JSON.stringify(data)}`, type: 'info' });
      
      // Enviar evento ping para content scripts do WhatsApp Web
      console.log('📤 Enviando evento ping para content scripts...');
      this.notifyContentScripts('ping', data);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket.IO desconectado:', reason);
      this.isConnected = false;
      this.updateBadge('OFF', BADGE_COLORS.red);
      this.stopHeartbeat();
      this.notifyPopup('disconnected');
      this.notifyPopup('log', { message: `Socket.IO desconectado: ${reason}`, type: 'warning' });
      
      if (reason !== 'io client disconnect' && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Erro no Socket.IO:', error);
      this.updateBadge('ERR', BADGE_COLORS.amber);
      this.handleConnectionError(error);
      // Verificar se error.message existe antes de acessar
      const errorMessage = error?.message || error?.toString() || 'Erro desconhecido';
      this.notifyPopup('log', { message: `Erro no Socket.IO: ${errorMessage}`, type: 'error' });
    });
    
    console.log('✅ Listeners configurados');
  }

  disconnect() {
    if (this.socket) {
      console.log('🧹 Removendo todos os listeners...');
      this.socket.off(); // Remove TODOS os listeners
      this.socket.disconnect();
      this.socket = null;
    }
    this.stopHeartbeat();
    this.isConnected = false;
    // resetar controlador (cancela countdown e zera attempts)
    this.reconnect.reset();
    // Desligado manualmente
    this.updateBadge('-', BADGE_COLORS.gray);
    this.notifyPopup('disconnected');
    this.notifyPopup('log', { message: 'Desconectado', type: 'info' });
  }

  handleConnectionError(error) {
    if (this.reconnect.attempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    } else {
      console.error('Máximo de tentativas de reconexão atingido');
      this.updateBadge('OFF', BADGE_COLORS.red);
      this.notifyPopup('log', { message: 'Máximo de tentativas de reconexão atingido', type: 'error' });
    }
  }

  scheduleReconnect() {
    this.reconnect.scheduleNext();
    this.reconnectAttempts = this.reconnect.attempts;
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.socket) {
        this.socket.emit('ping', { timestamp: Date.now() });
      }
    }, 30000);
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  clearReconnectCountdown() {
    if (this.reconnect) {
      this.reconnect.cancel();
    }
  }

  sendMessage(data) {
    if (this.isConnected && this.socket && this.socket.connected) {
      // Usar padrão whatsapp-evento com tipo específico
      this.socket.emit('whatsapp-evento', {
        tipo: 'NOVA_MENSAGEM',
        dados: JSON.parse(data)
      });
      console.log('Evento whatsapp-evento enviado via Socket.IO:', data);
      // Agora 'data' já contém diretamente os dados da mensagem (id, body, sender, etc.)
      this.notifyPopup('log', { message: `Evento NOVA_MENSAGEM enviado: ${data.body || 'sem body'}`, type: 'success' });
    } else {
      console.log('Socket.IO não conectado, adicionando à fila:', data);
      this.messageQueue.push(data);
      this.notifyPopup('log', { message: 'Socket.IO não conectado, adicionando à fila', type: 'warning' });
    }
  }

  processMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const message = this.messageQueue.shift();
      this.sendMessage(message);
    }
  }

  handleMessage(data) {
    switch (data.type) {
      case 'ping':
        this.socket.emit('pong', { timestamp: Date.now() });
        break;
      case 'notification':
        this.showNotification(data.title, data.message);
        break;
      case 'whatsapp_message':
        this.notifyContentScripts('whatsapp_message', data);
        break;
      default:
        this.notifyContentScripts('message', data);
    }
  }

  setupTabListeners() {
    // Verificar quando uma aba é ativada
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      const tab = await chrome.tabs.get(activeInfo.tabId);
      this.updateIconState(tab);
    });

    // Verificar quando uma aba é atualizada
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete') {
        this.updateIconState(tab);
      }
    });

    // Verificar todas as abas ao iniciar
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        if (tab.active) {
          this.updateIconState(tab);
        }
      });
    });
  }

  updateIconState(tab) {
    // Manter extensão sempre habilitada em todas as páginas
    chrome.action.enable(tab.id);
  }

  async notifyContentScripts(type, data = {}) {
    const tabs = await chrome.tabs.query({ url: 'https://web.whatsapp.com/*' });
    
    for (const tab of tabs) {
      try {
        await chrome.tabs.sendMessage(tab.id, { type, data });
      } catch (error) {
        console.log(`Erro ao enviar mensagem para tab ${tab.id}:`, error);
      }
    }
  }

  async notifyPopup(type, data = {}) {
    try {
      // Verificar se o runtime ainda está válido antes de enviar mensagem
      if (chrome.runtime && chrome.runtime.id) {
        // Tentar verificar se há views abertas (compatibilidade com MV2/MV3)
        let hasOpenViews = false;

        try {
          // Método para Manifest V2
          if (chrome.extension && chrome.extension.getViews) {
            const views = chrome.extension.getViews({ type: 'popup' });
            hasOpenViews = views.length > 0;
          }
        } catch (e) {
          // Se getViews não estiver disponível, assumir que pode tentar enviar
          hasOpenViews = true;
        }

        if (hasOpenViews) {
          await chrome.runtime.sendMessage({ type, data });
        }
        // Se não há popup aberto, simplesmente ignora silenciosamente
      }
    } catch (error) {
      // Popup não está aberto ou runtime inválido, ignorar silenciosamente
      // Apenas fazer log em modo debug se necessário
      if (error.message && !error.message.includes('Receiving end does not exist')) {
        console.debug('Erro inesperado ao notificar popup:', error.message);
      }
    }
  }

  updateBadge(text, color) {
    try {
      chrome.action.setBadgeText({ text: text || '' });

      // Forçar texto branco sempre que possível (nem todas as versões suportam esta API)
      try {
        if (chrome.action.setBadgeTextColor) {
          chrome.action.setBadgeTextColor({ color: '#FFFFFF' });
        }
      } catch (e) {
        // Ignorar se não suportado
        console.debug('setBadgeTextColor não suportado:', e.message);
      }

      if (color) {
        chrome.action.setBadgeBackgroundColor({ color });
      }
    } catch (error) {
      console.warn('Erro ao atualizar badge:', error.message);
    }
  }

  showNotification(title, message) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'images/icon48.png',
      title: title || 'Promokit WebSocket',
      message: message || 'Nova mensagem recebida'
    });
  }
}

const backgroundManager = new BackgroundManager();

chrome.runtime.onInstalled.addListener((details) => {
  console.log('Extensão instalada/atualizada:', details.reason);

  if (details.reason === 'install') {
    console.log('Primeira instalação - configurando valores padrão');
    chrome.storage.sync.set({
      serverUrl: 'https://localhost',
      autoConnect: true
    }).then(() => {
      console.log('Configurações padrão salvas com sucesso');
    }).catch((error) => {
      console.error('Erro ao salvar configurações padrão:', error);
    });
  }

  // Garantir que a extensão esteja habilitada
  chrome.action.enable().catch((error) => {
    console.warn('Erro ao habilitar action:', error);
  });
});