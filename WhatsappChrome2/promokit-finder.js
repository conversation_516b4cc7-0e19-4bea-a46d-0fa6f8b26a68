document.addEventListener('DOMContentLoaded', function() {
  const findTabsBtn = document.getElementById('findTabsBtn');
  const loading = document.getElementById('loading');
  const results = document.getElementById('results');
  const noResults = document.getElementById('noResults');
  const tabsList = document.getElementById('tabsList');
  const backToMainBtn = document.getElementById('backToMainBtn');
  
  findTabsBtn.addEventListener('click', function() {
    // Mostrar loading
    findTabsBtn.disabled = true;
    loading.classList.remove('hidden');
    results.classList.add('hidden');
    noResults.classList.add('hidden');
    tabsList.innerHTML = '';
    
    // Enviar mensagem para o background script
    chrome.runtime.sendMessage({ action: 'findPromoKitTabs' }, function(response) {
      findTabsBtn.disabled = false;
      loading.classList.add('hidden');
      
      if (response && response.tabs && response.tabs.length > 0) {
        // Mostrar resultados
        results.classList.remove('hidden');
        
        // Criar elementos para cada aba encontrada
        response.tabs.forEach(tab => {
          const tabItem = document.createElement('div');
          tabItem.className = 'tab-item';
          
          // Extrair subdomínio da URL
          const urlObj = new URL(tab.url);
          const hostname = urlObj.hostname;
          const subdomain = hostname.split('.')[0];
          
          tabItem.innerHTML = `
            <img class="tab-icon" src="${tab.favIconUrl || 'assets/default-favicon.png'}" alt="Favicon">
            <div class="tab-info">
              <div class="tab-title">${tab.title}</div>
              <div class="tab-url">https://<span class="subdomain">${subdomain}</span>.promokit.com.br</div>
            </div>
          `;
          
          // Adicionar evento de clique para focar na aba
          tabItem.addEventListener('click', function() {
            chrome.tabs.update(tab.id, { active: true });
            chrome.windows.update(tab.windowId, { focused: true });
          });
          
          tabsList.appendChild(tabItem);
        });
      } else {
        // Mostrar mensagem de nenhum resultado
        noResults.classList.remove('hidden');
      }
    });
  });
  
  // Botão para voltar para a página principal
  backToMainBtn.addEventListener('click', function() {
    window.location.href = 'index.html';
  });
});
