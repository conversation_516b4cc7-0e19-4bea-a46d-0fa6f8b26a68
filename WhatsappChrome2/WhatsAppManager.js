/**
 * WhatsAppManager - Classe para gerenciar comportamentos do WhatsApp Web
 * 
 * Esta classe encapsula funcionalidades relacionadas ao WhatsApp Web, como:
 * - Monitoramento de mudanças de chat
 * - Envio de mensagens
 * - Sistema de eventos personalizado
 */
class WhatsAppManager {
  constructor() {
    this.activeChat = null;
    this.listeners = {};
    this.isInitialized = false;
    
    // Definir os tipos de eventos disponíveis
    this.EVENT_TYPES = {
      INITIALIZED: 'initialized',
      CHAT_CHANGED: 'chatChanged',
      NEW_MESSAGE: 'newMessage',
      MESSAGE_SENT: 'messageSent',
      MESSAGE_ERROR: 'messageError'
    };
  }

  /**
   * Inicializa o gerenciador e configura os listeners
   */
  initialize() {
    if (this.isInitialized) {
      console.log('WhatsAppManager já está inicializado');
      return;
    }

    // Verificar se o WPP está disponível
    if (typeof window.WPP === 'undefined') {
      console.error('WPP não está disponível. Certifique-se de que o wppconnect está carregado.');
      return;
    }

    this.setupChatChangeListener();
    this.setupMessageListener();
    this.isInitialized = true;
    console.log('WhatsAppManager inicializado com sucesso');
    
    // Emitir evento de inicialização
    this.triggerEvent(this.EVENT_TYPES.INITIALIZED, this);
  }

  /**
   * Configura o listener para mudanças de chat
   */
  setupChatChangeListener() {
    window.WPP.on('chat.active_chat', (chat) => {
      const previousChat = this.activeChat;
      this.activeChat = chat;
      
      console.log('Chat ativo mudou para:', chat ? chat.id._serialized : 'nenhum');
      
      this.triggerEvent(this.EVENT_TYPES.CHAT_CHANGED, {
        currentChat: chat,
        previousChat: previousChat
      });
    });
  }

  /**
   * Configura o listener para novas mensagens
   */
  setupMessageListener() {
    window.WPP.on('chat.new_message', (message) => {
      console.log('Nova mensagem recebida:', message);
      this.triggerEvent(this.EVENT_TYPES.NEW_MESSAGE, message);
    });
  }

  /**
   * Envia uma mensagem para um chat específico
   * @param {string} chatId - ID do chat para enviar a mensagem
   * @param {string} message - Conteúdo da mensagem
   * @returns {Promise} - Promise com o resultado do envio
   */
  async sendMessage(chatId, message) {
    try {
      if (!window.WPP) {
        throw new Error('WPP não está disponível');
      }
      
      const result = await window.WPP.chat.sendTextMessage(chatId, message);
      console.log('Mensagem enviada com sucesso:', result);
      this.triggerEvent(this.EVENT_TYPES.MESSAGE_SENT, {
        chatId,
        message,
        result
      });
      
      return result;
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      this.triggerEvent(this.EVENT_TYPES.MESSAGE_ERROR, {
        chatId,
        message,
        error
      });
      
      throw error;
    }
  }

  /**
   * Envia uma mensagem para o chat ativo atual
   * @param {string} message - Conteúdo da mensagem
   * @returns {Promise} - Promise com o resultado do envio
   */
  async sendMessageToActiveChat(message) {
    if (!this.activeChat) {
      throw new Error('Nenhum chat ativo no momento');
    }
    
    return this.sendMessage(this.activeChat.id._serialized, message);
  }

  /**
   * Registra um callback para o evento de inicialização
   * @param {Function} callback - Função a ser chamada quando o gerenciador for inicializado
   * @returns {WhatsAppManager} - A instância atual para encadeamento
   */
  onInitialized(callback) {
    return this.on(this.EVENT_TYPES.INITIALIZED, callback);
  }

  /**
   * Registra um callback para o evento de mudança de chat
   * @param {Function} callback - Função a ser chamada quando o chat ativo mudar
   * @returns {WhatsAppManager} - A instância atual para encadeamento
   */
  onChatChanged(callback) {
    return this.on(this.EVENT_TYPES.CHAT_CHANGED, callback);
  }

  /**
   * Registra um callback para o evento de nova mensagem
   * @param {Function} callback - Função a ser chamada quando uma nova mensagem for recebida
   * @returns {WhatsAppManager} - A instância atual para encadeamento
   */
  onNewMessage(callback) {
    return this.on(this.EVENT_TYPES.NEW_MESSAGE, callback);
  }

  /**
   * Registra um callback para o evento de mensagem enviada
   * @param {Function} callback - Função a ser chamada quando uma mensagem for enviada com sucesso
   * @returns {WhatsAppManager} - A instância atual para encadeamento
   */
  onMessageSent(callback) {
    return this.on(this.EVENT_TYPES.MESSAGE_SENT, callback);
  }

  /**
   * Registra um callback para o evento de erro ao enviar mensagem
   * @param {Function} callback - Função a ser chamada quando ocorrer um erro ao enviar mensagem
   * @returns {WhatsAppManager} - A instância atual para encadeamento
   */
  onMessageError(callback) {
    return this.on(this.EVENT_TYPES.MESSAGE_ERROR, callback);
  }

  /**
   * Registra um callback para um evento específico
   * @param {string} eventName - Nome do evento
   * @param {Function} callback - Função a ser chamada quando o evento ocorrer
   */
  on(eventName, callback) {
    if (!this.listeners[eventName]) {
      this.listeners[eventName] = [];
    }
    
    this.listeners[eventName].push(callback);
    return this; // Para permitir encadeamento
  }

  /**
   * Remove um callback de um evento específico
   * @param {string} eventName - Nome do evento
   * @param {Function} callback - Função a ser removida
   */
  off(eventName, callback) {
    if (!this.listeners[eventName]) {
      return this;
    }
    
    if (callback) {
      this.listeners[eventName] = this.listeners[eventName].filter(cb => cb !== callback);
    } else {
      // Se nenhum callback for fornecido, remove todos os listeners deste evento
      delete this.listeners[eventName];
    }
    
    return this;
  }

  /**
   * Dispara um evento com dados específicos
   * @param {string} eventName - Nome do evento
   * @param {any} data - Dados a serem passados para os callbacks
   */
  triggerEvent(eventName, data) {
    if (!this.listeners[eventName]) {
      return;
    }
    
    this.listeners[eventName].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Erro ao executar callback para evento ${eventName}:`, error);
      }
    });
  }

  /**
   * Retorna o chat ativo atual
   * @returns {Object|null} - O chat ativo ou null se nenhum chat estiver ativo
   */
  getActiveChat() {
    return this.activeChat;
  }

  /**
   * Verifica se um chat específico está ativo
   * @param {string} chatId - ID do chat para verificar
   * @returns {boolean} - true se o chat estiver ativo, false caso contrário
   */
  isChatActive(chatId) {
    if (!this.activeChat) {
      return false;
    }
    
    return this.activeChat.id._serialized === chatId;
  }

  /**
   * Obtém a lista de chats disponíveis
   * @returns {Promise<Array>} - Promise com a lista de chats
   */
  async getChats() {
    try {
      if (!window.WPP) {
        throw new Error('WPP não está disponível');
      }
      
      const chats = await window.WPP.chat.list();
      return chats;
    } catch (error) {
      console.error('Erro ao obter lista de chats:', error);
      throw error;
    }
  }
}

// Exportar a classe para uso em outros arquivos
if (typeof module !== 'undefined' && module.exports) {
  module.exports = WhatsAppManager;
} else {
  // Para uso no navegador
  window.WhatsAppManager = WhatsAppManager;
}
