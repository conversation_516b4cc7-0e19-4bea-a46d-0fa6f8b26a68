function ajusteTelefoneWhatsapp(telefone) {
  var tipo = libphonenumber.parsePhoneNumber('+' + telefone).getType();

  if( telefone.indexOf('8431131968') !== -1 ) {
    return telefone;
  }
  if( tipo !== 'FIXED_LINE' && telefone.length  < 13 && telefone.indexOf("55") === 0 ) {
    telefone = telefone.substr(0, 4) + '9' + telefone.substr(4);
  }

  return telefone;
}

// Script para verificar a inicialização do WPP no contexto da página
(function() {
  console.log('WPP Checker: Iniciando verificação no contexto da página...');

  // Variáveis para controle do estado do WhatsApp
  let whatsappCarregado = false;
  let numeroWhatsapp = '';
  let waId = '';
  let statusCheckInterval = null;

  // Função para encontrar o iframe do PromoKit
  function encontreFramePromokit() {
    try {
      // Sempre busca o iframe atual no DOM, sem usar cache
      const iframe = document.getElementById('whatsapp-support-iframe');
      if (!iframe) {
        console.warn('WPP Checker: Iframe do PromoKit não encontrado no DOM');
      }
      return iframe;
    } catch (e) {
      console.error('WPP Checker: Erro ao encontrar iframe PromoKit', e);
      return null;
    }
  }

  // Função para enviar mensagem para o PromoKit
  function envieParaPromokit(tipo, dados) {
    try {
      const frame = encontreFramePromokit();
      if (frame && frame.contentWindow) {
        frame.contentWindow.postMessage({
          tipo: tipo,
          ...dados
        }, '*');
        if( tipo !== 'STATUS_WHATSAPP' && tipo !== 'RESP_STATUS_WHATSAPP') {
          console.log(`WPP Checker: Mensagem ${tipo} enviada para o iframe do PromoKit`);
        }
      } else {
        console.warn(`WPP Checker: Não foi possível enviar mensagem ${tipo} - iframe não encontrado ou sem contentWindow`);
      }
    } catch (e) {
      console.error(`WPP Checker: Erro ao enviar mensagem ${tipo}`, e);
    }
  }

  // Configurar listener para mensagens do iframe pai
  window.addEventListener('message', function(e) {
    if (e.data && e.data.tipo === 'NOVA_MENSAGEM') {
      const mensagem = e.data.text;
      // Verificar se é uma solicitação para verificar se o WhatsApp carregou
      if (mensagem && mensagem.tipo === 'CARREGOU_WHATSAPP') {
        console.log('WPP Checker: Recebida solicitação para verificar se o WhatsApp carregou');
        // Verificar o status atual do WhatsApp
        const status = checkWhatsAppStatus();

        // Responder com o status atual do WhatsApp
        envieParaPromokit('RESP_CARREGOU_WHATSAPP', {
          carregou: status.carregou,
          pronto: status.pronto,
          conectado: status.conectado,
          numero: status.numero,
          waId: waId
        });

        // Enviar também o status completo para garantir
        envieParaPromokit('RESP_STATUS_WHATSAPP', {
          carregou: status.carregou,
          pronto: status.pronto,
          conectado: status.conectado,
          numero: status.numero
        });

        // Se ainda não estiver verificando periodicamente, iniciar verificação
        if (!statusCheckInterval) {
          startStatusCheck();
        }
      }

      // Verificar se é uma solicitação para verificar o status do WhatsApp
      if (mensagem && mensagem.tipo === 'STATUS_WHATSAPP') {
        // Verificar o status atual do WhatsApp
        const statusWhatsapp = checkWhatsAppStatus();

        // Responder com o status atual do WhatsApp
        envieParaPromokit('RESP_STATUS_WHATSAPP', {
          carregou: statusWhatsapp.carregou,
          pronto: statusWhatsapp.pronto,
          conectado: statusWhatsapp.conectado,
          numero: statusWhatsapp.numero
        });
      }

      // Nota: Outras mensagens serão processadas pelo message-handler.js
    }
  });

  // Função para iniciar verificação periódica do status
  function startStatusCheck() {
    console.log('WPP Checker: Iniciando verificação periódica do status');
    // Verificar o status a cada 2 segundos
    statusCheckInterval = setInterval(() => {
      const status = checkWhatsAppStatus();
      // Se o status mudou para conectado, notificar
      if (status.carregou && status.pronto && status.conectado) {
        //console.log('WPP Checker: WhatsApp está conectado!');
        // Enviar status atualizado
        envieParaPromokit('RESP_STATUS_WHATSAPP', {
          carregou: status.carregou,
          pronto: status.pronto,
          conectado: status.conectado,
          numero: status.numero
        });
      }
    }, 2000);
  }

  // Função para verificar o status do WhatsApp
  function checkWhatsAppStatus() {
    const status = {
      carregou: whatsappCarregado,
      pronto: false,
      conectado: false,
      numero: numeroWhatsapp
    };

    try {
      // Verificar se o WhatsApp está carregado
      if (window.WPP || window.WAPI) {
        status.carregou = true;
        whatsappCarregado = true;
      }

      // Verificar se o WAPI está disponível para usar os métodos solicitados
      if (window.WAPI && window.WPP) {
        try {
          // Verificar se o WPP está totalmente inicializado
          if (window.WPP.isFullReady && window.WPP.conn && window.WPP.conn.getMyUserId) {
            // Obter o número usando WAPI
            const numero = window.WAPI.obtenhaMeuNumero();
            if (numero) {
              numeroWhatsapp = numero;
              status.numero = numeroWhatsapp;
            }

            // Verificar se está logado usando WAPI
            status.pronto = WAPI.isLoggedIn();

            // Verificar se está conectado usando WAPI
            status.conectado = WAPI.isConnected();
          } else {
            console.log('WPP Checker: WPP não está totalmente inicializado ainda');
            // Usar métodos alternativos para verificar o status quando WPP não está totalmente pronto
            if (window.Store && window.Store.Conn) {
              status.pronto = true;
              status.conectado = window.Store.Conn.connected === true;

              // Tentar obter o número do WhatsApp
              const user = window.Store.Conn.wid || window.Store.Conn.me;
              if (user) {
                numeroWhatsapp = user.user;
                waId = user._serialized;
                status.numero = numeroWhatsapp;
              }
            }
          }
        } catch (err) {
          console.warn('WPP Checker: Erro ao usar métodos WAPI, usando fallback', err);
          // Fallback para métodos alternativos
          if (window.Store && window.Store.Conn) {
            status.pronto = true;
            status.conectado = window.Store.Conn.connected === true;

            // Tentar obter o número do WhatsApp
            const user = window.Store.Conn.wid || window.Store.Conn.me;
            if (user) {
              numeroWhatsapp = user.user;
              waId = user._serialized;
              status.numero = numeroWhatsapp;
            }
          }
        }
      }
      // Fallback para o método WPP se WAPI não estiver disponível
      else if (window.WPP && window.Store) {
        status.pronto = true;

        // Verificar se o WhatsApp está conectado
        if (window.Store.Conn) {
          status.conectado = window.Store.Conn.connected === true;

          // Tentar obter o número do WhatsApp
          const user = window.Store.Conn.wid || window.Store.Conn.me;
          if (user) {
            numeroWhatsapp = user.user;
            waId = user._serialized;
            status.numero = numeroWhatsapp;
          }
        }
      }

      // Verificação adicional para garantir que o WhatsApp está realmente conectado
      if (status.pronto && !status.conectado) {
        // Verificar se há outros indicadores de conexão
        if (window.Store && window.Store.Stream && window.Store.Stream.available) {
          status.conectado = true;
        }
      }

    } catch (err) {
      console.error('WPP Checker: Erro ao verificar status do WhatsApp', err);
    }

    return status;
  }

  // Função para verificar se o WPP está disponível
  function checkWPP() {
    console.log('WPP Checker: Verificando WPP: ' + (window.WPP ? 'DISPONÍVEL' : 'NÃO DISPONÍVEL'));

    if (window.WPP) {
      // Marcar que o WhatsApp carregou
      whatsappCarregado = true;
      window.carregouWhatsapp = true;

      // Tentar obter o número do WhatsApp
      if (window.Store && window.Store.Conn) {
        try {
          const user = window.Store.Conn.wid || window.Store.Conn.me;
          if (user) {
            numeroWhatsapp = user.user;
            waId = user._serialized;
            console.log('WPP Checker: Número do WhatsApp obtido:', numeroWhatsapp);
          }
        } catch (err) {
          console.error('WPP Checker: Erro ao obter número do WhatsApp', err);
        }
      }

      // Iniciar verificação periódica do status
      if (!statusCheckInterval) {
        startStatusCheck();
      }

      // Configurar listener para mudanças de chat
      setupChatChangeListener();

      // Notificar que o WPP está disponível
      window.dispatchEvent(new CustomEvent('wpp_initialized', {
        detail: {
          success: true,
          version: window.WPP.version || 'desconhecida'
        }
      }));

      // Verificar o status atual do WhatsApp
      const status = checkWhatsAppStatus();

      // Enviar mensagem para o iframe do PromoKit informando que o WhatsApp carregou
      envieParaPromokit('RESP_CARREGOU_WHATSAPP', {
        carregou: true,
        pronto: status.pronto,
        conectado: status.conectado,
        numero: numeroWhatsapp,
        waId: waId
      });

      // Enviar também o status completo para garantir
      envieParaPromokit('RESP_STATUS_WHATSAPP', {
        carregou: status.carregou,
        pronto: status.pronto,
        conectado: status.conectado,
        numero: status.numero
      });

      return true;
    }
    return false;
  }

  // Verificar imediatamente
  if (!checkWPP()) {
    // Se não estiver disponível, configurar um intervalo para verificar
    console.log('WPP Checker: WPP não disponível, configurando verificação periódica...');
    const wppCheckInterval = setInterval(() => {
      if (checkWPP()) {
        console.log('WPP Checker: WPP detectado com sucesso!');
        clearInterval(wppCheckInterval);
      }
    }, 1000);

    // Definir um timeout para parar de verificar após 30 segundos
    setTimeout(() => {
      clearInterval(wppCheckInterval);
      if (!window.WPP) {
        console.error('WPP Checker: Timeout - WPP não foi inicializado após 30 segundos');
        window.dispatchEvent(new CustomEvent('wpp_initialized', {
          detail: {
            success: false,
            error: 'timeout'
          }
        }));
      }
    }, 30000);
  }

  // Função para configurar o listener de mudança de chat
  function setupChatChangeListener() {
    if (!window.WPP) {
      console.error('WPP Checker: WPP não está disponível para configurar listener de chat');
      return false;
    }

    try {
      console.log('WPP Checker: Configurando listener para mudanças de chat...');
      window.WPP.on('chat.active_chat', (chat) => {
        if (!chat) return;

        console.log('WPP Checker: Chat ativo mudou para:', chat.id._serialized);

        // Verificar se é um grupo
        const ehGrupo = chat.isGroup;

        // Obter informações do contato/grupo
        let contatoAtual = '';
        let nomeContato = '';
        let url = '/admin/index';

        if (ehGrupo) {
          // Se for grupo, pegar o ID do grupo
          contatoAtual = chat.id.user;
          nomeContato = chat.formattedTitle || 'Grupo';
        } else {
          // Se for contato individual
          contatoAtual = chat.id.user;

          // Tentar obter o nome do contato
          if (chat.contact) {
            nomeContato = chat.contact.formattedName || chat.contact.pushname || '';
          }

          // Ajustar o telefone e criar a URL
          const telefone = ajusteTelefoneWhatsapp(contatoAtual);

          url = '/admin/contatos/iframe/' + encodeURIComponent('+' + telefone) + '?nome=' + encodeURIComponent(nomeContato);
        }

        // Enviar mensagem para o iframe do PromoKit
          /*
        envieParaPromokit('SELECIONOU_CONTATO', {
          chat: WAPI._serializeRawObj(chat),
          url: url
        });

           */
      });

      console.log('WPP Checker: Listener para mudanças de chat configurado com sucesso');
      return true;
    } catch (err) {
      console.error('WPP Checker: Erro ao configurar listener para mudanças de chat', err);
      return false;
    }
  }

  // Verificar se o WhatsApp já está carregado
  if (window.WPP) {
    console.log('WPP Checker: WPP já está disponível, verificando status...');
    const status = checkWhatsAppStatus();
    console.log('WPP Checker: Status inicial do WhatsApp:', status);

    // Configurar listener para mudanças de chat
    setupChatChangeListener();
  }
})();
