/**
 * EnhancedScriptManager - Gerenciador avançado de scripts para a extensão WhatsApp Suporte
 *
 * Este gerenciador implementa o padrão Singleton e fornece funcionalidades avançadas:
 * - Carregamento sequencial de scripts respeitando dependências
 * - Sistema de eventos para notificar quando scripts são carregados
 * - Verificação de integridade dos scripts
 * - Mecanismo de recuperação para scripts com falha
 * - Persistência de estado entre mudanças de URL do iframe
 */
class EnhancedScriptManager {
  constructor() {
    // Garante que apenas uma instância seja criada (Singleton)
    if (EnhancedScriptManager.instance) {
      return EnhancedScriptManager.instance;
    }

    EnhancedScriptManager.instance = this;

    // Mapa de scripts registrados e seus estados
    this.scripts = new Map();

    // Contador de tentativas de carregamento para cada script
    this.loadAttempts = new Map();

    // Sistema de eventos
    this.eventListeners = {
      SCRIPT_LOADED: [],
      ALL_SCRIPTS_LOADED: [],
      SCRIPT_ERROR: [],
      SCRIPT_RELOADED: []
    };

    // Flag para controlar se todos os scripts foram carregados
    this.allScriptsLoaded = false;

    // Máximo de tentativas de carregamento
    this.maxLoadAttempts = 3;

    // Configuração dos scripts da extensão
    this.initializeScriptRegistry();

    console.log('EnhancedScriptManager inicializado');
  }

  /**
   * Inicializa o registro de scripts com a configuração padrão
   */
  initializeScriptRegistry() {
    // Define a sequência de carregamento dos scripts com suas dependências
    const scriptConfigs = [
      {
        id: 'wppconnect',
        url: 'libs/wa-js/wppconnect-wa.js',
        removable: true,
        dependencies: []
      },
      {
        id: 'libphonenumber',
        url: 'libs/libphonenumber/libphonenumber-js.min.js',
        removable: false,
        dependencies: []
      },
      {
        id: 'libphonenumber-wrapper',
        url: 'libs/libphonenumber-wrapper.js',
        removable: false,
        dependencies: ['libphonenumber']
      },
      {
        id: 'wpp-checker',
        url: 'libs/wpp-checker.js',
        removable: false,
        dependencies: ['wppconnect']
      },
      {
        id: 'wpp-wapi',
        url: 'libs/wa-js/wpp-wapi.js',
        removable: false,
        dependencies: ['wppconnect']
      },
      {
        id: 'wpp-handler',
        url: 'libs/wa-js/wpp-handler.js',
        removable: false,
        dependencies: ['wppconnect', 'wpp-wapi']
      }
    ];

    // Registra cada script no gerenciador
    for (const config of scriptConfigs) {
      this.registerScript(
        config.id,
        config.url,
        {
          removable: config.removable,
          dependencies: config.dependencies
        }
      );
    }
  }

  /**
   * Registra um script no gerenciador
   * @param {string} id - Identificador único do script
   * @param {string} url - URL relativa do script
   * @param {Object} options - Opções adicionais
   * @param {boolean} options.removable - Se o script deve ser removido após carregamento
   * @param {Array<string>} options.dependencies - IDs de scripts que devem ser carregados antes
   * @param {Function} options.verifyFn - Função para verificar se o script está funcionando
   */
  registerScript(id, url, options = {}) {
    const scriptInfo = {
      id,
      url,
      status: 'NOT_LOADED', // NOT_LOADED, LOADING, LOADED, ERROR
      element: null,
      timestamp: null,
      removable: options.removable || false,
      dependencies: options.dependencies || [],
      verifyFn: options.verifyFn || null
    };

    this.scripts.set(id, scriptInfo);
    this.loadAttempts.set(id, 0);

    return this;
  }

  /**
   * Verifica se um script está carregado
   * @param {string} id - ID do script
   * @returns {boolean} - true se o script estiver carregado
   */
  isScriptLoaded(id) {
    const script = this.scripts.get(id);
    return script && script.status === 'LOADED';
  }

  /**
   * Verifica se todos os scripts estão carregados
   * @returns {boolean} - true se todos os scripts estiverem carregados
   */
  areAllScriptsLoaded() {
    for (const [id, script] of this.scripts.entries()) {
      if (script.status !== 'LOADED') {
        return false;
      }
    }
    return true;
  }

  /**
   * Carrega um script específico
   * @param {string} id - ID do script a ser carregado
   * @returns {Promise} - Promise resolvida quando o script for carregado
   */
  async loadScript(id) {
    const script = this.scripts.get(id);

    if (!script) {
      console.error(`Script ${id} não está registrado`);
      return Promise.reject(new Error(`Script ${id} não está registrado`));
    }

    // Se o script já estiver carregado, retorna imediatamente
    if (script.status === 'LOADED') {
      console.log(`Script ${id} já está carregado`);
      return Promise.resolve();
    }

    // Se o script estiver carregando, aguarda o carregamento
    if (script.status === 'LOADING') {
      console.log(`Script ${id} já está carregando, aguardando...`);
      return new Promise((resolve, reject) => {
        const checkLoaded = () => {
          if (script.status === 'LOADED') {
            resolve();
          } else if (script.status === 'ERROR') {
            reject(new Error(`Erro ao carregar script ${id}`));
          } else {
            setTimeout(checkLoaded, 100);
          }
        };
        checkLoaded();
      });
    }

    // Verifica se as dependências estão carregadas
    for (const depId of script.dependencies) {
      if (!this.isScriptLoaded(depId)) {
        console.log(`Carregando dependência ${depId} para o script ${id}`);
        try {
          await this.loadScript(depId);
        } catch (error) {
          console.error(`Erro ao carregar dependência ${depId} para o script ${id}:`, error);
          return Promise.reject(error);
        }
      }
    }

    // Atualiza o status para LOADING
    script.status = 'LOADING';

    // Incrementa o contador de tentativas
    const attempts = this.loadAttempts.get(id) + 1;
    this.loadAttempts.set(id, attempts);

    console.log(`Carregando script ${id} (tentativa ${attempts}/${this.maxLoadAttempts})`);

    return new Promise((resolve, reject) => {
      const scriptElement = document.createElement('script');
      scriptElement.src = chrome.runtime.getURL(script.url);

      scriptElement.onload = () => {
        script.status = 'LOADED';
        script.timestamp = Date.now();
        script.element = scriptElement;

        console.log(`Script ${id} carregado com sucesso`);

        // Remove o script do DOM se for removable
        if (script.removable) {
          scriptElement.remove();
          script.element = null;
        }

        // Dispara o evento SCRIPT_LOADED
        this.triggerEvent('SCRIPT_LOADED', { id, script });

        // Verifica se todos os scripts foram carregados
        if (this.areAllScriptsLoaded() && !this.allScriptsLoaded) {
          this.allScriptsLoaded = true;
          this.triggerEvent('ALL_SCRIPTS_LOADED', { scripts: Array.from(this.scripts.values()) });
        }

        resolve();
      };

      scriptElement.onerror = (error) => {
        script.status = 'ERROR';
        console.error(`Erro ao carregar script ${id}:`, error);

        // Dispara o evento SCRIPT_ERROR
        this.triggerEvent('SCRIPT_ERROR', { id, error });

        // Tenta recarregar o script se não excedeu o número máximo de tentativas
        if (attempts < this.maxLoadAttempts) {
          console.log(`Tentando recarregar script ${id} em 1 segundo...`);
          setTimeout(() => {
            this.loadScript(id)
              .then(resolve)
              .catch(reject);
          }, 1000);
        } else {
          reject(new Error(`Falha ao carregar script ${id} após ${attempts} tentativas`));
        }
      };

      (document.head || document.documentElement).appendChild(scriptElement);
    });
  }

  /**
   * Carrega todos os scripts registrados na ordem correta
   * @returns {Promise} - Promise resolvida quando todos os scripts forem carregados
   */
  async loadAllScripts() {
    console.log('Iniciando carregamento de todos os scripts...');

    // Obtém a lista de scripts ordenada por dependências
    const sortedScripts = this.getScriptsInDependencyOrder();

    // Carrega os scripts sequencialmente
    for (const id of sortedScripts) {
      try {
        await this.loadScript(id);
      } catch (error) {
        console.error(`Erro ao carregar script ${id}:`, error);
        // Continua carregando os próximos scripts mesmo se um falhar
      }
    }

    console.log('Carregamento de scripts concluído');
    return Promise.resolve();
  }

  /**
   * Obtém a lista de scripts ordenada por dependências
   * @returns {Array<string>} - Lista de IDs de scripts na ordem correta de carregamento
   */
  getScriptsInDependencyOrder() {
    const visited = new Set();
    const result = [];

    const visit = (id) => {
      if (visited.has(id)) return;

      visited.add(id);

      const script = this.scripts.get(id);
      if (!script) return;

      for (const depId of script.dependencies) {
        visit(depId);
      }

      result.push(id);
    };

    // Visita todos os scripts
    for (const [id] of this.scripts.entries()) {
      visit(id);
    }

    return result;
  }

  /**
   * Garante que um script específico esteja carregado
   * @param {string} id - ID do script
   * @returns {Promise} - Promise resolvida quando o script estiver carregado
   */
  async ensureScriptLoaded(id) {
    if (this.isScriptLoaded(id)) {
      return Promise.resolve();
    }

    return this.loadScript(id);
  }

  /**
   * Garante que todos os scripts estejam carregados
   * @returns {Promise} - Promise resolvida quando todos os scripts estiverem carregados
   */
  async ensureAllScriptsLoaded() {
    if (this.areAllScriptsLoaded()) {
      return Promise.resolve();
    }

    return this.loadAllScripts();
  }

  /**
   * Verifica a integridade de um script
   * @param {string} id - ID do script
   * @returns {boolean} - true se o script estiver íntegro
   */
  verifyScriptIntegrity(id) {
    const script = this.scripts.get(id);

    if (!script || script.status !== 'LOADED') {
      return false;
    }

    // Se houver uma função de verificação personalizada, usa ela
    if (typeof script.verifyFn === 'function') {
      return script.verifyFn();
    }

    // Verificações padrão baseadas no ID do script
    switch (id) {
      case 'wppconnect':
        return typeof window.WPP !== 'undefined';
      case 'wpp-wapi':
        return typeof window.WAPI !== 'undefined';
      case 'libphonenumber':
        return typeof window.libphonenumber !== 'undefined';
      default:
        // Para scripts sem verificação específica, assume que está ok se carregado
        return true;
    }
  }

  /**
   * Verifica a integridade de todos os scripts
   * @returns {Promise<Object>} - Promise com objeto contendo o status de integridade de cada script
   */
  async verifyAllScriptsIntegrity() {
    const result = {};

    for (const [id] of this.scripts.entries()) {
      result[id] = this.verifyScriptIntegrity(id);
    }

    return Promise.resolve(result);
  }

  /**
   * Recarrega um script específico
   * @param {string} id - ID do script
   * @returns {Promise} - Promise resolvida quando o script for recarregado
   */
  async reloadScript(id) {
    const script = this.scripts.get(id);

    if (!script) {
      return Promise.reject(new Error(`Script ${id} não está registrado`));
    }

    // Remove o script atual se existir
    if (script.element && script.element.parentNode) {
      script.element.parentNode.removeChild(script.element);
    }

    // Reseta o status e o contador de tentativas
    script.status = 'NOT_LOADED';
    script.element = null;
    this.loadAttempts.set(id, 0);

    // Dispara o evento SCRIPT_RELOADED
    this.triggerEvent('SCRIPT_RELOADED', { id });

    // Carrega o script novamente
    return this.loadScript(id);
  }

  /**
   * Recarrega todos os scripts com problemas de integridade
   * @returns {Promise} - Promise resolvida quando todos os scripts forem recarregados
   */
  async reloadScriptsWithIntegrityIssues() {
    const integrityStatus = await this.verifyAllScriptsIntegrity();
    const scriptsToReload = [];

    for (const [id, isIntegrous] of Object.entries(integrityStatus)) {
      if (!isIntegrous) {
        scriptsToReload.push(id);
      }
    }

    if (scriptsToReload.length === 0) {
      console.log('Todos os scripts estão íntegros');
      return Promise.resolve();
    }

    console.log(`Recarregando scripts com problemas de integridade: ${scriptsToReload.join(', ')}`);

    // Recarrega os scripts na ordem correta
    const sortedScriptsToReload = this.getScriptsInDependencyOrder()
      .filter(id => scriptsToReload.includes(id));

    for (const id of sortedScriptsToReload) {
      try {
        await this.reloadScript(id);
      } catch (error) {
        console.error(`Erro ao recarregar script ${id}:`, error);
      }
    }

    return Promise.resolve();
  }

  /**
   * Limpa todos os scripts carregados
   */
  clearLoadedScripts() {
    for (const [id, script] of this.scripts.entries()) {
      if (script.element && script.element.parentNode) {
        script.element.parentNode.removeChild(script.element);
      }

      script.status = 'NOT_LOADED';
      script.element = null;
      this.loadAttempts.set(id, 0);
    }

    this.allScriptsLoaded = false;
    console.log('Todos os scripts foram limpos');
  }

  /**
   * Registra um listener para um evento
   * @param {string} eventName - Nome do evento
   * @param {Function} callback - Função a ser chamada quando o evento ocorrer
   */
  on(eventName, callback) {
    if (!this.eventListeners[eventName]) {
      this.eventListeners[eventName] = [];
    }

    this.eventListeners[eventName].push(callback);
    return this;
  }

  /**
   * Remove um listener de um evento
   * @param {string} eventName - Nome do evento
   * @param {Function} callback - Função a ser removida
   */
  off(eventName, callback) {
    if (!this.eventListeners[eventName]) {
      return this;
    }

    if (callback) {
      this.eventListeners[eventName] = this.eventListeners[eventName].filter(cb => cb !== callback);
    } else {
      this.eventListeners[eventName] = [];
    }

    return this;
  }

  /**
   * Dispara um evento
   * @param {string} eventName - Nome do evento
   * @param {any} data - Dados a serem passados para os callbacks
   */
  triggerEvent(eventName, data) {
    if (!this.eventListeners[eventName]) {
      return;
    }

    for (const callback of this.eventListeners[eventName]) {
      try {
        callback(data);
      } catch (error) {
        console.error(`Erro ao executar callback para evento ${eventName}:`, error);
      }
    }
  }

  /**
   * Obtém o status de um script
   * @param {string} id - ID do script
   * @returns {Object|null} - Informações sobre o script ou null se não existir
   */
  getScriptStatus(id) {
    const script = this.scripts.get(id);

    if (!script) {
      return null;
    }

    return {
      id: script.id,
      url: script.url,
      status: script.status,
      timestamp: script.timestamp,
      dependencies: script.dependencies,
      isIntegrous: this.verifyScriptIntegrity(id),
      loadAttempts: this.loadAttempts.get(id)
    };
  }

  /**
   * Obtém o status de todos os scripts
   * @returns {Array<Object>} - Lista com o status de todos os scripts
   */
  getAllScriptsStatus() {
    const result = [];

    for (const [id] of this.scripts.entries()) {
      result.push(this.getScriptStatus(id));
    }

    return result;
  }

  /**
   * Registra um callback para quando um script for carregado
   * @param {string} id - ID do script
   * @param {Function} callback - Função a ser chamada quando o script for carregado
   */
  onScriptLoaded(id, callback) {
    return this.on('SCRIPT_LOADED', (data) => {
      if (data.id === id) {
        callback(data);
      }
    });
  }

  /**
   * Registra um callback para quando todos os scripts forem carregados
   * @param {Function} callback - Função a ser chamada quando todos os scripts forem carregados
   */
  onAllScriptsLoaded(callback) {
    return this.on('ALL_SCRIPTS_LOADED', callback);
  }

  /**
   * Registra um callback para quando ocorrer um erro no carregamento de um script
   * @param {string} id - ID do script
   * @param {Function} callback - Função a ser chamada quando ocorrer um erro
   */
  onScriptError(id, callback) {
    return this.on('SCRIPT_ERROR', (data) => {
      if (data.id === id) {
        callback(data);
      }
    });
  }
}

// Exporta a classe para uso em outros arquivos
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedScriptManager;
} else {
  // Para uso no navegador
  window.EnhancedScriptManager = EnhancedScriptManager;
}

// Cria a instância global do gerenciador
window.scriptManager = new EnhancedScriptManager();
