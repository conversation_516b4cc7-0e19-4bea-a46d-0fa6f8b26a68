// Script para o popup da extensão WhatsApp Suporte

document.addEventListener('DOMContentLoaded', function() {
    // Elementos da interface
    const statusIndicator = document.getElementById('status-indicator');
    const statusMessage = document.getElementById('status-message');
    const openWhatsAppButton = document.getElementById('open-whatsapp');
    const settingsButton = document.getElementById('settings-button');
    const messagesCount = document.getElementById('messages-count');
    const clientsCount = document.getElementById('clients-count');
    const pendingCount = document.getElementById('pending-count');
    const findPromoKitButton = document.getElementById('find-promokit');
    
    // Verifica o estado atual da extensão
    checkExtensionStatus();
    
    // Adiciona eventos aos botões
    openWhatsAppButton.addEventListener('click', openWhatsAppWeb);
    settingsButton.addEventListener('click', openSettings);
    
    // Adiciona evento ao botão de busca de abas PromoKit (se existir)
    if (findPromoKitButton) {
        findPromoKitButton.addEventListener('click', openPromoKitFinder);
    }
    
    // Verifica o status da extensão
    function checkExtensionStatus() {
        chrome.storage.local.get(['isActive', 'unreadMessages', 'lastSync'], function(data) {
            if (data.isActive) {
                // Extensão está ativa e conectada ao WhatsApp Web
                statusIndicator.classList.remove('status-inactive');
                statusIndicator.classList.add('status-active');
                statusMessage.textContent = 'Conectado ao WhatsApp Web';
            } else {
                // Extensão não está conectada
                statusIndicator.classList.remove('status-active');
                statusIndicator.classList.add('status-inactive');
                statusMessage.textContent = 'Aguardando conexão com WhatsApp Web...';
            }
            
            // Atualiza contadores
            updateCounters();
        });
    }
    
    // Abre o WhatsApp Web em uma nova aba
    function openWhatsAppWeb() {
        chrome.tabs.create({ url: 'https://web.whatsapp.com/' });
        window.close(); // Fecha o popup
    }
    
    // Abre a página de configurações da extensão
    function openSettings() {
        chrome.runtime.openOptionsPage();
        // Alternativa se a página de opções não estiver definida:
        // chrome.tabs.create({ url: 'settings.html' });
        window.close(); // Fecha o popup
    }
    
    // Abre a página de busca de abas PromoKit
    function openPromoKitFinder() {
        window.location.href = 'promokit-finder.html';
    }
    
    // Atualiza os contadores de estatísticas
    function updateCounters() {
        // Busca dados do armazenamento local
        chrome.storage.local.get(['unreadMessages', 'clientCount', 'pendingCount'], function(data) {
            // Atualiza os contadores na interface
            messagesCount.textContent = data.unreadMessages || 0;
            clientsCount.textContent = data.clientCount || 0;
            pendingCount.textContent = data.pendingCount || 0;
        });
        
        // Verifica se há uma aba do WhatsApp Web aberta
        chrome.tabs.query({ url: 'https://web.whatsapp.com/*' }, function(tabs) {
            if (tabs.length > 0) {
                // WhatsApp Web está aberto
                statusIndicator.classList.remove('status-inactive');
                statusIndicator.classList.add('status-active');
                statusMessage.textContent = 'Conectado ao WhatsApp Web';
                
                // Envia mensagem para verificar o status real da conexão
                chrome.tabs.sendMessage(tabs[0].id, { action: 'CHECK_STATUS' }, function(response) {
                    if (chrome.runtime.lastError || !response) {
                        // Erro na comunicação ou sem resposta
                        console.log('Erro ao verificar status:', chrome.runtime.lastError);
                        return;
                    }
                    
                    // Atualiza o status com base na resposta
                    if (response.connected) {
                        statusMessage.textContent = 'Conectado ao WhatsApp Web';
                    } else {
                        statusMessage.textContent = 'WhatsApp Web aberto, mas não conectado';
                    }
                });
            }
        });
    }
    
    // Verifica o status a cada 5 segundos
    setInterval(checkExtensionStatus, 5000);
});
